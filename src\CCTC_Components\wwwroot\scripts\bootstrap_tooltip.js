export function addTooltip(containerId) {
    let ele = document.getElementById(containerId);
    if(ele) {
        return new bootstrap.Tooltip(ele);
    }
}

export function disposeTooltip(containerId) {
    $(`#${containerId}`).tooltip('dispose');
}

export function enableTooltip(containerId) {
    $(`#${containerId}`).tooltip('enable');
}

export function disableTooltip(containerId) {
    $(`#${containerId}`).tooltip('disable');
}

export function updateTooltipTitle(containerId, newTitle, delay = 0, enable = true) {
    setTimeout(function () {
        $(`#${containerId}`).attr('data-bs-original-title', newTitle);
        if (enable) {
            enableTooltip(containerId);
        }
        else {
            disableTooltip(containerId);
        }
    }, delay);
}
