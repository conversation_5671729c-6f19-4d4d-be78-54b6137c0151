import { ICustomWorld } from '../support/custom-world';
import { When } from '@cucumber/cucumber';

/**
 * A step to press the keyboard arrow keys. For example:
 * the user presses the up arrow key once
 * the user presses the down arrow key 3 times
 * It may be necessary to focus on an element before calling this step so that it can receive keyboard events
 * @param {string} keyDirection - The key direction
 * @param {number} times - The number of times to press the key (optional)
 */
When(
  /^the user presses the (up|down|left|right) arrow key (\d+)?(?: times|once)$/,
  async function (this: ICustomWorld, keyDirection: string, times?: number) {
    const numKeyPresses = times ?? 1;
    const page = this.page!;
    for (let i = 0; i < numKeyPresses; i++) {
      switch (keyDirection) {
        case 'up':
          await page.keyboard.press('ArrowUp');
          break;
        case 'down':
          await page.keyboard.press('ArrowDown');
          break;
        case 'left':
          await page.keyboard.press('ArrowLeft');
          break;
        case 'right':
          await page.keyboard.press('ArrowRight');
          break;
      }
      // Small delay between key presses to ensure proper event handling
      if (i < numKeyPresses - 1) {
        await page.waitForTimeout(50);
      }
    }
  }
);

/**
 * A step to press the configured keyboard keys. For example:
 * the user presses the escape key once
 * the user presses the backspace key 3 times
 * It may be necessary to focus on an element before calling this step so that it can receive keyboard events
 * @param {string} keyType - The key type
 * @param {number} times - The number of times to press the key (optional)
 */
When(
  /^the user presses the (escape|enter|backspace) key (\d+)?(?: times|once)$/,
  async function (this: ICustomWorld, keyType, times?: number) {
    const numKeyPresses = times ?? 1;
    const page = this.page!;
    for (let i = 0; i < numKeyPresses; i++) {
      switch (keyType) {
        case 'escape':
          await page.keyboard.press('Escape');
          break;
        case 'enter':
          await page.keyboard.press('Enter');
          break;
        case 'backspace':
          await page.keyboard.press('Backspace');
          break;
      }
      // Small delay between key presses to ensure proper event handling
      if (i < numKeyPresses - 1) {
        await page.waitForTimeout(50);
      }
    }
  }
);
