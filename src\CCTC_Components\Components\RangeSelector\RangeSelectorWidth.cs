namespace CCTC_Components.Components.RangeSelector
{
    /// <summary>
    /// Defines the width/height options for the RangeSelector component
    /// </summary>
    public enum RangeSelectorWidth
    {
        /// <summary>
        /// Automatic sizing based on container
        /// </summary>
        Auto,
        /// <summary>
        /// Small size (200px for horizontal, 200px height for vertical)
        /// </summary>
        Small,
        /// <summary>
        /// Medium size (400px for horizontal, 300px height for vertical)
        /// </summary>
        Medium,
        /// <summary>
        /// Large size (600px for horizontal, 400px height for vertical)
        /// </summary>
        Large,
        /// <summary>
        /// Full size (100% for horizontal, 500px height for vertical)
        /// </summary>
        Full
    }
}


// I think here I will need to make a different file that is similar for height.
// I think it is fine to have a few set sizes, we dont need to infinite choice in range selector sizes.