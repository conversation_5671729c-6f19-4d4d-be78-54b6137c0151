import { ICustomWorld } from '../support/custom-world';
import { compareToBaseImage } from '../utils/compareImages';
import * as Helpers from '../support/helper-functions';
import { When, Then } from '@cucumber/cucumber';
import { expect } from '@playwright/test';

When(
  'the user selects the {string} Panel menu header',
  async function (this: ICustomWorld, menuHeader: string) {
    await Helpers.getSamplerTabsSelectedContent(this).locator('.panelmenu-header').getByText(menuHeader).click();
  }
);

When(
  'the user selects the {string} Panel menu item',
  async function (this: ICustomWorld, menuItem: string) {
    await Helpers.getSamplerTabsSelectedContent(this).locator('cctc-panelmenuitem').getByText(menuItem, { exact: true }).click();
  }
);

Then(
  'there is a panel menu item called {string}',
  async function (this: ICustomWorld, menuItem: string) {
    await Helpers.getSamplerTabsSelectedContent(this).locator('cctc-panelmenuitem').getByText(menuItem, { exact: true }).click();
  }
);

Then(
  'there is a panel menu header called {string}',
  async function (this: ICustomWorld, menuHeader: string) {
    const header = Helpers.getSamplerTabsSelectedContent(this).locator('.panelmenu-header-title', { hasText: menuHeader });
    await expect(header.first()).toBeVisible();
  }
);

When(
  'the user clicks the panel menu expand and collapse bar',
  async function (this: ICustomWorld) {
    await Helpers.getSamplerTabsSelectedContent(this).locator('.panelmenu-collapse').click();
  }
);

Then(
  'the panel menu matches the base image {string}',
  async function (this: ICustomWorld, name: string) {
    const screenshot = await Helpers.tryGetStableScreenshot(
      Helpers.getSamplerTabsSelectedContent(this).locator('cctc-panelmenu')
    );
    await compareToBaseImage(this, name, screenshot);
  }
);

Then(
  'the panel menu item {string} is selected',
  async function (this: ICustomWorld, menuItem: string) {
    const item = Helpers.getSamplerTabsSelectedContent(this).locator('cctc-panelmenuitem').getByText(menuItem, { exact: true });
    await expect(item.locator('.panelmenu-item')).toHaveClass(/panelmenu-item-in-scope/);
  }
);

Then(
  'the panel menu item {string} is not selected',
  async function (this: ICustomWorld, menuItem: string) {
    const item = Helpers.getSamplerTabsSelectedContent(this).locator('cctc-panelmenuitem').getByText(menuItem, { exact: true });
    await expect(item.locator('.panelmenu-item')).toHaveClass(/panelmenu-item-not-in-scope/);
  }
);

Then(
  'the element below the panel menu shows {string} as selected',
  async function (this: ICustomWorld, menuItem: string) {
    const selectedText = Helpers.getSamplerTabsSelectedContent(this).getByText(`selected: ${menuItem}`, { exact: true });
    await expect(selectedText).toBeVisible();
  }
);

Then(
  'the selected panel menu text matches the base image {string}',
  async function (this: ICustomWorld, name: string) {
    const selectedTextElement = this.page!.getByText(/selected:/, { exact: false });
    await expect(selectedTextElement).toBeVisible({ timeout: 5000 });
    const screenshot = await Helpers.tryGetStableScreenshot(selectedTextElement);
    await compareToBaseImage(this, name, screenshot);
  }
);

Then(
  /^the panel menu header "([^"]*)" is (open|closed)$/,
  async function (this: ICustomWorld, menuHeader: string, state: string) {
    const header = Helpers.getSamplerTabsSelectedContent(this).locator('.panelmenu-header', { hasText: menuHeader });
    const icon = header.locator('.panelmenu-header-expandcollapse-icon');
    await expect(icon).toHaveText(state === 'open' ? 'expand_less' : 'expand_more');
  }
);

Then(
  /^the panel menu expand and collapse arrows point (left|right)$/,
  async function (this: ICustomWorld, direction: string) {
    const collapseIcon = Helpers.getSamplerTabsSelectedContent(this)
      .locator('.panelmenu-collapse .material-icons');
    await expect(collapseIcon).toHaveText(
      direction === 'left' ? 'keyboard_double_arrow_left' : 'keyboard_double_arrow_right'
    );
  }
);
// can add up or down cases to this ^ if needed

Then(
  'the text {string} is not visible in the panel menu',
  async function (this: ICustomWorld, text: string) {
    const panelMenu = Helpers.getSamplerTabsSelectedContent(this)
      .locator('cctc-panelmenu');
    const textElement = panelMenu.getByText(text, { exact: true });
    await expect(textElement).not.toBeVisible();
  }
);

Then(
  'the panel menu item with Id {string} has a tooltip enabled containing the title {string}',
  async function (this: ICustomWorld, id: string, title: string) {
    const tooltip = Helpers.getSamplerTabsSelectedContent(this)
      .locator(`cctc-panelmenuitem[id="${id}"] cctc-tooltip`);
    await expect(tooltip).toHaveAttribute('data-bs-original-title', title);
  }
);

// unused so far below
//
//
//
//
//

Then(
  'the Panel menu header matches the base image {string}',
  async function (this: ICustomWorld, name: string) {
    const screenshot = await Helpers.tryGetStableScreenshot(
      Helpers.getSamplerTabsSelectedContent(this).locator('.panelmenu-header')
    );
    await compareToBaseImage(this, name, screenshot);
  }
);

Then(
  'the panel menu item matches the base image {string}',
  async function (this: ICustomWorld, name: string) {
    const screenshot = await Helpers.tryGetStableScreenshot(
      Helpers.getSamplerTabsSelectedContent(this).locator('cctc-panelmenuitem')
    );
    await compareToBaseImage(this, name, screenshot);
  }
);

Then(
  'the panel menu item with Id {string} has no tooltip enabled',
  async function (this: ICustomWorld, id: string) {
    const panelMenuItem = Helpers.getSamplerTabsSelectedContent(this)
      .locator(`cctc-panelmenuitem[id="${id}"]`);
    await expect(panelMenuItem).toBeAttached();
    await expect(panelMenuItem.locator('cctc-tooltip')).toHaveCount(0);
  }
);

When(
  'the user selects the {string} Panel menu item in the {string} header',
  async function (this: ICustomWorld, menuItem: string, menuHeader: string) {
    await Helpers.getSamplerTabsSelectedContent(this).locator('.panelmenu-header').getByText(menuHeader).click();
    await Helpers.getSamplerTabsSelectedContent(this).locator('cctc-panelmenuitem').getByText(menuItem, { exact: true }).click();
  }
);
// not used the above but maybe this would be better in some places