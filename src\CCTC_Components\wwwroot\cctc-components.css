﻿
/*
    states should be restricted to one of the following;

    normal (could be implicit so could be omitted)
    active
    inactive
    highlight
    disabled
    readonly
    focus
    hover
    selected
    pressed
    dragging
    dropping

    {prefix}{component name(alphanumeric no spaces)}{component subpart (optional)}{state (optional)}{css property}
 */

/*
    add all custom elements here
*/
cctc-tooltip,
cctc-timeout,
cctc-progress,
cctc-switch,
cctc-animatedplaceholder,
cctc-pill, cctc-pill-content-container,
cctc-button,
cctc-concertina, cctc-concertina-header, cctc-concertina-subheader,
cctc-concertinaitem, cctc-concertinaitem-header, cctc-concertinaitem-subheader, cctc-concertinaitem-content,
cctc-datatextbox, cctc-datatextbox-input, cctc-datatextbox-dropdown, cctc-datatextbox-selected,
cctc-infotext,
cctc-infoicon, cctc-infoicon-image-container,
cctc-lister, cctc-lister-filter-header, cctc-lister-items-container, cctc-lister-display-counts, cctc-lister-item-row, cctc-lister-items-header,
cctc-confirmmodal, cctc-confirmmodal-container, cctc-confirmmodal-header, cctc-confirmmodal-responses,
cctc-panelmenu, cctc-panelmenuheader, cctc-panelmenuitem,
cctc-steps, cctc-steps-controls, cctc-steps-headers, cctc-steps-current-content, cctc-progressstep,
cctc-circle,
cctc-tabs, cctc-tabs-headers, cctc-tabs-selected-content, cctc-tabitem, cctc-tabitem-header,
cctc-input, cctc-input-dropdown-selected, cctc-input-dropdown, cctc-input-dropdown-options, cctc-input-dropdown-option,
cctc-chart,
cctc-rangeselector
{
    display: block;
}

:root {
    /* general variables for use across components ----------------------------------------------*/
    --cctc-color: white;
    --cctc-success-color: var(--bs-success);
    --cctc-info-color: var(--bs-info);
    --cctc-warning-color: var(--bs-warning);
    --cctc-danger-color: var(--bs-danger);
    --cctc-inactive-color: #A0A0A0;
    --cctc-highlight-color: #C0C0C0;
    --cctc-hover-color: #C0C0C0;
    --cctc-background-color: #505050;
    --cctc-border-radius: 0px;
    --cctc-border-color: var(--cctc-color);
    --cctc-border-width: 1px;
    --cctc-border-style: solid;
    --cctc-hover-background-color: #686868;
    --cctc-active-background-color: #686868;
    --cctc-selected-background-color: #404040;
    --cctc-highlight-background-color: #767676;
    --cctc-highlight-border-color: #A0A0A0;
    --cctc-icon-color: var(--cctc-color);
    --cctc-icon-active-color: var(--cctc-highlight-color);
    --cctc-icon-hover-color: var(--cctc-hover-color);
    --cctc-disabled-color: #808080;
    --cctc-disabled-background-color: #cbcbcb;
    --cctc-link-color: cyan;
    --cctc-code-color: #d7ff47;
    --cctc-emphasis: #ffc700;
    --cctc-webkit-line-clamp: 3;

    /* component specific variables ----------------------------------------------*/

    /* tabs */
    --cctc-tabs-color: var(--cctc-color);
    --cctc-tabs-background-color: var(--cctc-background-color);
    --cctc-tabs-header-color: var(--cctc-inactive-color);
    --cctc-tabs-header-selected-color: var(--cctc-color);
    --cctc-tabs-header-background-color: var(--cctc-background-color);
    --cctc-tabs-header-selected-background-color: var(--cctc-selected-background-color);
    --cctc-tabs-header-highlight-color: var(--cctc-highlight-color);

    /* concertina */
    --cctc-concertina-icon-color: var(--cctc-icon-color);
    --cctc-concertina-icon-hover-color: var(--cctc-icon-hover-color);
    --cctc-concertina-header-color: var(--cctc-color);
    --cctc-concertina-header-background-color: var(--cctc-background-color);
    --cctc-concertina-subheader-color: var(--cctc-highlight-color);
    --cctc-concertina-subheader-background-color: var(--cctc-background-color);

    /* concertinaitem */
    --cctc-concertinaitem-icon-color: var(--cctc-icon-color);
    --cctc-concertinaitem-icon-hover-color: var(--cctc-icon-hover-color);
    --cctc-concertinaitem-header-color: var(--cctc-color);
    --cctc-concertinaitem-header-background-color: var(--cctc-background-color);
    --cctc-concertinaitem-subheader-color: var(--cctc-highlight-color);
    --cctc-concertinaitem-subheader-background-color: var(--cctc-background-color);

    /* panelmenu */
    --cctc-panelmenu-background-color: var(--cctc-background-color);
    --cctc-panelmenu-hover-background-color: var(--cctc-hover-background-color);
    --cctc-panelmenu-active-background-color: var(--cctc-active-background-color);
    --cctc-panelmenu-highlight-border-color: var(--cctc-highlight-border-color);
    --cctc-panelmenu-border-radius: var(--cctc-border-radius);
    --cctc-panelmenu-border-color: var(--cctc-background-color);
    --cctc-panelmenu-highlight-color: var(--cctc-highlight-color);

    /* lister */
    --cctc-lister-color: var(--cctc-color);
    --cctc-lister-background-color: var(--cctc-background-color);
    --cctc-lister-hover-background-color: var(--cctc-hover-background-color);
    --cctc-lister-highlight-background-color: var(--cctc-highlight-background-color);
    --cctc-lister-highlight-color: var(--cctc-highlight-color);
    --cctc-lister-working-template-color: var(--cctc-highlight-color);
    --cctc-lister-filter-header-color: var(--cctc-color);
    --cctc-lister-filter-header-background-color: var(--cctc-background-color);
    --cctc-lister-items-header-color: var(--cctc-inactive-color);
    --cctc-lister-items-header-background-color: var(--cctc-selected-background-color);
    --cctc-lister-display-counts-color: var(--cctc-color);
    --cctc-lister-display-counts-background-color: var(--cctc-background-color);

    /*
        input - covers Text, TextArea, Date, Time, DateAndTime, Numeric, Dropdown, Radio, CheckBox
    */
    --cctc-input-color: var(--cctc-color);
    --cctc-input-background-color: var(--cctc-background-color);
    --cctc-input-icon-color: var(--cctc-highlight-color);
    --cctc-input-icon-hover-color: var(--cctc-color);
    --cctc-input-readonly-icon-color: var(--cctc-readonly-icon-color);
    --cctc-input-height: 1.8rem;
    --cctc-input-padding-left: 0.5rem;
    --cctc-input-padding-right: 0.5rem;
    --cctc-input-border-radius: var(--cctc-border-radius);
    --cctc-input-border-color: var(--cctc-border-color);
    --cctc-input-border-width: var(--cctc-border-width);
    --cctc-input-border-style: var(--cctc-border-style);
    --cctc-input-disabled-color: var(--cctc-disabled-color);
    --cctc-input-disabled-background-color: var(--cctc-disabled-background-color);
    --cctc-input-webkit-line-clamp: var(--cctc-webkit-line-clamp);

    /* datatextbox */
    --cctc-datatextbox-input-color: var(--cctc-color);
    --cctc-datatextbox-input-background-color: var(--cctc-background-color);
    --cctc-datatextbox-input-border-color: var(--cctc-color);
    --cctc-datatextbox-input-border-style: var(--cctc-border-style);
    --cctc-datatextbox-input-border-width: var(--cctc-border-width);
    --cctc-datatextbox-input-border-top-left-radius: 3px;
    --cctc-datatextbox-input-border-top-right-radius: 3px;
    --cctc-datatextbox-input-border-bottom-right-radius: 0;
    --cctc-datatextbox-input-border-bottom-left-radius: 0;
    --cctc-datatextbox-dropdown-color: var(--cctc-color);
    --cctc-datatextbox-dropdown-border-color: var(--cctc-color);
    --cctc-datatextbox-dropdown-background-color: var(--cctc-active-background-color);
    --cctc-datatextbox-dropdown-height: 20rem;
    --cctc-datatextbox-icon-active-color: var(--cctc-icon-active-color);
    --cctc-datatextbox-icon-hover-color: var(--cctc-icon-hover-color);

    /* steps */
    --cctc-steps-color: var(--cctc-color);
    --cctc-steps-background-color: var(--cctc-background-color);
    --cctc-steps-icon-active-color: var(--cctc-icon-active-color);
    --cctc-steps-icon-hover-color: var(--cctc-icon-hover-color);
    --cctc-steps-icon-disabled-color: var(--cctc-disabled-color);
    --cctc-steps-header-color: var(--cctc-selected-background-color);
    --cctc-steps-header-background-color: var(--cctc-background-color);
    --cctc-steps-header-inactive-color: var(--cctc-inactive-color);
    --cctc-steps-header-inactive-background-color: var(--cctc-background-color);

    /* infotext */
    --cctc-infotext-icon-color: var(--cctc-icon-color);
    --cctc-infotext-icon-hover-color: var(--cctc-hover-color);
    --cctc-infotext-popover-color: var(--cctc-color);
    --cctc-infotext-popover-background-color: var(--cctc-highlight-background-color);
    --cctc-infotext-webkit-line-clamp: var(--cctc-webkit-line-clamp);

    /*infoicon*/
    --cctc-infoicon-image-container-background-color: var(--cctc-background-color);
    --cctc-infoicon-image-container-border-radius: var(--cctc-border-radius);
    --cctc-infoicon-image-border-radius: var(--cctc-border-radius);
    --cctc-infoicon-popover-color: var(--cctc-color);
    --cctc-infoicon-popover-header-color: var(--cctc-color);
    --cctc-infoicon-popover-background-color: var(--cctc-highlight-background-color);
    --cctc-infoicon-popover-header-background-color: var(--cctc-active-background-color);

    /*animatedplaceholder*/
    --cctc-animatedplaceholder-background-color: rgb(205, 127, 50);
    --cctc-animatedplaceholder-background-image: linear-gradient(to left, rgb(218, 160, 109, .05), rgb(218, 160, 109, .3), rgb(218, 160, 109, .6), rgb(218, 160, 109, .3), rgb(218, 160, 109, .05));
    --cctc-animatedplaceholder-border-radius: var(--cctc-border-radius);

    /*progress*/
    --cctc-progress-accent-color: var(--cctc-highlight-color);
    --cctc-progress-height: 1rem;

    /*pill*/
    --cctc-pill-color: var(--cctc-color);
    --cctc-pill-background-color: var(--cctc-highlight-background-color);
    --cctc-pill-border-color: var(--cctc-border-color);
    --cctc-pill-success-color: var(--cctc-color);
    --cctc-pill-success-border-color: var(--cctc-success-color);
    --cctc-pill-success-background-color: var(--cctc-success-color);
    --cctc-pill-info-color: var(--cctc-color);
    --cctc-pill-info-background-color: var(--cctc-info-color);
    --cctc-pill-info-border-color: var(--cctc-info-color);
    --cctc-pill-warning-color: var(--cctc-color);
    --cctc-pill-warning-background-color: var(--cctc-warning-color);
    --cctc-pill-warning-border-color: var(--cctc-warning-color);
    --cctc-pill-danger-color: var(--cctc-color);
    --cctc-pill-danger-background-color: var(--cctc-danger-color);
    --cctc-pill-danger-border-color: var(--cctc-danger-color);

    /*confirmmodal*/
    --cctc-confirmmodal-color: var(--cctc-color);
    --cctc-confirmmodal-background-color: var(--cctc-background-color);
    --cctc-confirmmodal-border-color: var(--cctc-color);
    --cctc-confirmmodal-header-color: var(--cctc-warning-color);
    --cctc-confirmmodal-response-color: var(--cctc-info-color);
    --cctc-confirmmodal-response-border-color: var(--cctc-info-color);
    --cctc-confirmmodal-response-hover-color: var(--cctc-color);
    --cctc-confirmmodal-response-hover-border-color: var(--cctc-color);
    --cctc-confirmmodal-border-radius: var(--cctc-border-radius);

    /*
        button - covers Button, RefreshButton
    */
    --cctc-button-color: var(--cctc-color);
    --cctc-button-hover-color: var(--cctc-hover-color);
    --cctc-button-icon-color: var(--cctc-icon-color);
    --cctc-button-icon-hover-color: var(--cctc-icon-hover-color);
    --cctc-button-x-padding: 0.75rem;
    --cctc-button-y-padding: 0.25rem;
    --cctc-button-border-radius: var(--cctc-border-radius);
    --cctc-button-border-color: var(--cctc-border-color);
    --cctc-button-border-hover-color: var(--cctc-hover-color);
    --cctc-button-border-width: var(--cctc-border-width);
    --cctc-button-border-style: var(--cctc-border-style);

    /*switch*/
    --cctc-switch-color: var(--cctc-color);
    --cctc-switch-disabled-color: var(--cctc-disabled-color);
    --cctc-switch-background-color: var(--cctc-color);
    --cctc-switch-disabled-background-color: var(--cctc-disabled-background-color);
    --cctc-switch-active-hover-box-shadow: 0 0 0 1px var(--cctc-highlight-background-color);
    --cctc-switch-inactive-hover-box-shadow: 0 0 0 1px var(--cctc-highlight-color);
    --cctc-switch-slider-active-background-color: var(--cctc-highlight-color);
    --cctc-switch-slider-inactive-background-color: var(--cctc-highlight-background-color);
    --cctc-switch-slider-disabled-background-color: var(--cctc-highlight-background-color);

    /*rangeselector*/
    --cctc-rangeselector-track-height: 10px;
    --cctc-rangeselector-track-background-color: #e0e0e0;
    --cctc-rangeselector-track-border-radius: 3px;
    --cctc-rangeselector-fill-background-color: #007bff;
    --cctc-rangeselector-thumb-size: 20px;
    --cctc-rangeselector-thumb-background-color: #007bff;
    --cctc-rangeselector-thumb-border: 2px solid white;
    --cctc-rangeselector-thumb-border-radius: 50%;
    --cctc-rangeselector-thumb-hover-box-shadow: 0 0 0 8px rgba(0, 123, 255, 0.2);
    --cctc-rangeselector-thumb-dragging-box-shadow: 0 0 0 12px rgba(0, 123, 255, 0.3);
    --cctc-rangeselector-thumb-focus-box-shadow: 0 0 0 8px rgba(0, 123, 255, 0.3);
    --cctc-rangeselector-thumb-focus-outline-color: #007bff;
    --cctc-rangeselector-labels-font-size: 12px;
    --cctc-rangeselector-labels-color: #666;
    --cctc-rangeselector-disabled-opacity: 0.5;
}

input[type="checkbox"], input[type="radio"] {
    transform: scale(1.5);
}

.material-icons {
    font-size: 0.875rem;
}

*:focus {
    outline: 0;
}