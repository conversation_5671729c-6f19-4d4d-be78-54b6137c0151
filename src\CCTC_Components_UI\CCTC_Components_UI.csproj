<?xml version="1.0" encoding="utf-8"?>
<Project Sdk="Microsoft.NET.Sdk.BlazorWebAssembly">
  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
  </PropertyGroup>
	<ItemGroup>
      <ProjectReference Include="..\CCTC_Components\CCTC_Components.csproj" />
	</ItemGroup>
	<ItemGroup>
	  <PackageReference Include="MudBlazor" Version="8.8.0" />
	</ItemGroup>
  <Target Name="BuildCssDev" Condition="'$(Configuration)' == 'Debug'" AfterTargets="Build">
    <Exec Command="sass ./wwwroot/css/custom.scss ./wwwroot/css/custom.css" />
  </Target>
  <Target Name="BuildCssProd" Condition="'$(Configuration)' == 'Release'" BeforeTargets="Build">
    <Exec Command="sass ./wwwroot/css/custom.scss ./wwwroot/css/custom.css" />
  </Target>

  <Import Project="..\..\.paket\Paket.Restore.targets" />
</Project>