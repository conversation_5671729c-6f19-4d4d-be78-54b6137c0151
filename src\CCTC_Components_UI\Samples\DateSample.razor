﻿@page "/datesample"

@{
    var description = new List<string>
    {
        "A component for date input. Support Reactive input and format validation"
    };

    var features = new List<(string, string)>
    {
        ("Interaction", "Can be made read-only and / or disabled. The read-only icon is optional"),
        ("Constrained input", "Apply a date format (see <code>Lib.Common.Services.Clock.availableDateFormats</code> for the supported formats), set a min and / or max date"),
        ("Binding", "The component throttle speed can be changed"),
        ("Feedback", "Provides configurable feedback on the inputted date"),
        ("Clear value", "Allows the current value to be cleared from the UI (depends on the data type - see gotchas). Not enabled by default"),
    };

    var gotchas = new List<(string, string)>
    {
        ("ThrottleMs", "500 ms is the minimum <code>ThrottleMs</code> and will be applied automatically unless a higher value is provided"),
        ("AllowClear", "Can clear only if TValue is of type nullable dateonly"),
        ("--cctc-input-webkit-line-clamp", "CSS variable not used by this component")
    };

    var tips = new List<(string, string)>
    {
        ("Target the cctc-input component(s) on a page ignoring any child cctc-input components. For example, setting a uniform input component width adjusting according to screen size",
@"
<code>
    <pre>

    ::deep cctc-input:not(cctc-input cctc-input) {
        width: 100%;
    }

    @media (min-width: 1200px) {
        ::deep cctc-input:not(cctc-input cctc-input) {
            width: 35%;
        }
    }
    </pre>
</code>")
    };

    var usageCode = new List<(string title, string description, string code, RenderFragment fragment)>
    {
        ("Format: default, FeedbackIcon.Error", "",
@"<Date
    Id=""usage1""
    TValue=""DateOnly""
    Value=""@DateValue""
    ThrottleMs=""@Constants.DefaultThrottleMs""
    ValueChanged=""@(args => DateChanged(args, ""yyyy-MM-dd""))""
    DateChanged=""@(args => Console.WriteLine(args.Value))""
    FeedbackIcon=""FeedbackIcon.Error"">
</Date>

code {

    DateOnly DateValue { get; set; } = new DateOnly(2022, 5, 6);

    void DateChanged(DateOnly? newValue, string format)
    {
        DateValue = newValue;
        Console.WriteLine($""Date new value is: {newValue.ToString(format)}"");
    }
}",
        @<Date
            Id="usage1"
            TValue="DateOnly"
            Value="@DateValue"
            ThrottleMs="@Constants.DefaultThrottleMs"
            ValueChanged="@(args => DateChanged(args, "yyyy-MM-dd"))"
            DateChanged="@(args => Console.WriteLine(args.Value))"
            FeedbackIcon="FeedbackIcon.Error">
        </Date>),
        ("Format: dd/MM/yy, FeedbackIcon.None, CssClass applied", "",
@"<Date
    Id=""usage2""
    @bind-Value=""@DateValue""
    CssClass=""red-border""
    ThrottleMs=""@Constants.DefaultThrottleMs""
    DateFormat=""dd/MM/yy""
    DateChanged=""@(args => Console.WriteLine(args.Value))""
    FeedbackIcon=""FeedbackIcon.None"">
</Date>

@code {

    DateOnly DateValue { get; set; } = new DateOnly(2022, 5, 6);
}",
        @<Date
            Id="usage2"
            @bind-Value="@DateValue"
            CssClass="red-border"
            ThrottleMs="@Constants.DefaultThrottleMs"
            DateFormat="dd/MM/yy"
            DateChanged="@(args => Console.WriteLine(args.Value))"
            FeedbackIcon="FeedbackIcon.None">
        </Date>),
        ("Format: dd/MM/yyyy, Min (11/12/2023) and Max (16/12/2023), FeedbackIcon.Both", "",
@"<Date
    Id=""usage3""
    @bind-Value=""@DateValue""
    ThrottleMs=""@Constants.DefaultThrottleMs""
    DateFormat=""dd/MM/yyyy""
    MinDate=""@(new DateOnly(2023,12,11))""
    MaxDate=""@(new DateOnly(2023,12,16))""
    DateChanged=""@(args => Console.WriteLine(args.Value))""
    FeedbackIcon=""FeedbackIcon.Both"">
</Date>

@code {

    DateOnly DateValue { get; set; } = new DateOnly(2022, 5, 6);
}",
        @<Date
            Id="usage3"
            @bind-Value="@DateValue"
            ThrottleMs="@Constants.DefaultThrottleMs"
            DateFormat="dd/MM/yyyy"
            MinDate="@(new DateOnly(2023,12,11))"
            MaxDate="@(new DateOnly(2023,12,16))"
            DateChanged="@(args => Console.WriteLine(args.Value))"
            FeedbackIcon="FeedbackIcon.Both">
        </Date>),
        ("Format: dd MMM yy, FeedbackIcon.Valid", "",
@"<Date
    Id=""usage4""
    @bind-Value=""@DateValue""
    ThrottleMs=""@Constants.DefaultThrottleMs""
    DateFormat=""dd MMM yy""
    DateChanged=""@(args => Console.WriteLine(args.Value))""
    FeedbackIcon=""FeedbackIcon.Valid"">
</Date>

@code {

    DateOnly DateValue { get; set; } = new DateOnly(2022, 5, 6);
}",
        @<Date
            Id="usage4"
            @bind-Value="@DateValue"
            ThrottleMs="@Constants.DefaultThrottleMs"
            DateFormat="dd MMM yy"
            DateChanged="@(args => Console.WriteLine(args.Value))"
            FeedbackIcon="FeedbackIcon.Valid">
        </Date>),
        ("Format: dd MMM yyyy", "",
@"<Date
    Id=""usage5""
    @bind-Value=""@DateValue""
    ThrottleMs=""@Constants.DefaultThrottleMs""
    DateFormat=""dd MMM yyyy""
    DateChanged=""@(args => Console.WriteLine(args.Value))"">
</Date>

@code {

    DateOnly DateValue { get; set; } = new DateOnly(2022, 5, 6);
}",
        @<Date
            Id="usage5"
            @bind-Value="@DateValue"
            ThrottleMs="@Constants.DefaultThrottleMs"
            DateFormat="dd MMM yyyy"
            DateChanged="@(args => Console.WriteLine(args.Value))">
        </Date>),
        ("Format: dd MMMM yyyy", "",
@"<Date
    Id=""usage6""
    @bind-Value=""@DateValue""
    ThrottleMs=""@Constants.DefaultThrottleMs""
    DateFormat=""dd MMMM yyyy""
    DateChanged=""@(args => Console.WriteLine(args.Value))"">
</Date>

@code {

    DateOnly DateValue { get; set; } = new DateOnly(2022, 5, 6);
}",
        @<Date
            Id="usage6"
            @bind-Value="@DateValue"
            ThrottleMs="@Constants.DefaultThrottleMs"
            DateFormat="dd MMMM yyyy"
            DateChanged="@(args => Console.WriteLine(args.Value))">
        </Date>),
        ("Format: yyyy-MM-dd, null initial value", "",
@"<Date
    Name=""Usage7""
    Id=""usage7""
    @bind-Value=""@DateValueNullable""
    ThrottleMs=""@Constants.DefaultThrottleMs""
    DateFormat=""yyyy-MM-dd""
    DateChanged=""@(args => Console.WriteLine(args.Value))"">
</Date>

@code {

    DateOnly? DateValueNullable { get; set; }
}",
        @<Date
            Id="usage7"
            @bind-Value="@DateValueNullable"
            ThrottleMs="@Constants.DefaultThrottleMs"
            DateFormat="yyyy-MM-dd"
            DateChanged="@(args => Console.WriteLine(args.Value))">
        </Date>),
        ("Format: yyyy-MM-dd, read-only", "",
@"<Date
    Id=""usage8""
    @bind-Value=""@DateValue""
    ThrottleMs=""@Constants.DefaultThrottleMs""
    DateFormat=""yyyy-MM-dd""
    DateChanged=""@(args => Console.WriteLine(args.Value))""
    ReadOnly=""true"">
</Date>

@code {

    DateOnly DateValue { get; set; } = new DateOnly(2022, 5, 6);
}",
        @<Date
            Id="usage8"
            @bind-Value="@DateValue"
            ThrottleMs="@Constants.DefaultThrottleMs"
            DateFormat="yyyy-MM-dd"
            DateChanged="@(args => Console.WriteLine(args.Value))"
            ReadOnly="true">
        </Date>),
        ("Format: yyyy-MM-dd, read-only and disabled", "",
@"<Date
    Id=""usage9""
    @bind-Value=""@DateValue""
    ThrottleMs=""@Constants.DefaultThrottleMs""
    DateFormat=""yyyy-MM-dd""
    DateChanged=""@(args => Console.WriteLine(args.Value))""
    ReadOnly=""true""
    Disabled=""true"">
</Date>

@code {

    DateOnly DateValue { get; set; } = new DateOnly(2022, 5, 6);
}",
        @<Date
            Id="usage9"
            @bind-Value="@DateValue"
            ThrottleMs="@Constants.DefaultThrottleMs"
            DateFormat="yyyy-MM-dd"
            DateChanged="@(args => Console.WriteLine(args.Value))"
            ReadOnly="true"
            Disabled="true">
        </Date>),
        ("Format: yyyy-MM-dd, read-only, disabled and read-only icon hidden", "",
@"<Date
    Id=""usage10""
    @bind-Value=""@DateValue""
    ThrottleMs=""@Constants.DefaultThrottleMs""
    DateFormat=""yyyy-MM-dd""
    DateChanged=""@(args => Console.WriteLine(args.Value))""
    ReadOnly=""true""
    Disabled=""true""
    HideReadOnlyIcon=""true"">
</Date>

@code {

    DateOnly DateValue { get; set; } = new DateOnly(2022, 5, 6);
}",
        @<Date
            Id="usage10"
            @bind-Value="@DateValue"
            ThrottleMs="@Constants.DefaultThrottleMs"
            DateFormat="yyyy-MM-dd"
            DateChanged="@(args => Console.WriteLine(args.Value))"
            ReadOnly="true"
            Disabled="true"
            HideReadOnlyIcon="true">
        </Date>),
        ("Format: yyyy-MM-dd, read-only and read-only icon hidden", "",
@"<Date
    Id=""usage11""
    @bind-Value=""@DateValue""
    ThrottleMs=""@Constants.DefaultThrottleMs""
    DateFormat=""yyyy-MM-dd""
    DateChanged=""@(args => Console.WriteLine(args.Value))""
    ReadOnly=""true""
    HideReadOnlyIcon=""true"">
</Date>

@code {

    DateOnly DateValue { get; set; } = new DateOnly(2022, 5, 6);
}",
        @<Date
            Id="usage11"
            @bind-Value="@DateValue"
            ThrottleMs="@Constants.DefaultThrottleMs"
            DateFormat="yyyy-MM-dd"
            DateChanged="@(args => Console.WriteLine(args.Value))"
            ReadOnly="true"
            HideReadOnlyIcon="true">
        </Date>),
        ("Format: yyyy-MM-dd, disabled", "",
@"<Date
    Id=""usage12""
    @bind-Value=""@DateValue""
    ThrottleMs=""@Constants.DefaultThrottleMs""
    DateFormat=""yyyy-MM-dd""
    DateChanged=""@(args => Console.WriteLine(args.Value))""
    Disabled=""true"">
</Date>

@code {

    DateOnly DateValue { get; set; } = new DateOnly(2022, 5, 6);
}",
        @<Date
            Id="usage12"
            @bind-Value="@DateValue"
            ThrottleMs="@Constants.DefaultThrottleMs"
            DateFormat="yyyy-MM-dd"
            DateChanged="@(args => Console.WriteLine(args.Value))"
            Disabled="true">
        </Date>),
        ("Format: default, allow clear, FeedbackIcon.Error", "",
@"<Date
    Id=""usage13""
    @bind-Value=""@DateValueNullable2""
    ThrottleMs=""@Constants.DefaultThrottleMs""
    DateChanged=""@(args => Console.WriteLine(args.Value ?? ""value cleared""))""
    AllowClear=""true""
    FeedbackIcon=""FeedbackIcon.Error"">
</Date>

code {

    DateOnly? DateValueNullable2 { get; set; } = new DateOnly(2023, 1, 4);
}",
        @<Date
            Id="usage13"
            @bind-Value="@DateValueNullable2"
            ThrottleMs="@Constants.DefaultThrottleMs"
            DateChanged="@(args => Console.WriteLine(args.Value ?? "value cleared"))"
            AllowClear="true"
            FeedbackIcon="FeedbackIcon.Error">
        </Date>),
        ("Format: default, allow clear, null initial value, FeedbackIcon.Error", "",
@"<Date
    Id=""usage14""
    @bind-Value=""@DateValueNullable""
    ThrottleMs=""@Constants.DefaultThrottleMs""
    DateChanged=""@(args => Console.WriteLine(args.Value ?? ""value cleared""))""
    AllowClear=""true""
    FeedbackIcon=""FeedbackIcon.Error"">
</Date>

code {

    DateOnly? DateValueNullable { get; set; }
}",
        @<Date
            Id="usage14"
            @bind-Value="@DateValueNullable"
            ThrottleMs="@Constants.DefaultThrottleMs"
            DateChanged="@(args => Console.WriteLine(args.Value ?? "value cleared"))"
            AllowClear="true"
            FeedbackIcon="FeedbackIcon.Error">
        </Date>)
    };
}

@* The wrapper div is necessary here for ::deep isolated css rules to be applied to the Date component *@
<div>
    <Sampler
        ComponentName="Date"
        QualifiedComponentName="Temporal.Date"
        ComponentCssName="input"
        ComponentTypeName="date"
        Description="@description"
        Features="@features"
        UsageText="Typical usages of the <code>Date</code> component are shown below"
        UsageCodeList="@usageCode"
        Gotchas="@gotchas"
        Tips="@tips"
        ContentHeightPixels="450">
        <ExampleTemplate>
            <Date
                Id="example1"
                @bind-Value="@DateValue"
                ThrottleMs="@Constants.DefaultThrottleMs"
                DateChanged="@(args => Console.WriteLine(args.Value))"
                FeedbackIcon="FeedbackIcon.Error">
            </Date>
        </ExampleTemplate>
    </Sampler>
</div>

@code {

    DateOnly DateValue { get; set; } = new DateOnly(2022, 5, 6);

    DateOnly? DateValueNullable { get; set; }

    DateOnly? DateValueNullable2 { get; set; } = new DateOnly(2023, 1, 4);

    void DateChanged(DateOnly newValue, string format)
    {
        DateValue = newValue;
        Console.WriteLine($"Date new value is: {newValue.ToString(format)}");
    }
}
