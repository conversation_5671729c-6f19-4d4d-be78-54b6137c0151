@component @panelmenu @panelmenu_2
Feature: the panel menu headers can contain one or more child item
    <PERSON><PERSON><PERSON>: the panel menu header contains one child item
        Given the user is at the home page
        And the user selects the "Panel menu" component in the container "Panel menu" 
        And there is a panel menu header called "header 1"
        And the panel menu header "header 1" is closed
        When the user selects the "header 1" Panel menu header
        And the panel menu header "header 1" is open
        Then there is a panel menu item called "panel item 1A"
        And the panel menu matches the base image "1 items under 1 header"

    Scenario: the panel menu header contains two child items
        Given the user is at the home page
        And the user selects the "Panel menu" component in the container "Panel menu"
        And there is a panel menu header called "header 2"
        And the panel menu header "header 2" is closed
        When the user selects the "header 2" Panel menu header
        And the panel menu header "header 2" is open
        Then there is a panel menu item called "panel item 2A"
        And there is a panel menu item called "panel item 2B"
        And the panel menu matches the base image "2 items under 1 header"