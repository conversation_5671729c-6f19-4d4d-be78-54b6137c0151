cctc-rangeselector {
    display: block;
    width: 100%;
    margin: 20px 0;
    user-select: none;
}

/*  need to check i cannot use more generic colouring from elsewhere in the above  */

.cctc-rangeselector-track {
    position: relative;
    height: var(--cctc-rangeselector-track-height);
    background-color: var(--cctc-rangeselector-track-background-color);
    border-radius: var(--cctc-rangeselector-track-border-radius);
    margin: 20px 0;
}

.cctc-rangeselector-fill {
    position: absolute;
    height: 100%;
    background-color: var(--cctc-rangeselector-fill-background-color);
    border-radius: var(--cctc-rangeselector-track-border-radius);
    transition: none;
}

.cctc-rangeselector-thumb {
    position: absolute;
    width: var(--cctc-rangeselector-thumb-size);
    height: var(--cctc-rangeselector-thumb-size);
    background-color: var(--cctc-rangeselector-thumb-background-color);
    border: var(--cctc-rangeselector-thumb-border);
    border-radius: var(--cctc-rangeselector-thumb-border-radius);
    top: -7px;
    margin-left: -10px;
    cursor: grab;
    transition: box-shadow 0.2s ease, transform 0.2s ease;
    z-index: 2;
    
    /* Enable keyboard focus */
    outline: 2px solid transparent;
    outline-offset: 2px;
}

.cctc-rangeselector-thumb:active,
.cctc-rangeselector-thumb.cctc-rangeselector-dragging {
    cursor: grabbing;
}

.cctc-rangeselector-thumb:hover {
    box-shadow: var(--cctc-rangeselector-thumb-hover-box-shadow);
    transform: scale(1.1);
}

.cctc-rangeselector-thumb.cctc-rangeselector-dragging {
    box-shadow: var(--cctc-rangeselector-thumb-dragging-box-shadow);
    transform: scale(1.2);
    z-index: 10;
}

.cctc-rangeselector-thumb-lower {
    z-index: 3;
}

.cctc-rangeselector-thumb-upper {
    z-index: 3;
}

.cctc-rangeselector-labels {
    display: flex;
    justify-content: space-between;
    font-size: var(--cctc-rangeselector-labels-font-size);
    color: var(--cctc-rangeselector-labels-color);
    margin-top: 5px;
}

.cctc-rangeselector-thumb:focus {
    outline: 2px solid var(--cctc-rangeselector-thumb-focus-outline-color) !important;
    outline-offset: 3px;
    box-shadow: var(--cctc-rangeselector-thumb-focus-box-shadow);
    transform: scale(1.1);
}

cctc-rangeselector .component-wrapper.disabled .cctc-rangeselector-thumb {
    cursor: not-allowed;
    opacity: var(--cctc-rangeselector-disabled-opacity);
    pointer-events: none;
}

cctc-rangeselector .component-wrapper.disabled .cctc-rangeselector-thumb:hover {
    box-shadow: none;
    transform: none;
}

cctc-rangeselector .component-wrapper.disabled .cctc-rangeselector-thumb:focus {
    outline: none !important;
    box-shadow: none !important;
    transform: none !important;
}

cctc-rangeselector .component-wrapper.disabled .cctc-rangeselector-track {
    cursor: not-allowed;
    pointer-events: none;
}

cctc-rangeselector .component-wrapper.disabled .cctc-rangeselector-fill {
    opacity: var(--cctc-rangeselector-disabled-opacity);
}

.component-wrapper {
    width: 100%;
    padding: 0 22px; /* Provide space for thumb (10px) + max shadow (12px) extending beyond track edges */
}

/* Width variants */
.component-wrapper.width-small {
    width: 200px;
}

.component-wrapper.width-medium {
    width: 400px;
}

.component-wrapper.width-large {
    width: 600px;
}

.component-wrapper.width-full {
    width: 100%;
}

/* Vertical orientation styles */
.component-wrapper.vertical {
    display: flex;
    flex-direction: row;
    align-items: center;
    height: 300px;
    width: auto;
    padding: 22px 0; /* Provide space for thumb (10px) + max shadow (12px) extending beyond track edges */
}

.component-wrapper.vertical .cctc-rangeselector-track {
    width: 10px;
    height: 100%;
    margin: 0 20px;
}

.component-wrapper.vertical .cctc-rangeselector-fill {
    width: 100%;
    height: auto;
}

.component-wrapper.vertical .cctc-rangeselector-thumb {
    top: auto;
    left: -7px;
    margin-left: 0;
    margin-bottom: -10px;
}

.component-wrapper.vertical .cctc-rangeselector-labels {
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
    margin-top: 0;
    margin-left: 10px;
}

/* Vertical width variants */
.component-wrapper.vertical.width-small {
    height: 200px;
    width: auto;
    padding: 22px 0; /* Ensure consistent padding for all vertical variants */
}

.component-wrapper.vertical.width-medium {
    height: 300px;
    width: auto;
    padding: 22px 0; /* Ensure consistent padding for all vertical variants */
}

.component-wrapper.vertical.width-large {
    height: 400px;
    width: auto;
    padding: 22px 0; /* Ensure consistent padding for all vertical variants */
}

.component-wrapper.vertical.width-full {
    height: 500px;
    width: auto;
    padding: 22px 0; /* Ensure consistent padding for all vertical variants */
}

.cctc-rangeselector-labels-hidden {
    display: none;
}