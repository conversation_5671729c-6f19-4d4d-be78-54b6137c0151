@component @rangeselector @rangeselector_7
Feature: the range selector disabled state prevents interaction
    <PERSON><PERSON><PERSON>: the disabled range selector displays correct styling and prevents interaction
        Given the user is at the home page
        And the user selects the "Range selector" component in the container "Input"
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "Disabled State"
        Then the range selector is disabled
        And the range selector appears disabled
        And the range selector component image matches the base image "disabled range selector"

        # Unsure which disabled checking definition is better- both work

    Scenario: the disabled range selector does not respond to mouse interaction
        Given the user is at the home page
        And the user selects the "Range selector" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Disabled State"
        And the range selector is disabled
        When the user attempts to drag the disabled lower thumb
        Then the disabled lower thumb does not move