﻿@page "/buttonsample"

@{
    var description = new List<string>
    {
        "The Button component"
    };

    var features = new List<(string, string)>
    {
        ("Configuration", "Customise the button icon and text. The button icon can be placed on the left or right. The button can be disabled"),
        ("Interactivity", "A callback can be associated with user button click")
    };

    var usageCode = new List<(string title, string description, string code, RenderFragment fragment)>
    {
        ("ButtonIcon: circle, IconPosition: Left, styled with border", "",
@"<Button
    Id=""usage1""
    ButtonIcon=""circle""
    ButtonText=""@ShortButtonText""
    IconPosition=""PositionX.Left""
    StyledWithBorder=""true""
    OnClick=""@(() => Console.WriteLine(""click""))"">
</Button>

@code {

    string ShortButtonText { get; set; } = ""some text"";
}",
        @<Button
            Id="usage1"
            ButtonIcon="circle"
            ButtonText="@ShortButtonText"
            IconPosition="PositionX.Left"
            StyledWithBorder="true"
            OnClick="@(() => Console.WriteLine("click"))">
        </Button>),
        ("ButtonIcon: light_mode, IconPosition: Right, overridden icon color via Style", "",
@"<Button
    Id=""usage2""
    Style=""--cctc-button-icon-color: var(--cctc-warning-color);""
    ButtonIcon=""light_mode""
    ButtonText=""@ShortButtonText""
    IconPosition=""PositionX.Right""
    OnClick=""@(() => Console.WriteLine(""click""))"">
</Button>

@code {
    
    string ShortButtonText { get; set; } = ""some text"";
}",
        @<Button
            Id="usage2"
            Style="--cctc-button-icon-color: var(--cctc-warning-color);"
            ButtonIcon="light_mode"
            ButtonText="@ShortButtonText"
            IconPosition="PositionX.Right"
            OnClick="@(() => Console.WriteLine("click"))">
        </Button>),
        ("ButtonIcon: circle, IconPosition: Left, ButtonText: long text to demonstrate wrapping, CssClass: restricted-width", "",
@"<Button
    Id=""usage3""
    CssClass=""restricted-width""
    ButtonIcon=""circle""
    ButtonText=""@LongButtonText""
    IconPosition=""PositionX.Left""
    OnClick=""@(() => Console.WriteLine(""click""))"">
</Button>

@code {

    string LongButtonText { get; set; } = ""Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat."";
}",
        @<Button
            Id="usage3"
            CssClass="restricted-width"
            ButtonIcon="circle"
            ButtonText="@LongButtonText"
            IconPosition="PositionX.Left"
            OnClick="@(() => Console.WriteLine("click"))">
        </Button>),
        ("ButtonIcon: no icon", "",
@"<Button
    Id=""usage4""
    ButtonText=""@ShortButtonText""
    OnClick=""@(() => Console.WriteLine(""click""))"">
</Button>

@code {

    string ShortButtonText { get; set; } = ""some text"";
}",
        @<Button
            Id="usage4"
            ButtonText="@ShortButtonText"
            OnClick="@(() => Console.WriteLine("click"))">
        </Button>),
        ("ButtonIcon: circle, ButtonText: no text", "",
@"<Button
    Id=""usage5""
    ButtonIcon=""circle""
    OnClick=""@(() => Console.WriteLine(""click""))"">
</Button>

@code {

}",
        @<Button
            Id="usage5"
            ButtonIcon="circle"
            OnClick="@(() => Console.WriteLine("click"))">
        </Button>),
        ("ButtonIcon: circle, IconPosition: Left, Disabled", "",
@"<Button
    Id=""usage6""
    ButtonIcon=""circle""
    ButtonText=""@ShortButtonText""
    IconPosition=""PositionX.Left""
    OnClick=""@(() => Console.WriteLine(""click""))""
    Disabled=""true"">
</Button>

@code {

    string ShortButtonText { get; set; } = ""some text"";
}",
    @<Button
        Id="usage6"
        ButtonIcon="circle"
        ButtonText="@ShortButtonText"
        IconPosition="PositionX.Left"
        OnClick="@(() => Console.WriteLine("click"))"
        Disabled="true">
    </Button>)
    };
}

@* The wrapper div is necessary here for ::deep isolated css rules to be applied to the Button component *@
<div>
    <Sampler
        ComponentName="Button"
        QualifiedComponentName="Buttons.Button"
        ComponentCssName="button"
        ComponentTypeName="button"
        Description="@description"
        Features="@features"
        UsageText="Typical usages of the <code>Button</code> component are shown below"
        UsageCodeList="@usageCode"
        ContentHeightPixels="425">
        <ExampleTemplate>
            <Button
                Id="example1"
                ButtonIcon="circle"
                ButtonText="some text"
                IconPosition="PositionX.Left"
                StyledWithBorder="true"
                OnClick="@(() => _counter++)">
            </Button>
            <p class="mt-3">Counter: @_counter</p>
        </ExampleTemplate>
    </Sampler>
</div>

@code {

    int _counter;

    string ShortButtonText { get; set; } = "some text";

    string LongButtonText { get; set; } = "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.";
}