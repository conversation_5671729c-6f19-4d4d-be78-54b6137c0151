namespace CCTC_Components.Components.RangeSelector
{
    /// <summary>
    /// Defines the orientation options for the RangeSelector component
    /// </summary>
    public enum RangeSelectorOrientation
    {
        /// <summary>
        /// Horizontal orientation (default) - slider extends left to right
        /// </summary>
        Horizontal,
        
        /// <summary>
        /// Vertical orientation - slider extends bottom to top
        /// </summary>
        Vertical
    }
}