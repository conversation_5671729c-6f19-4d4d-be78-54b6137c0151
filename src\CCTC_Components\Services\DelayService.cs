﻿using CCTC_Lib.Contracts.Interaction;

namespace CCTC_Components.Services;

/// <summary>
/// Provides a delay that can be mocked in unit tests
/// </summary>
public class DelayService : IDelayService
{
    /// <summary>
    /// Delay for given TimeSpan
    /// </summary>
    /// <param name="timeSpan">The delay</param>
    public async Task Delay(TimeSpan timeSpan)
    {
        await Task.Delay(timeSpan);
    }

    /// <summary>
    /// Delay for given TimeSpan with cancellation
    /// </summary>
    /// <param name="timeSpan">The delay</param>
    /// <param name="token">The cancellation token</param>
    public async Task Delay(TimeSpan timeSpan, CancellationToken token)
    {
        await Task.Delay(timeSpan, token);
    }
}