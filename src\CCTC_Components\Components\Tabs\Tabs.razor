﻿@using System.Diagnostics
@inherits CCTC_Components.Components.__CCTC.CCTCBase

<CascadingValue Value="this">
    <cctc-tabs id="@Id" class="@CssClass" style="@Style" data-author="cctc">
        <div id="@Id-tabs-container">
            <div class="@_containerClass">
                <cctc-tabs-headers id="@Id-tabs-headers" class="@_tabsHeadersClass" style="@_tabsHeadersStyle">
                    @ChildContent
                </cctc-tabs-headers>
                <cctc-tabs-selected-content id="@Id-tabs-selected-content" class="tabs-selected-content"
                                            style="@_tabsSelectedContentStyle">
                    @_selectedTabItemContent
                </cctc-tabs-selected-content>
            </div>
        </div>
    </cctc-tabs>
</CascadingValue>

@code {

    /// <summary>
    /// The content for the tabs
    /// </summary>
    [Parameter, EditorRequired]
    public required RenderFragment ChildContent { get; set; }

    /// <summary>
    /// The tab id that should be selected on initialization
    /// </summary>
    /// <remarks>If not given, the first tabitem is automatically selected</remarks>
    [Parameter]
    public string? PreSelectTabId { get; set; }

    /// <summary>
    /// The size of the tab item content. Provides a default of 100.
    /// </summary>
    /// <remarks>The Tabs component is designed to be a fixed height and therefore the content of the tab items can scroll depending
    /// on size. This requires the tab item to have a set height</remarks>
    [Parameter]
    public int ContentHeightPixels { get; set; } = 100;

    /// <summary>
    /// A callback invoked when a <see cref="TabItem"/> is selected
    /// </summary>
    [Parameter]
    public EventCallback<TabItem> OnTabItemSelected { get; set; }

    /// <summary>
    /// Sets the placement of the headers
    /// </summary>
    [Parameter]
    public TabHeaderPlacement TabHeaderPlacement { get; set; } = TabHeaderPlacement.Top;

    string? _selectedTabItemId;
    RenderFragment? _selectedTabItemContent;

    /// <summary>
    /// Displays the appropriate content when a <see cref="TabItem"/> is selected
    /// </summary>
    /// <param name="tabItem">The selected tab item</param>
    public async Task TabItemSelected(TabItem tabItem)
    {
        _selectedTabItemId = tabItem.Id;
        _selectedTabItemContent = tabItem.ChildContent;
        await OnTabItemSelected.InvokeAsync(tabItem);
        StateHasChanged();
    }

    /// <summary>
    /// Displays the appropriate content when a <see cref="TabItem"/> id is selected
    /// </summary>
    /// <param name="tabItemId">The id of the requested tabitem</param>
    /// <exception cref="InvalidOperationException">Thrown if the tabitem id does not exist</exception>
    public async Task TabItemSelected(string tabItemId)
    {
        if (!ChildExists(tabItemId))
        {
            throw new InvalidOperationException($"The requested tab item id {tabItemId} does not exist");
        }

        await TabItemSelected(_children.Single(x => x.Id == tabItemId));
    }

    /// <summary>
    /// Allows a <see cref="TabItem"/> to check whether it is selected
    /// </summary>
    /// <param name="id">The id of the tab item</param>
    /// <returns>True if the tab item with the given id is currently selected</returns>
    public bool IsSelected(string id) => _selectedTabItemId == id;

    string? _containerClass;
    string? _tabsSelectedContentStyle;
    string? _tabsHeadersClass;
    string? _tabsHeadersStyle;

    bool IsTopOrBottom => TabHeaderPlacement is TabHeaderPlacement.Top or TabHeaderPlacement.Bottom;
    bool IsLeftOrRight => TabHeaderPlacement is TabHeaderPlacement.Left or TabHeaderPlacement.Right;

    /// <summary>
    /// Returns the count of registered <see cref="TabItem"/>s
    /// </summary>
    public List<TabItem> Children => _children;

    /// <summary>
    /// Returns the <see cref="TabItem"/> at the given index
    /// </summary>
    /// <param name="index">The index of the item requested</param>
    /// <returns>A <see cref="TabItem"/></returns>
    /// <exception cref="IndexOutOfRangeException">Thrown if the index requested is invalid</exception>
    /// <remarks>The index is zero based</remarks>
    public TabItem GetTabItemByIndex(int index)
    {
        if (index > _children.Count - 1 || index < 0)
        {
            throw new IndexOutOfRangeException($"The count of children is {_children.Count} so the request for index {index} is invalid (zero based)");
        }

        return _children[index];
    }

    /// <summary>
    /// Gets the first child <see cref="TabItem"/> registered to this Tabs
    /// </summary>
    public TabItem FirstChild => GetTabItemByIndex(0);

    List<TabItem> _children = new();

    /// <summary>
    /// Registers a child TabItem for this Tabs
    /// </summary>
    /// <param name="child">The <see cref="TabItem"/> to register</param>
    public void Register(TabItem child)
    {
        if (!ChildExists(child.Id))
        {
            _children.Add(child);
        }
    }

    bool ChildExists(string childId) => _children.Exists(x => x.Id == childId);

    /// <inheritdoc />
    protected override void OnAfterRender(bool firstRender)
    {
        if (firstRender)
        {
            //handle cases where the display of tabitems is dynamic and the preset one may not be
            //present in the tabs. Default to the first tabitem if so
            if (_selectedTabItemId is null || !ChildExists(_selectedTabItemId))
            {
                if (!_children.Any())
                {
                    throw new InvalidOperationException("The Tabs component must have at least one child");
                }

                _selectedTabItemId = FirstChild.Id;
                _selectedTabItemContent = FirstChild.ChildContent;
                StateHasChanged();
            }
        }
    }

    ///<inheritdoc/>
    protected override void OnParametersSet()
    {
        //set the selected tab item based on any preselection
        //this must directly set the _selectedTabItemId as _children are not yet loaded
        if (PreSelectTabId is not null && _selectedTabItemId is null)
        {
            _selectedTabItemId = PreSelectTabId;
        }

        _containerClass =
            new CssBuilder()
                .AddClass("d-flex")
                .AddClass("flex-row", TabHeaderPlacement == TabHeaderPlacement.Left)
                .AddClass("flex-column", TabHeaderPlacement == TabHeaderPlacement.Top)
                .AddClass("flex-row-reverse", TabHeaderPlacement == TabHeaderPlacement.Right)
                .AddClass("flex-column-reverse", TabHeaderPlacement == TabHeaderPlacement.Bottom)
                .Build();

        _tabsHeadersClass =
            new CssBuilder()
                .AddClass("tabs-headers-top", TabHeaderPlacement == TabHeaderPlacement.Top)
                .AddClass("tabs-headers-left", TabHeaderPlacement == TabHeaderPlacement.Left)
                .AddClass("tabs-headers-right", TabHeaderPlacement == TabHeaderPlacement.Right)
                .AddClass("tabs-headers-bottom", TabHeaderPlacement == TabHeaderPlacement.Bottom)
                .AddClass("tabs-headers", IsLeftOrRight)
                .Build();

        _tabsHeadersStyle =
            new StyleBuilder()
                .AddStyle("height", $"{ContentHeightPixels}px", IsLeftOrRight)
                .Build();

        const string marginWidth = "0.2rem";

        _tabsSelectedContentStyle =
            new StyleBuilder()
                .AddStyle("height", $"{ContentHeightPixels}px")
                .AddStyle("margin-right", marginWidth, TabHeaderPlacement is TabHeaderPlacement.Right)
                .AddStyle("margin-left", marginWidth, TabHeaderPlacement is TabHeaderPlacement.Left)
                .AddStyle("margin-top", marginWidth, TabHeaderPlacement is TabHeaderPlacement.Top)
                .AddStyle("margin-bottom", marginWidth, TabHeaderPlacement is TabHeaderPlacement.Bottom)
                .Build();
    }

}