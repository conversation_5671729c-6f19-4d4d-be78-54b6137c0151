﻿@using Microsoft.JSInterop
@using CCTC_Lib.Models.UI
@inherits CCTC_Components.Components.__CCTC.CCTCBase

<cctc-tabitem id="@Id" role="tab" class="@CssClass" style="@Style" data-author="cctc">
    @{
        var tabItemHeaderId = $"{Id}-tabitem-header";
        <Tooltip Id="@($"{Id}-tabitem-tooltip")"
                 Content="@Header"
                 TooltipBehaviour="@(new TooltipBehaviour.EnabledOnOverflow($"#{tabItemHeaderId}"))">
            <cctc-tabitem-header id="@tabItemHeaderId" @onclick="OnHeaderClicked" class="@_tabItemClass">
                @Header
            </cctc-tabitem-header>
        </Tooltip>
    }
</cctc-tabitem>

@code {

    /// <summary>
    /// The Tabs component for which this TabItem is a child
    /// </summary>
    [CascadingParameter]
    public required Tabs Parent { get; set; }

    /// <summary>
    /// The content of the TabItem
    /// </summary>
    [Parameter, EditorRequired]
    public required RenderFragment ChildContent { get; set; }

    /// <summary>
    /// The header to display in the TabItem
    /// </summary>
    [Parameter, EditorRequired]
    public required string Header { get; set; }

    async Task OnHeaderClicked()
    {
        await Parent.TabItemSelected(this);
    }

    TabHeaderPlacement TabHeaderPlacement => Parent.TabHeaderPlacement;

    string? _tabItemClass;

    /// <inheritdoc />
    protected override void OnParametersSet()
    {
        _tabItemClass =
            new CssBuilder()
                .AddClass("tab-item-header")
                .AddClass("top-item-header", TabHeaderPlacement is TabHeaderPlacement.Top)
                .AddClass("left-item-header", TabHeaderPlacement is TabHeaderPlacement.Left)
                .AddClass("bottom-item-header", TabHeaderPlacement is TabHeaderPlacement.Bottom)
                .AddClass("right-item-header", TabHeaderPlacement is TabHeaderPlacement.Right)
                .AddClass("tab-item-header-selected", Parent.IsSelected(Id))
                .AddClass("top-item-header-selected", TabHeaderPlacement is TabHeaderPlacement.Top && Parent.IsSelected(Id))
                .AddClass("left-item-header-selected", TabHeaderPlacement is TabHeaderPlacement.Left && Parent.IsSelected(Id))
                .AddClass("bottom-item-header-selected", TabHeaderPlacement is TabHeaderPlacement.Bottom && Parent.IsSelected(Id))
                .AddClass("right-item-header-selected", TabHeaderPlacement is TabHeaderPlacement.Right && Parent.IsSelected(Id))
                .Build();
    }

    /// <inheritdoc />
    protected override async Task OnInitializedAsync()
    {
        Parent.Register(this);

        if (Parent.IsSelected(Id))
        {
            await Parent.TabItemSelected(this);
        }
    }

}