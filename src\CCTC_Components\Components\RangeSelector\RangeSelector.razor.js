// Holds collections of range selector instances - following Chart component pattern
let rangeSelectors = [];
let dragState = [];

// Global event handlers (shared across all instances)
let globalEventListenersAdded = false;

export function initialize(track, dotNetRef, rangeId) {
    // Store the instance data using the unique ID
    const wrapper = track.closest('.component-wrapper');
    rangeSelectors[rangeId] = {
        trackElement: track,
        dotNetReference: dotNetRef,
        isVertical: wrapper.classList.contains('vertical'),
        isDisabled: wrapper.classList.contains('disabled')
    };
    
    dragState[rangeId] = {
        isDragging: false,
        dragThumbType: null,
        currentRangeId: null
    };
    
    // Add global event listeners only once
    if (!globalEventListenersAdded) {
        document.addEventListener('mousemove', handleMouseMove);
        document.addEventListener('mouseup', handleMouseUp);
        document.addEventListener('touchmove', handleTouchMove, { passive: false });
        document.addEventListener('touchend', handleTouchEnd);
        globalEventListenersAdded = true;
    }
    
    const thumbs = track.querySelectorAll('.cctc-rangeselector-thumb');
    
    thumbs.forEach((thumb) => {
        const isLowerThumb = thumb.classList.contains('cctc-rangeselector-thumb-lower');
        
        // Remove draggable attribute - we'll handle this with mouse/touch events
        thumb.removeAttribute('draggable');
        
        // Store the rangeId on the thumb element for event handling
        thumb.dataset.rangeId = rangeId;
        
        thumb.addEventListener('mousedown', (e) => {
            e.preventDefault();
            if (!rangeSelectors[rangeId].isDisabled) {
                startDrag(isLowerThumb, rangeId);
            }
        });
        
        thumb.addEventListener('touchstart', (e) => {
            e.preventDefault();
            if (!rangeSelectors[rangeId].isDisabled) {
                startDrag(isLowerThumb, rangeId);
            }
        });
        
        // Don't handle keyboard events in JavaScript - let Blazor handle them
    });
    
    // Store rangeId on track element and add click handler
    track.dataset.rangeId = rangeId;
    track.addEventListener('click', handleTrackClick);
}

export function startDrag(isLowerThumb, rangeId) {
    const state = dragState[rangeId];
    const instance = rangeSelectors[rangeId];
    
    if (!state || !instance || instance.isDisabled) return;
    
    state.isDragging = true;
    state.dragThumbType = isLowerThumb ? 'lower' : 'upper';
    state.currentRangeId = rangeId;
    
    const thumb = instance.trackElement.querySelector(
        isLowerThumb ? '.cctc-rangeselector-thumb-lower' : '.cctc-rangeselector-thumb-upper'
    );
    thumb.classList.add('cctc-rangeselector-dragging');
    
    // Prevent text selection during drag
    document.body.style.userSelect = 'none';
}

function handleMouseMove(e) {
    // Find the currently dragging range selector
    const currentRangeId = getCurrentDraggingRangeId();
    if (!currentRangeId) return;
    
    e.preventDefault();
    updateThumbPosition(e.clientX, currentRangeId, e.clientY);
}

function handleTouchMove(e) {
    // Find the currently dragging range selector
    const currentRangeId = getCurrentDraggingRangeId();
    if (!currentRangeId) return;
    
    e.preventDefault();
    if (e.touches.length > 0) {
        updateThumbPosition(e.touches[0].clientX, currentRangeId, e.touches[0].clientY);
    }
}

function handleMouseUp(e) {
    // Find the currently dragging range selector
    const currentRangeId = getCurrentDraggingRangeId();
    if (!currentRangeId) return;
    
    stopDrag(currentRangeId);
}

function handleTouchEnd(e) {
    // Find the currently dragging range selector
    const currentRangeId = getCurrentDraggingRangeId();
    if (!currentRangeId) return;
    
    stopDrag(currentRangeId);
}

function handleTrackClick(e) {
    const track = e.currentTarget;
    const rangeId = track.dataset.rangeId;
    const state = dragState[rangeId];
    const instance = rangeSelectors[rangeId];
    
    if (!state || !instance || state.isDragging || instance.isDisabled) return;
    
    const rect = track.getBoundingClientRect();
    let percentage;
    
    if (instance.isVertical) {
        const clickY = e.clientY - rect.top;
        percentage = Math.max(0, Math.min(100, 100 - (clickY / rect.height) * 100));
    } else {
        const clickX = e.clientX - rect.left;
        percentage = Math.max(0, Math.min(100, (clickX / rect.width) * 100));
    }
    
    // Determine which thumb is closer to the click position
    const lowerThumb = track.querySelector('.cctc-rangeselector-thumb-lower');
    const upperThumb = track.querySelector('.cctc-rangeselector-thumb-upper');
    
    let lowerPos, upperPos;
    
    if (instance.isVertical) {
        lowerPos = parseFloat(lowerThumb.style.bottom) || 0;
        upperPos = parseFloat(upperThumb.style.bottom) || 100;
    } else {
        lowerPos = parseFloat(lowerThumb.style.left) || 0;
        upperPos = parseFloat(upperThumb.style.left) || 100;
    }
    
    const distanceToLower = Math.abs(percentage - lowerPos);
    const distanceToUpper = Math.abs(percentage - upperPos);
    
    const isLowerThumb = distanceToLower < distanceToUpper;
    
    instance.dotNetReference.invokeMethodAsync('UpdateValueAsync', percentage, isLowerThumb);
}

function updateThumbPosition(clientX, rangeId, clientY = null) {
    const state = dragState[rangeId];
    const instance = rangeSelectors[rangeId];
    
    if (!state || !instance) return;
    
    const rect = instance.trackElement.getBoundingClientRect();
    let percentage;
    
    if (instance.isVertical) {
        const y = clientY - rect.top;
        percentage = Math.max(0, Math.min(100, 100 - (y / rect.height) * 100));
    } else {
        const x = clientX - rect.left;
        percentage = Math.max(0, Math.min(100, (x / rect.width) * 100));
    }
    
    const isLowerThumb = state.dragThumbType === 'lower';
    instance.dotNetReference.invokeMethodAsync('UpdateValueAsync', percentage, isLowerThumb);
}

function stopDrag(rangeId) {
    const state = dragState[rangeId];
    const instance = rangeSelectors[rangeId];
    
    if (!state || !instance || !state.isDragging) return;
    
    state.isDragging = false;
    
    const thumb = instance.trackElement.querySelector(
        state.dragThumbType === 'lower' ? '.cctc-rangeselector-thumb-lower' : '.cctc-rangeselector-thumb-upper'
    );
    thumb.classList.remove('cctc-rangeselector-dragging');
    
    // Restore text selection
    document.body.style.userSelect = '';
    
    state.dragThumbType = null;
    state.currentRangeId = null;
    
    instance.dotNetReference.invokeMethodAsync('StopDragAsync');
}

function getCurrentDraggingRangeId() {
    for (const rangeId in dragState) {
        if (dragState[rangeId].isDragging) {
            return rangeId;
        }
    }
    return null;
}

export function preventDefault(event) {
    event.preventDefault();
}

// Cleanup function for when a specific component instance is disposed
export function dispose(rangeId) {
    // Remove this instance
    if (rangeSelectors[rangeId]) {
        delete rangeSelectors[rangeId];
    }
    if (dragState[rangeId]) {
        delete dragState[rangeId];
    }
    
    // If no more instances exist, remove global event listeners
    if (Object.keys(rangeSelectors).length === 0 && globalEventListenersAdded) {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
        document.removeEventListener('touchmove', handleTouchMove);
        document.removeEventListener('touchend', handleTouchEnd);
        globalEventListenersAdded = false;
    }
}