.content {
    padding-top: 1.1rem;
    background-color: var(--cctc-background-color);
}

#blazor-error-ui {
    background: orange;
    bottom: 0;
    box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.2);
    display: none;
    left: 0;
    padding: 0.6rem 1.25rem 0.7rem 1.25rem;
    position: fixed;
    width: 100%;
    z-index: 1000;
}

#blazor-error-ui .dismiss {
    cursor: pointer;
    position: absolute;
    right: 0.75rem;
    top: 0.5rem;
}

.loading-progress {
    position: relative;
    display: block;
    width: 8rem;
    height: 8rem;
    margin: 20vh auto 1rem auto;
}

.loading-progress circle {
    fill: none;
    stroke: #e0e0e0;
    stroke-width: 0.6rem;
    transform-origin: 50% 50%;
    transform: rotate(-90deg);
}

.loading-progress circle:last-child {
    stroke: #1b6ec2;
    stroke-dasharray: calc(3.141 * var(--blazor-load-percentage, 0%) * 0.8), 500%;
    transition: stroke-dasharray 0.05s ease-in-out;
}

.loading-progress-text {
    position: absolute;
    text-align: center;
    font-weight: bold;


.loading-progress-text {
    inset: calc(20vh + 3.25rem) 0 auto 0.2rem;
    color: black;
}

.loading-progress-text:after {
    content: var(--blazor-load-percentage-text, "Loading");
}

*.dev-outline {
    outline: 1px solid #f00 !important;
}

.dev-todo {
    background-color: var(--bs-danger);
    color: yellow;
}


/*scrollbar should be set once here and will then cascade to all content*/
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-track {
    background: var(--cctc-background-color);
    border: none;
}

::-webkit-scrollbar-thumb {
    background: var(--cctc-highlight-background-color);
    border-radius: 6px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--cctc-highlight-color);
    border-radius: 6px;
}


/*desktop*/
@media (min-width: 1200px) {

    ::-webkit-scrollbar {
        width: 9px;
        height: 9px;
    }
}