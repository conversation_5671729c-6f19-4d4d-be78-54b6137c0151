
> cctc_components_functional_tests@0.0.1 only
> npm run cucumber -- --tags @rangeselector


> cctc_components_functional_tests@0.0.1 cucumber
> tsx ./node_modules/@cucumber/cucumber/bin/cucumber.js --tags @rangeselector

tests running on chrome
base url: https://localhost:7021
@component @rangeselector @rangeselector_1
Feature: the range selector component sample page navigation # features\rangeselector\rangeselector_1.feature:2

  @component @rangeselector @rangeselector_1
  Scenario: the range selector sample page is available # features\rangeselector\rangeselector_1.feature:3
    Given the user is at the home page
Waiting for app readiness signal...
✅ App ready signal received successfully
    When the user selects the "Range selector" component in the container "Input"
    Then the url ending is "rangeselectorsample"

@component @rangeselector @rangeselector_2
Feature: all range selector data types are displayed correctly # features\rangeselector\rangeselector_2.feature:2

  @component @rangeselector @rangeselector_2
  Scenario: the range selector displays integer values correctly # features\rangeselector\rangeselector_2.feature:3
    Given the user is at the home page
Waiting for app readiness signal...
✅ App ready signal received successfully
    And the user selects the "Range selector" component in the container "Input"
    When the range selector is displayed
    Then the range selector displays "integer" values from "0" to "100"
    And the "lower" value is 25
    And the "upper" value is 75

  @component @rangeselector @rangeselector_2
  Scenario: the range selector displays double values correctly # features\rangeselector\rangeselector_2.feature:11
    Given the user is at the home page
Waiting for app readiness signal...
✅ App ready signal received successfully
    And the user selects the "Range selector" component in the container "Input"
    And the user clicks the "Usage" tab
    And the user expands the concertina by clicking on the header with the text "Double Range"
    When the range selector displays "double" values from "0.00" to "10.00"
    Then the values are formatted to 2 decimal places
    And the "lower" value is "2.50"
    And the "upper" value is "7.80"

  @component @rangeselector @rangeselector_2
  Scenario: the range selector displays date values correctly # features\rangeselector\rangeselector_2.feature:21
    Given the user is at the home page
Waiting for app readiness signal...
✅ App ready signal received successfully
    And the user selects the "Range selector" component in the container "Input"
    And the user clicks the "Usage" tab
    And the user expands the concertina by clicking on the header with the text "Date Range"
    When the range selector displays "date" values from "2024-01-01" to "2024-12-31"
    Then the values are formatted "YYYY-MM-DD"
    And the "lower" value is "2024-03-15"
    And the "upper" value is "2024-09-15"

  @component @rangeselector @rangeselector_2
  Scenario: the range selector displays time values correctly # features\rangeselector\rangeselector_2.feature:31
    Given the user is at the home page
Waiting for app readiness signal...
    × failed
      Error: function timed out, ensure the promise resolves within 60000 milliseconds
          at Timeout.<anonymous> (C:\Users\<USER>\source\repos\CCTC_Components\functional_scripts\node_modules\@cucumber\cucumber\src\time.ts:52:14)
          at listOnTimeout (node:internal/timers:588:17)
          at process.processTimers (node:internal/timers:523:7)
    And the user selects the "Range selector" component in the container "Input"
    - skipped
    And the user clicks the "Usage" tab
    - skipped
    And the user expands the concertina by clicking on the header with the text "Time Range (TimeSpan)"
    - skipped
    When the range selector displays "time" values from "00:00" to "24:00"
    - skipped
    Then the values are formatted "HH:mm"
    - skipped
    And the "lower" value is "09:00"
    - skipped
    And the "upper" value is "17:00"
    - skipped
⚠️ Error in app readiness check: Error: page.evaluate: Target page, context or browser has been closed
Falling back to DOM availability check

@component @rangeselector @rangeselector_3
Feature: the range selector functions correctly with blue section and value constraints # features\rangeselector\rangeselector_3.feature:2

  @component @rangeselector @rangeselector_3
  Scenario: the range selector displays blue section between thumbs # features\rangeselector\rangeselector_3.feature:3
    Given the user is at the home page
Waiting for app readiness signal...
✅ App ready signal received successfully
    And the user selects the "Range selector" component in the container "Input"
    And the user clicks the "Usage" tab
    And the user expands the concertina by clicking on the header with the text "Integer Range"
    When the range selector is displayed
    Then the range selector fill lies between the two thumbs

  @component @rangeselector @rangeselector_3
  Scenario: the range selector prevents invalid ranges # features\rangeselector\rangeselector_3.feature:11
    Given the user is at the home page
Waiting for app readiness signal...
✅ App ready signal received successfully
    And the user selects the "Range selector" component in the container "Input"
    And the user clicks the "Usage" tab
    And the user expands the concertina by clicking on the header with the text "Integer Range"
    And the range selector displays "integer" values from "0" to "100"
    And the "upper" value is 75
    When the user drags the lower thumb to position 90%
    Then the lower value does not exceed the upper value

@component @rangeselector @rangeselector_4
Feature: the range selector supports different orientations and sizing options # features\rangeselector\rangeselector_4.feature:2

  @component @rangeselector @rangeselector_4
  Scenario: thumb positions match values when dragged # features\rangeselector\rangeselector_4.feature:3
    Given the user is at the home page
Waiting for app readiness signal...
    × failed
      Error: function timed out, ensure the promise resolves within 60000 milliseconds
          at Timeout.<anonymous> (C:\Users\<USER>\source\repos\CCTC_Components\functional_scripts\node_modules\@cucumber\cucumber\src\time.ts:52:14)
          at listOnTimeout (node:internal/timers:588:17)
          at process.processTimers (node:internal/timers:523:7)
    And the user selects the "Range selector" component in the container "Input"
    - skipped
    And the user clicks the "Usage" tab
    - skipped
    And the user expands the concertina by clicking on the header with the text "Integer Range"
    - skipped
    When the user drags the lower thumb to position 50%
    - skipped
    Then the lower thumb is positioned at 50% along the track
    - skipped
    And the lower value changes to approximately 50
    - skipped
    When the user drags the upper thumb to position 80%
    - skipped
    Then the upper thumb is positioned at 80% along the track
    - skipped
    And the upper value changes to approximately 80
    - skipped
⚠️ Error in app readiness check: Error: page.evaluate: Target page, context or browser has been closed
Falling back to DOM availability check

  @component @rangeselector @rangeselector_4
  Scenario: the vertical range selector displays and functions correctly # features\rangeselector\rangeselector_4.feature:15
    Given the user is at the home page
Waiting for app readiness signal...
✅ App ready signal received successfully
    And the user selects the "Range selector" component in the container "Input"
    And the user clicks the "Usage" tab
    And the user expands the concertina by clicking on the header with the text "Vertical Orientation"
    And the range selector is oriented "vertically"
    And the range selector displays "integer" values from "0" to "100"
    And the "lower" value is 25
    And the "upper" value is 75
    And the vertical range selector at position 1 has the lower thumb at position 70%
    × failed
      Error: expect(received).toBe(expected) // Object.is equality

      Expected: 70
      Received: 63
          at Proxy.<anonymous> (C:\Users\<USER>\source\repos\CCTC_Components\functional_scripts\node_modules\playwright\lib\matchers\expect.js:227:37)
          at CustomWorld.<anonymous> (C:\Users\<USER>\source\repos\CCTC_Components\functional_scripts\src\steps\components.rangeselector.steps.ts:100:44)
    And the vertical range selector at position 1 has the upper thumb at position 30%
    - skipped
    When the user drags the lower thumb to position 30%
    - skipped
    Then the lower value changes to approximately 30
    - skipped
    When the user drags the upper thumb to position 85%
    - skipped
    Then the upper value changes to approximately 85
    - skipped

  @component @rangeselector @rangeselector_4
  Scenario: the range selector supports different width sizes # features\rangeselector\rangeselector_4.feature:31
    Given the user is at the home page
Waiting for app readiness signal...
✅ App ready signal received successfully
    And the user selects the "Range selector" component in the container "Input"
    And the user clicks the "Usage" tab
    And the user expands the concertina by clicking on the header with the text "Small Width"
    And the range selector has "small" size
    And the "lower" value is 15
    And the "upper" value is 85
    When the user drags the lower thumb to position 40%
    Then the lower value changes to approximately 40
    When the user drags the upper thumb to position 70%
    Then the upper value changes to approximately 70

  @component @rangeselector @rangeselector_4
  Scenario: the vertical range selector supports different height sizes # features\rangeselector\rangeselector_4.feature:45
    Given the user is at the home page
Waiting for app readiness signal...
✅ App ready signal received successfully
    And the user selects the "Range selector" component in the container "Input"
    And the user clicks the "Usage" tab
    When the user expands the concertina by clicking on the header with the text "Vertical Small"
    Then the range selector is oriented "vertically"
    And the range selector has "small" size
    And the "lower" value is 40
    And the "upper" value is 60
    And the vertical range selector at position 1 has the lower thumb at position 57%
    × failed
      Error: expect(received).toBe(expected) // Object.is equality

      Expected: 57
      Received: 53
          at Proxy.<anonymous> (C:\Users\<USER>\source\repos\CCTC_Components\functional_scripts\node_modules\playwright\lib\matchers\expect.js:227:37)
          at CustomWorld.<anonymous> (C:\Users\<USER>\source\repos\CCTC_Components\functional_scripts\src\steps\components.rangeselector.steps.ts:100:44)
    And the vertical range selector at position 1 has the upper thumb at position 40%
    - skipped
    When the user drags the lower thumb to position 50%
    - skipped
    Then the lower value changes to approximately 50
    - skipped
    When the user drags the upper thumb to position 70%
    - skipped
    Then the upper value changes to approximately 70
    - skipped

@component @rangeselector @rangeselector_5
Feature: multiple range selectors work independently and simultaneously # features\rangeselector\rangeselector_5.feature:2

  @component @rangeselector @rangeselector_5
  Scenario: multiple range selectors display different data types independently # features\rangeselector\rangeselector_5.feature:3
    Given the user is at the home page
Waiting for app readiness signal...
✅ App ready signal received successfully
    And the user selects the "Range selector" component in the container "Input"
    And the user clicks the "Usage" tab
    When the user expands the concertina by clicking on the header with the text "Multiple Sliders"
    Then the range selector at position 1 displays "price" values
    × failed
      Error: [31mTimed out 5000ms waiting for [39mexpect(locator).toBeVisible()

      Locator: locator('[id$=sampler] cctc-tabs-selected-content').locator('text="price"').first()
      Expected: visible
      Received: <element(s) not found>
      Call log:
      [2m  - locator._expect with timeout 5000ms[22m
      [2m  - waiting for locator('[id$=sampler] cctc-tabs-selected-content').locator('text="price"').first()[22m

          at Proxy.<anonymous> (C:\Users\<USER>\source\repos\CCTC_Components\functional_scripts\node_modules\playwright\lib\matchers\expect.js:227:37)
          at CustomWorld.<anonymous> (C:\Users\<USER>\source\repos\CCTC_Components\functional_scripts\src\steps\components.rangeselector.steps.ts:369:23)
    And the range selector at position 2 displays "quantity" values
    - skipped

  @component @rangeselector @rangeselector_5
  Scenario: changing one range selector does not affect others # features\rangeselector\rangeselector_5.feature:12
    Given the user is at the home page
Waiting for app readiness signal...
✅ App ready signal received successfully
    And the user selects the "Range selector" component in the container "Input"
    And the user clicks the "Usage" tab
    And the user expands the concertina by clicking on the header with the text "Multiple Sliders"
    And the range selector at position 1 has the lower thumb at position 10%
    And the range selector at position 1 has the upper thumb at position 50%
    And the range selector at position 2 has the lower thumb at position 10%
    × failed
      Error: expect(received).toBe(expected) // Object.is equality

      Expected: 10
      Received: 8
          at Proxy.<anonymous> (C:\Users\<USER>\source\repos\CCTC_Components\functional_scripts\node_modules\playwright\lib\matchers\expect.js:227:37)
          at CustomWorld.<anonymous> (C:\Users\<USER>\source\repos\CCTC_Components\functional_scripts\src\steps\components.rangeselector.steps.ts:77:44)
    And the range selector at position 2 has the upper thumb at position 50%
    - skipped
    When the user drags the range selector at position 1 lower thumb to position 20%
    - skipped
    And the user drags the range selector at position 1 upper thumb to position 70%
    - skipped
    Then the range selector at position 2 has the lower thumb at position 10%
    - skipped
    And the range selector at position 2 has the upper thumb at position 50%
    - skipped

  @component @rangeselector @rangeselector_5
  Scenario: multiple vertical range selectors work independently # features\rangeselector\rangeselector_5.feature:27
    Given the user is at the home page
Waiting for app readiness signal...
✅ App ready signal received successfully
    And the user selects the "Range selector" component in the container "Input"
    And the user clicks the "Usage" tab
    And the user expands the concertina by clicking on the header with the text "Multiple Vertical Controls"
    And the vertical range selector at position 1 has the lower thumb at position 80%
    And the vertical range selector at position 1 has the upper thumb at position 37%
    And the vertical range selector at position 2 has the lower thumb at position 70%
    And the vertical range selector at position 2 has the upper thumb at position 25%
    × failed
      Error: expect(received).toBe(expected) // Object.is equality

      Expected: 25
      Received: 11
          at Proxy.<anonymous> (C:\Users\<USER>\source\repos\CCTC_Components\functional_scripts\node_modules\playwright\lib\matchers\expect.js:227:37)
          at CustomWorld.<anonymous> (C:\Users\<USER>\source\repos\CCTC_Components\functional_scripts\src\steps\components.rangeselector.steps.ts:100:44)
    And the vertical range selector at position 3 has the lower thumb at position 60%
    - skipped
    And the vertical range selector at position 3 has the upper thumb at position 10%
    - skipped
    When the user drags the range selector at position 1 lower thumb to position 30%
    - skipped
    And the user drags the range selector at position 1 upper thumb to position 70%
    - skipped
    Then the vertical range selector at position 2 has the lower thumb at position 70%
    - skipped
    And the vertical range selector at position 2 has the upper thumb at position 20%
    - skipped
    And the vertical range selector at position 3 has the lower thumb at position 60%
    - skipped
    And the vertical range selector at position 3 has the upper thumb at position 10%
    - skipped
    When the user drags the range selector at position 2 lower thumb to position 40%
    - skipped
    And the user drags the range selector at position 2 upper thumb to position 80%
    - skipped
    Then the vertical range selector at position 1 has the lower thumb at position 70%
    - skipped
    And the vertical range selector at position 1 has the upper thumb at position 30%
    - skipped
    And the vertical range selector at position 3 has the lower thumb at position 60%
    - skipped
    And the vertical range selector at position 3 has the upper thumb at position 10%
    - skipped
    When the user drags the range selector at position 3 lower thumb to position 25%
    - skipped
    And the user drags the range selector at position 3 upper thumb to position 85%
    - skipped
    Then the vertical range selector at position 1 has the lower thumb at position 70%
    - skipped
    And the vertical range selector at position 1 has the upper thumb at position 30%
    - skipped
    And the vertical range selector at position 2 has the lower thumb at position 60%
    - skipped
    And the vertical range selector at position 2 has the upper thumb at position 20%
    - skipped

@component @rangeselector @rangeselector_6
Feature: the range selector supports accessibility and keyboard navigation # features\rangeselector\rangeselector_6.feature:2

  @component @rangeselector @rangeselector_6
  Scenario: the range selector has proper ARIA attributes # features\rangeselector\rangeselector_6.feature:3
    Given the user is at the home page
Waiting for app readiness signal...
✅ App ready signal received successfully
    And the user selects the "Range selector" component in the container "Input"
    And the user clicks the "Usage" tab
    And the user expands the concertina by clicking on the header with the text "Integer Range"
    When the range selector is displayed
    Then the lower thumb has role "slider"
    And the lower thumb has aria-label "Lower range handle"
    And the upper thumb has role "slider"
    And the upper thumb has proper ARIA attributes

  @component @rangeselector @rangeselector_6
  Scenario: the range selector responds to keyboard navigation # features\rangeselector\rangeselector_6.feature:14
    Given the user is at the home page
Waiting for app readiness signal...
✅ App ready signal received successfully
    And the user selects the "Range selector" component in the container "Input"
    And the user clicks the "Usage" tab
    And the user expands the concertina by clicking on the header with the text "Integer Range"
    When the user focuses on the lower thumb
    And the user presses the right arrow key 5 times
    Then the lower value increases by approximately 5

@component @rangeselector @rangeselector_7
Feature: the range selector disabled state prevents interaction # features\rangeselector\rangeselector_7.feature:2

  @component @rangeselector @rangeselector_7
  Scenario: the disabled range selector displays correct styling and prevents interaction # features\rangeselector\rangeselector_7.feature:3
    Given the user is at the home page
Waiting for app readiness signal...
✅ App ready signal received successfully
    And the user selects the "Range selector" component in the container "Input"
    And the user clicks the "Usage" tab
    When the user expands the concertina by clicking on the header with the text "Disabled State"
    Then the range selector is properly disabled
    × failed
      Error: expect(received).toBeLessThan(expected)

      Expected: < 1
      Received:   1
          at Proxy.<anonymous> (C:\Users\<USER>\source\repos\CCTC_Components\functional_scripts\node_modules\playwright\lib\matchers\expect.js:227:37)
          at CustomWorld.<anonymous> (C:\Users\<USER>\source\repos\CCTC_Components\functional_scripts\src\steps\components.rangeselector.steps.ts:463:38)
    And the range selector appears disabled
    - skipped
    And the range selector thumbs cannot be moved
    - skipped
    And the disabled range selector component image matches the base image "disabled range selector"
    - skipped

  @component @rangeselector @rangeselector_7
  Scenario: the disabled range selector does not respond to mouse interaction # features\rangeselector\rangeselector_7.feature:13
    Given the user is at the home page
Waiting for app readiness signal...
✅ App ready signal received successfully
    And the user selects the "Range selector" component in the container "Input"
    And the user clicks the "Usage" tab
    And the user expands the concertina by clicking on the header with the text "Disabled State"
    And the range selector is properly disabled
    × failed
      Error: expect(received).toBeLessThan(expected)

      Expected: < 1
      Received:   1
          at Proxy.<anonymous> (C:\Users\<USER>\source\repos\CCTC_Components\functional_scripts\node_modules\playwright\lib\matchers\expect.js:227:37)
          at CustomWorld.<anonymous> (C:\Users\<USER>\source\repos\CCTC_Components\functional_scripts\src\steps\components.rangeselector.steps.ts:463:38)
    When the user attempts to drag the disabled lower thumb
    - skipped
    Then the disabled thumb does not move
    - skipped

tests run on chrome
base url: https://localhost:7021
Failures:

1) Scenario: the range selector displays time values correctly # features\rangeselector\rangeselector_2.feature:31
   √ Before # src\support\common-hooks.ts:61
   × Given the user is at the home page # src\steps\components.common.steps.ts:76
       Attachment (text/plain): [Browser Console] Checking if app readiness API is available
       Attachment (text/plain): [Browser Console] App readiness API found, waiting for app ready signal
       Attachment (text/plain): [CCTC_Components_UI] Waiting for app to become ready...
       Error: function timed out, ensure the promise resolves within 60000 milliseconds
           at Timeout.<anonymous> (C:\Users\<USER>\source\repos\CCTC_Components\functional_scripts\node_modules\@cucumber\cucumber\src\time.ts:52:14)
           at listOnTimeout (node:internal/timers:588:17)
           at process.processTimers (node:internal/timers:523:7)
   - And the user selects the "Range selector" component in the container "Input" # src\steps\components.common.steps.ts:89
   - And the user clicks the "Usage" tab # src\steps\components.tabs.steps.ts:14
   - And the user expands the concertina by clicking on the header with the text "Time Range (TimeSpan)" # src\steps\components.concertina.steps.ts:45
   - When the range selector displays "time" values from "00:00" to "24:00" # src\steps\components.rangeselector.steps.ts:157
   - Then the values are formatted "HH:mm" # src\steps\components.rangeselector.steps.ts:232
   - And the "lower" value is "09:00" # src\steps\components.rangeselector.steps.ts:254
   - And the "upper" value is "17:00" # src\steps\components.rangeselector.steps.ts:254
   √ After # src\support\common-hooks.ts:85
       Attachment (text/plain): Status: FAILED. Duration:60s
       Attachment (text/plain): The screenshot attached below simply shows the whole page and is not an image for a specific locator
       Attachment (image/png)

2) Scenario: thumb positions match values when dragged # features\rangeselector\rangeselector_4.feature:3
   √ Before # src\support\common-hooks.ts:61
   × Given the user is at the home page # src\steps\components.common.steps.ts:76
       Attachment (text/plain): [Browser Console] Checking if app readiness API is available
       Attachment (text/plain): [Browser Console] App readiness API found, waiting for app ready signal
       Attachment (text/plain): [CCTC_Components_UI] Waiting for app to become ready...
       Error: function timed out, ensure the promise resolves within 60000 milliseconds
           at Timeout.<anonymous> (C:\Users\<USER>\source\repos\CCTC_Components\functional_scripts\node_modules\@cucumber\cucumber\src\time.ts:52:14)
           at listOnTimeout (node:internal/timers:588:17)
           at process.processTimers (node:internal/timers:523:7)
   - And the user selects the "Range selector" component in the container "Input" # src\steps\components.common.steps.ts:89
   - And the user clicks the "Usage" tab # src\steps\components.tabs.steps.ts:14
   - And the user expands the concertina by clicking on the header with the text "Integer Range" # src\steps\components.concertina.steps.ts:45
   - When the user drags the lower thumb to position 50% # src\steps\components.rangeselector.steps.ts:28
   - Then the lower thumb is positioned at 50% along the track # src\steps\components.rangeselector.steps.ts:302
   - And the lower value changes to approximately 50 # src\steps\components.rangeselector.steps.ts:262
   - When the user drags the upper thumb to position 80% # src\steps\components.rangeselector.steps.ts:28
   - Then the upper thumb is positioned at 80% along the track # src\steps\components.rangeselector.steps.ts:302
   - And the upper value changes to approximately 80 # src\steps\components.rangeselector.steps.ts:262
   √ After # src\support\common-hooks.ts:85
       Attachment (text/plain): Status: FAILED. Duration:60s
       Attachment (text/plain): The screenshot attached below simply shows the whole page and is not an image for a specific locator
       Attachment (image/png)

3) Scenario: the vertical range selector displays and functions correctly # features\rangeselector\rangeselector_4.feature:15
   √ Before # src\support\common-hooks.ts:61
   √ Given the user is at the home page # src\steps\components.common.steps.ts:76
       Attachment (text/plain): [Browser Console] Checking if app readiness API is available
       Attachment (text/plain): [Browser Console] App readiness API found, waiting for app ready signal
       Attachment (text/plain): [CCTC_Components_UI] Waiting for app to become ready...
       Attachment (text/plain): [CCTC_Components_UI] Wait completed - app is now ready
       Attachment (text/plain): [CCTC_Components_UI] Application is now ready - 2025-08-27T11:32:03.326Z
       Attachment (text/plain): [Browser Console] App ready signal received
   √ And the user selects the "Range selector" component in the container "Input" # src\steps\components.common.steps.ts:89
   √ And the user clicks the "Usage" tab # src\steps\components.tabs.steps.ts:14
   √ And the user expands the concertina by clicking on the header with the text "Vertical Orientation" # src\steps\components.concertina.steps.ts:45
   √ And the range selector is oriented "vertically" # src\steps\components.rangeselector.steps.ts:410
   √ And the range selector displays "integer" values from "0" to "100" # src\steps\components.rangeselector.steps.ts:157
   √ And the "lower" value is 25 # src\steps\components.rangeselector.steps.ts:242
   √ And the "upper" value is 75 # src\steps\components.rangeselector.steps.ts:242
   × And the vertical range selector at position 1 has the lower thumb at position 70% # src\steps\components.rangeselector.steps.ts:82
       Error: expect(received).toBe(expected) // Object.is equality

       Expected: 70
       Received: 63
           at Proxy.<anonymous> (C:\Users\<USER>\source\repos\CCTC_Components\functional_scripts\node_modules\playwright\lib\matchers\expect.js:227:37)
           at CustomWorld.<anonymous> (C:\Users\<USER>\source\repos\CCTC_Components\functional_scripts\src\steps\components.rangeselector.steps.ts:100:44)
   - And the vertical range selector at position 1 has the upper thumb at position 30% # src\steps\components.rangeselector.steps.ts:82
   - When the user drags the lower thumb to position 30% # src\steps\components.rangeselector.steps.ts:28
   - Then the lower value changes to approximately 30 # src\steps\components.rangeselector.steps.ts:262
   - When the user drags the upper thumb to position 85% # src\steps\components.rangeselector.steps.ts:28
   - Then the upper value changes to approximately 85 # src\steps\components.rangeselector.steps.ts:262
   √ After # src\support\common-hooks.ts:85
       Attachment (text/plain): Status: FAILED. Duration:0s
       Attachment (text/plain): The screenshot attached below simply shows the whole page and is not an image for a specific locator
       Attachment (image/png)

4) Scenario: the vertical range selector supports different height sizes # features\rangeselector\rangeselector_4.feature:45
   √ Before # src\support\common-hooks.ts:61
   √ Given the user is at the home page # src\steps\components.common.steps.ts:76
       Attachment (text/plain): [Browser Console] Checking if app readiness API is available
       Attachment (text/plain): [Browser Console] App readiness API found, waiting for app ready signal
       Attachment (text/plain): [CCTC_Components_UI] Waiting for app to become ready...
       Attachment (text/plain): [CCTC_Components_UI] Wait completed - app is now ready
       Attachment (text/plain): [CCTC_Components_UI] Application is now ready - 2025-08-27T11:32:12.237Z
       Attachment (text/plain): [Browser Console] App ready signal received
   √ And the user selects the "Range selector" component in the container "Input" # src\steps\components.common.steps.ts:89
   √ And the user clicks the "Usage" tab # src\steps\components.tabs.steps.ts:14
   √ When the user expands the concertina by clicking on the header with the text "Vertical Small" # src\steps\components.concertina.steps.ts:45
   √ Then the range selector is oriented "vertically" # src\steps\components.rangeselector.steps.ts:410
   √ And the range selector has "small" size # src\steps\components.rangeselector.steps.ts:419
   √ And the "lower" value is 40 # src\steps\components.rangeselector.steps.ts:242
   √ And the "upper" value is 60 # src\steps\components.rangeselector.steps.ts:242
   × And the vertical range selector at position 1 has the lower thumb at position 57% # src\steps\components.rangeselector.steps.ts:82
       Error: expect(received).toBe(expected) // Object.is equality

       Expected: 57
       Received: 53
           at Proxy.<anonymous> (C:\Users\<USER>\source\repos\CCTC_Components\functional_scripts\node_modules\playwright\lib\matchers\expect.js:227:37)
           at CustomWorld.<anonymous> (C:\Users\<USER>\source\repos\CCTC_Components\functional_scripts\src\steps\components.rangeselector.steps.ts:100:44)
   - And the vertical range selector at position 1 has the upper thumb at position 40% # src\steps\components.rangeselector.steps.ts:82
   - When the user drags the lower thumb to position 50% # src\steps\components.rangeselector.steps.ts:28
   - Then the lower value changes to approximately 50 # src\steps\components.rangeselector.steps.ts:262
   - When the user drags the upper thumb to position 70% # src\steps\components.rangeselector.steps.ts:28
   - Then the upper value changes to approximately 70 # src\steps\components.rangeselector.steps.ts:262
   √ After # src\support\common-hooks.ts:85
       Attachment (text/plain): Status: FAILED. Duration:0s
       Attachment (text/plain): The screenshot attached below simply shows the whole page and is not an image for a specific locator
       Attachment (image/png)

5) Scenario: multiple range selectors display different data types independently # features\rangeselector\rangeselector_5.feature:3
   √ Before # src\support\common-hooks.ts:61
   √ Given the user is at the home page # src\steps\components.common.steps.ts:76
       Attachment (text/plain): [Browser Console] Checking if app readiness API is available
       Attachment (text/plain): [Browser Console] App readiness API found, waiting for app ready signal
       Attachment (text/plain): [CCTC_Components_UI] Waiting for app to become ready...
       Attachment (text/plain): [CCTC_Components_UI] Wait completed - app is now ready
       Attachment (text/plain): [CCTC_Components_UI] Application is now ready - 2025-08-27T11:32:17.900Z
       Attachment (text/plain): [Browser Console] App ready signal received
   √ And the user selects the "Range selector" component in the container "Input" # src\steps\components.common.steps.ts:89
   √ And the user clicks the "Usage" tab # src\steps\components.tabs.steps.ts:14
   √ When the user expands the concertina by clicking on the header with the text "Multiple Sliders" # src\steps\components.concertina.steps.ts:45
   × Then the range selector at position 1 displays "price" values # src\steps\components.rangeselector.steps.ts:359
       Error: [31mTimed out 5000ms waiting for [39mexpect(locator).toBeVisible()

       Locator: locator('[id$=sampler] cctc-tabs-selected-content').locator('text="price"').first()
       Expected: visible
       Received: <element(s) not found>
       Call log:
       [2m  - locator._expect with timeout 5000ms[22m
       [2m  - waiting for locator('[id$=sampler] cctc-tabs-selected-content').locator('text="price"').first()[22m

           at Proxy.<anonymous> (C:\Users\<USER>\source\repos\CCTC_Components\functional_scripts\node_modules\playwright\lib\matchers\expect.js:227:37)
           at CustomWorld.<anonymous> (C:\Users\<USER>\source\repos\CCTC_Components\functional_scripts\src\steps\components.rangeselector.steps.ts:369:23)
   - And the range selector at position 2 displays "quantity" values # src\steps\components.rangeselector.steps.ts:359
   √ After # src\support\common-hooks.ts:85
       Attachment (text/plain): Status: FAILED. Duration:5s
       Attachment (text/plain): The screenshot attached below simply shows the whole page and is not an image for a specific locator
       Attachment (image/png)

6) Scenario: changing one range selector does not affect others # features\rangeselector\rangeselector_5.feature:12
   √ Before # src\support\common-hooks.ts:61
   √ Given the user is at the home page # src\steps\components.common.steps.ts:76
       Attachment (text/plain): [Browser Console] Checking if app readiness API is available
       Attachment (text/plain): [Browser Console] App readiness API found, waiting for app ready signal
       Attachment (text/plain): [CCTC_Components_UI] Waiting for app to become ready...
       Attachment (text/plain): [CCTC_Components_UI] Wait completed - app is now ready
       Attachment (text/plain): [CCTC_Components_UI] Application is now ready - 2025-08-27T11:32:28.293Z
       Attachment (text/plain): [Browser Console] App ready signal received
   √ And the user selects the "Range selector" component in the container "Input" # src\steps\components.common.steps.ts:89
   √ And the user clicks the "Usage" tab # src\steps\components.tabs.steps.ts:14
   √ And the user expands the concertina by clicking on the header with the text "Multiple Sliders" # src\steps\components.concertina.steps.ts:45
   √ And the range selector at position 1 has the lower thumb at position 10% # src\steps\components.rangeselector.steps.ts:59
   √ And the range selector at position 1 has the upper thumb at position 50% # src\steps\components.rangeselector.steps.ts:59
   × And the range selector at position 2 has the lower thumb at position 10% # src\steps\components.rangeselector.steps.ts:59
       Error: expect(received).toBe(expected) // Object.is equality

       Expected: 10
       Received: 8
           at Proxy.<anonymous> (C:\Users\<USER>\source\repos\CCTC_Components\functional_scripts\node_modules\playwright\lib\matchers\expect.js:227:37)
           at CustomWorld.<anonymous> (C:\Users\<USER>\source\repos\CCTC_Components\functional_scripts\src\steps\components.rangeselector.steps.ts:77:44)
   - And the range selector at position 2 has the upper thumb at position 50% # src\steps\components.rangeselector.steps.ts:59
   - When the user drags the range selector at position 1 lower thumb to position 20% # src\steps\components.rangeselector.steps.ts:43
   - And the user drags the range selector at position 1 upper thumb to position 70% # src\steps\components.rangeselector.steps.ts:43
   - Then the range selector at position 2 has the lower thumb at position 10% # src\steps\components.rangeselector.steps.ts:59
   - And the range selector at position 2 has the upper thumb at position 50% # src\steps\components.rangeselector.steps.ts:59
   √ After # src\support\common-hooks.ts:85
       Attachment (text/plain): Status: FAILED. Duration:0s
       Attachment (text/plain): The screenshot attached below simply shows the whole page and is not an image for a specific locator
       Attachment (image/png)

7) Scenario: multiple vertical range selectors work independently # features\rangeselector\rangeselector_5.feature:27
   √ Before # src\support\common-hooks.ts:61
   √ Given the user is at the home page # src\steps\components.common.steps.ts:76
       Attachment (text/plain): [Browser Console] Checking if app readiness API is available
       Attachment (text/plain): [Browser Console] App readiness API found, waiting for app ready signal
       Attachment (text/plain): [CCTC_Components_UI] Waiting for app to become ready...
       Attachment (text/plain): [CCTC_Components_UI] Wait completed - app is now ready
       Attachment (text/plain): [CCTC_Components_UI] Application is now ready - 2025-08-27T11:32:33.403Z
       Attachment (text/plain): [Browser Console] App ready signal received
   √ And the user selects the "Range selector" component in the container "Input" # src\steps\components.common.steps.ts:89
   √ And the user clicks the "Usage" tab # src\steps\components.tabs.steps.ts:14
   √ And the user expands the concertina by clicking on the header with the text "Multiple Vertical Controls" # src\steps\components.concertina.steps.ts:45
   √ And the vertical range selector at position 1 has the lower thumb at position 80% # src\steps\components.rangeselector.steps.ts:82
   √ And the vertical range selector at position 1 has the upper thumb at position 37% # src\steps\components.rangeselector.steps.ts:82
   √ And the vertical range selector at position 2 has the lower thumb at position 70% # src\steps\components.rangeselector.steps.ts:82
   × And the vertical range selector at position 2 has the upper thumb at position 25% # src\steps\components.rangeselector.steps.ts:82
       Error: expect(received).toBe(expected) // Object.is equality

       Expected: 25
       Received: 11
           at Proxy.<anonymous> (C:\Users\<USER>\source\repos\CCTC_Components\functional_scripts\node_modules\playwright\lib\matchers\expect.js:227:37)
           at CustomWorld.<anonymous> (C:\Users\<USER>\source\repos\CCTC_Components\functional_scripts\src\steps\components.rangeselector.steps.ts:100:44)
   - And the vertical range selector at position 3 has the lower thumb at position 60% # src\steps\components.rangeselector.steps.ts:82
   - And the vertical range selector at position 3 has the upper thumb at position 10% # src\steps\components.rangeselector.steps.ts:82
   - When the user drags the range selector at position 1 lower thumb to position 30% # src\steps\components.rangeselector.steps.ts:43
   - And the user drags the range selector at position 1 upper thumb to position 70% # src\steps\components.rangeselector.steps.ts:43
   - Then the vertical range selector at position 2 has the lower thumb at position 70% # src\steps\components.rangeselector.steps.ts:82
   - And the vertical range selector at position 2 has the upper thumb at position 20% # src\steps\components.rangeselector.steps.ts:82
   - And the vertical range selector at position 3 has the lower thumb at position 60% # src\steps\components.rangeselector.steps.ts:82
   - And the vertical range selector at position 3 has the upper thumb at position 10% # src\steps\components.rangeselector.steps.ts:82
   - When the user drags the range selector at position 2 lower thumb to position 40% # src\steps\components.rangeselector.steps.ts:43
   - And the user drags the range selector at position 2 upper thumb to position 80% # src\steps\components.rangeselector.steps.ts:43
   - Then the vertical range selector at position 1 has the lower thumb at position 70% # src\steps\components.rangeselector.steps.ts:82
   - And the vertical range selector at position 1 has the upper thumb at position 30% # src\steps\components.rangeselector.steps.ts:82
   - And the vertical range selector at position 3 has the lower thumb at position 60% # src\steps\components.rangeselector.steps.ts:82
   - And the vertical range selector at position 3 has the upper thumb at position 10% # src\steps\components.rangeselector.steps.ts:82
   - When the user drags the range selector at position 3 lower thumb to position 25% # src\steps\components.rangeselector.steps.ts:43
   - And the user drags the range selector at position 3 upper thumb to position 85% # src\steps\components.rangeselector.steps.ts:43
   - Then the vertical range selector at position 1 has the lower thumb at position 70% # src\steps\components.rangeselector.steps.ts:82
   - And the vertical range selector at position 1 has the upper thumb at position 30% # src\steps\components.rangeselector.steps.ts:82
   - And the vertical range selector at position 2 has the lower thumb at position 60% # src\steps\components.rangeselector.steps.ts:82
   - And the vertical range selector at position 2 has the upper thumb at position 20% # src\steps\components.rangeselector.steps.ts:82
   √ After # src\support\common-hooks.ts:85
       Attachment (text/plain): Status: FAILED. Duration:0s
       Attachment (text/plain): The screenshot attached below simply shows the whole page and is not an image for a specific locator
       Attachment (image/png)

8) Scenario: the disabled range selector displays correct styling and prevents interaction # features\rangeselector\rangeselector_7.feature:3
   √ Before # src\support\common-hooks.ts:61
   √ Given the user is at the home page # src\steps\components.common.steps.ts:76
       Attachment (text/plain): [Browser Console] Checking if app readiness API is available
       Attachment (text/plain): [Browser Console] App readiness API found, waiting for app ready signal
       Attachment (text/plain): [CCTC_Components_UI] Waiting for app to become ready...
       Attachment (text/plain): [CCTC_Components_UI] Wait completed - app is now ready
       Attachment (text/plain): [CCTC_Components_UI] Application is now ready - 2025-08-27T11:32:45.064Z
       Attachment (text/plain): [Browser Console] App ready signal received
   √ And the user selects the "Range selector" component in the container "Input" # src\steps\components.common.steps.ts:89
   √ And the user clicks the "Usage" tab # src\steps\components.tabs.steps.ts:14
   √ When the user expands the concertina by clicking on the header with the text "Disabled State" # src\steps\components.concertina.steps.ts:45
   × Then the range selector is properly disabled # src\steps\components.rangeselector.steps.ts:445
       Error: expect(received).toBeLessThan(expected)

       Expected: < 1
       Received:   1
           at Proxy.<anonymous> (C:\Users\<USER>\source\repos\CCTC_Components\functional_scripts\node_modules\playwright\lib\matchers\expect.js:227:37)
           at CustomWorld.<anonymous> (C:\Users\<USER>\source\repos\CCTC_Components\functional_scripts\src\steps\components.rangeselector.steps.ts:463:38)
   - And the range selector appears disabled # src\steps\components.rangeselector.steps.ts:466
   - And the range selector thumbs cannot be moved # src\steps\components.rangeselector.steps.ts:471
   - And the disabled range selector component image matches the base image "disabled range selector" # src\steps\components.rangeselector.steps.ts:482
   √ After # src\support\common-hooks.ts:85
       Attachment (text/plain): Status: FAILED. Duration:0s
       Attachment (text/plain): The screenshot attached below simply shows the whole page and is not an image for a specific locator
       Attachment (image/png)

9) Scenario: the disabled range selector does not respond to mouse interaction # features\rangeselector\rangeselector_7.feature:13
   √ Before # src\support\common-hooks.ts:61
   √ Given the user is at the home page # src\steps\components.common.steps.ts:76
       Attachment (text/plain): [Browser Console] Checking if app readiness API is available
       Attachment (text/plain): [Browser Console] App readiness API found, waiting for app ready signal
       Attachment (text/plain): [CCTC_Components_UI] Waiting for app to become ready...
       Attachment (text/plain): [CCTC_Components_UI] Wait completed - app is now ready
       Attachment (text/plain): [CCTC_Components_UI] Application is now ready - 2025-08-27T11:32:49.827Z
       Attachment (text/plain): [Browser Console] App ready signal received
   √ And the user selects the "Range selector" component in the container "Input" # src\steps\components.common.steps.ts:89
   √ And the user clicks the "Usage" tab # src\steps\components.tabs.steps.ts:14
   √ And the user expands the concertina by clicking on the header with the text "Disabled State" # src\steps\components.concertina.steps.ts:45
   × And the range selector is properly disabled # src\steps\components.rangeselector.steps.ts:445
       Error: expect(received).toBeLessThan(expected)

       Expected: < 1
       Received:   1
           at Proxy.<anonymous> (C:\Users\<USER>\source\repos\CCTC_Components\functional_scripts\node_modules\playwright\lib\matchers\expect.js:227:37)
           at CustomWorld.<anonymous> (C:\Users\<USER>\source\repos\CCTC_Components\functional_scripts\src\steps\components.rangeselector.steps.ts:463:38)
   - When the user attempts to drag the disabled lower thumb # src\steps\components.rangeselector.steps.ts:141
   - Then the disabled thumb does not move # src\steps\components.rangeselector.steps.ts:476
   √ After # src\support\common-hooks.ts:85
       Attachment (text/plain): Status: FAILED. Duration:0s
       Attachment (text/plain): The screenshot attached below simply shows the whole page and is not an image for a specific locator
       Attachment (image/png)

18 scenarios (9 failed, 9 passed)
173 steps (9 failed, 57 skipped, 107 passed)
3m16.327s (executing steps: 3m15.872s)
