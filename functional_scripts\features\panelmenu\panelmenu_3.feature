@component @panelmenu @panelmenu_3
Feature: when clicked the panel menu navigates to the supplied path
    <PERSON><PERSON><PERSON>: the selected item is displayed beneath the panel menu
        Given the user is at the home page
        And the user selects the "Panel menu" component in the container "Panel menu"
        When the user selects the "menuitem without header" Panel menu item
        Then the element below the panel menu shows "MenuItem1" as selected
        And the selected panel menu text matches the base image "selected shown"