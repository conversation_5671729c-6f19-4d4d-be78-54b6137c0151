﻿using CCTC_Components.Components.__CCTC.Models;
using CCTC_Components.Components.Lister;
using CCTC_Components.Components.TextBox;
using CCTC_Lib.Contracts.Data;
using CCTC_Lib.Enums.Data;
using CCTC_Lib.Models.UI;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Web;
using Xunit.Abstractions;
using SortDir = CCTC_Lib.Enums.Data.SortDir;

namespace CCTC_Components.bUnit.test;

public class ListerTests : CCTCComponentsTestContext
{
    const string DataHasFocusAttr = "data-has-focus";


    public ListerTests(ITestOutputHelper output) : base(output)
    {
        Services.AddTestSchedulers();
        AddGetElementBoundRectFromQuery(EmptyBoundingRect());
        AddSetFocusOnSelector();
    }

    #region TestData

    BoundingRect EmptyBoundingRect() => new (0, 0, 0, 0, 0, 0, 0, 0);

    public class TestListerItem : IEquatable<TestListerItem>, ISearchable, IUniqueIdentity<string>, ISortable
    {
        public TestListerItem()
        {
            Urn = Guid.NewGuid();
            SomeText = "";
        }

        public TestListerItem(Guid urn)
        {
            Urn = urn;
            SomeText = "";
        }

        public Guid Urn { get; }

        public int Id { get; init; }
        public Guid SomeGuid { get; init; }

        public string SomeText { get; set; }

        public bool Equals(TestListerItem? other)
        {
            if (ReferenceEquals(null, other)) return false;
            if (ReferenceEquals(this, other)) return true;
            return Id == other.Id && SomeGuid.Equals(other.SomeGuid);
        }

        public override bool Equals(object? obj)
        {
            if (ReferenceEquals(null, obj)) return false;
            if (ReferenceEquals(this, obj)) return true;
            if (obj.GetType() != GetType()) return false;
            return Equals((TestListerItem)obj);
        }

        public override int GetHashCode()
        {
            return HashCode.Combine(Id, SomeGuid);
        }

        public string SearchText()
        {
            return $"{Id} - {SomeGuid} - {SomeText}";
        }

        public static string SortBy { get; set; } = "SomeText";
        public static SortDir SortDir { get; set; } = SortDir.Asc;

        public string Key => Urn.ToString();
    }

    static List<TestListerItem> GetTestCollection()
    {
        var ret = new List<TestListerItem>();
        for (var i = 0; i < 100; i++)
        {
            ret.Add(new TestListerItem { Id = i, SomeGuid = Guid.NewGuid(), SomeText = $"some {i}" });
        }

        return ret;
    }

    static void SetSortParameters(string sortBy, SortDir sortDir)
    {
        TestListerItem.SortBy = sortBy;
        TestListerItem.SortDir = sortDir;
    }

    static List<TestListerItem> GetTestCollectionWithNonUniqueUrns()
    {
        var ret = new List<TestListerItem>();
        for (var i = 0; i < 100; i++)
        {
            ret.Add(new TestListerItem(new Guid()) { Id = i, SomeGuid = Guid.NewGuid(), SomeText = $"some {i}" });
        }

        return ret;
    }

    static List<TestListerItem> GetTestCollectionOrderedBySomeTextAsc(List<TestListerItem> testItems)
    {
        //Note: not using OrderByPropertyOrField extension method here - just use .NET Linq for getting expected ordered data
        return testItems.OrderBy(x => x.SomeText).ToList();
    }

    public class TestItemsService : IItemsService<TestListerItem>
    {
        public int ObservableThrottleMs { get; set; }

        public Task<(int totalItems, IEnumerable<TestListerItem> items)> GetItemsAsync(int startIndex, int numItems, CancellationToken token)
        {
            throw new NotImplementedException();
        }

        public Task<(int totalItems, IEnumerable<TestListerItem> items)> GetItemsAsync(int startIndex, int numItems)
        {
            throw new NotImplementedException();
        }

        public Task<(int totalItems, IEnumerable<TestListerItem> items)> GetItemsAsync(int startIndex, int numItems,
            string sortBy, SortDir sortDirection, CancellationToken token)
        {
            throw new NotImplementedException();
        }

        public Task<(int totalItems, IEnumerable<TestListerItem> items)> GetItemsAsync(int startIndex, int numItems,
            string sortBy, SortDir sortDirection)
        {
            return Task.FromResult((10, Enumerable.Empty<TestListerItem>()));
        }

        public Task<(int totalItems, int filteredItems, IEnumerable<TestListerItem> items)> GetItemsAsync(string filter,
            FilterComparisonType filterComparisonType, int startIndex, int numItems,
            string sortBy, SortDir sortDirection, CancellationToken token)
        {
            throw new NotImplementedException();
        }

        public Task<(int totalItems, int filteredItems, IEnumerable<TestListerItem> items)> GetItemsAsync(string filter, FilterComparisonType filterComparisonType,
            int startIndex, int numItems, string sortBy, SortDir sortDirection)
        {
            throw new NotImplementedException();
        }

        public CancellationToken CancellationToken { get; set; }

    }

    #endregion TestData

    [Fact]
    public void InitialisingWithNoDataSourceThrowsException()
    {

        Assert.Throws<InvalidOperationException>(() => RenderComponent<Lister<TestListerItem>>());
    }

    [Fact]
    public void InitialisingWithBothDataAndItemsServiceThrowsException()
    {

        var testData = GetTestCollection();

        Assert.Throws<InvalidOperationException>(() => RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Data, testData)
            .Add(p => p.ItemsService, new TestItemsService())
        ));
    }

    [Fact]
    public void InitialisingWithBothItemsServiceAndListerLoadingThrowsException()
    {

        Assert.Throws<InvalidOperationException>(() => RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.ItemsService, new TestItemsService())
            .Add(p => p.ListerLoadingFunc, _ => Task.FromResult(new List<TestListerItem>())))
        );
    }

    [Fact]
    public void InitialisingWithBothDataAndListerLoadingThrowsException()
    {

        var testData = GetTestCollection();
        Assert.Throws<InvalidOperationException>(() => RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Data, testData)
            .Add(p => p.ListerLoadingFunc, _ => Task.FromResult(new List<TestListerItem>())))
        );
    }

    [Fact]
    public void InitialisingWithPreLoadedDataAndCancellationThrowsException()
    {

        var testData = GetTestCollection();
        Assert.Throws<InvalidOperationException>(() => RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Data, testData)
            .Add(p => p.CanCancelThenRefresh, true)
            .Add(p => p.ListerLoadingFunc, _ => Task.FromResult(new List<TestListerItem>())))
        );
    }

    [Fact]
    public void InitialisingWithVirtualizeFalseDoesNotUseVirtualizeComponent()
    {

        var testData = GetTestCollection();
        var cut = RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, "not-virt")
            .Add(p => p.Data, testData)
            .Add(p => p.Template, item => $"<span>{item.Id}</span>")
            .Add(p => p.Virtualize, false)
        );

        cut.WaitForElement(".item-row");
        var markUp = cut.Markup;
        Assert.Contains("not-virt-no-virtualize", markUp);
    }

    [Fact]
    public void InitialisingWithVirtualizeTrueDoesUseVirtualizeComponent()
    {

        var testData = GetTestCollection();
        var cut = RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, "with-virt")
            .Add(p => p.Data, testData)
            .Add(p => p.Template, item => $"<span id='item'>{item.Id}</span>")
            .Add(p => p.Virtualize, true)
        );

        cut.WaitForElement("#with-virt-virtualize");
        var markUp = cut.Markup;
        Assert.Contains("""with-virt-virtualize""", markUp);
    }

    [Fact]
    public void ItemOnClickItemIsRaisedWhenCanClickNotGiven()
    {
        AddScrollIntoViewFromQuery();
        var testData = GetTestCollection().OrderBy(x => x.SomeText).ToList();

        (int index, bool isSelected, TestListerItem data) eventCallBack = new();
        const string id = "some-name";

        var cut = RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.Data, testData)
            .Add(p => p.Template, item => $"<span id='item'>{item.Id}</span>")
            .Add(p => p.Virtualize, false)
            .Add(p => p.OnClickedItem, data => eventCallBack = data)
        );

        cut.WaitForElement(".item-row");
        cut.WaitForAssertion(() => cut.Find(".item-row")); ;
        var item = cut.Find($"#{id}-item-row-2");
        item.Click(new MouseEventArgs());

        Assert.Equal(2, eventCallBack.index);
        Assert.Equal(false, eventCallBack.isSelected);
        Assert.Equal(testData.Find(x => x.SomeText == "some 10")?.SomeGuid, eventCallBack.data?.SomeGuid);
    }

    [Fact]
    public void ItemOnClickItemIsNotRaisedWhenCanClickGivenAndFails()
    {
        AddScrollIntoViewFromQuery();
        var testData = GetTestCollection();

        (int index, bool isSelected, TestListerItem data) eventCallBack = new(-1, false, new TestListerItem());
        const string id = "some-name";
        var cut = RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.Data, testData)
            .Add(p => p.Template, item => $"<span id='item'>{item.Id}</span>")
            .Add(p => p.Virtualize, false)
            .Add(p => p.CanClick, item => item.Id == 99)
            .Add(p => p.OnClickedItem, data => eventCallBack = data)
        );

        cut.WaitForElement(".item-row");
        cut.Render();
        var item = cut.Find($"#{id}-item-row-2");
        item.Click(new MouseEventArgs());

        Assert.Equal(-1, eventCallBack.index);
    }

    [Fact]
    public async Task ItemOnClickItemIsRaisedWhenCanClickGivenAndPasses()
    {
        AddScrollIntoViewFromQuery();
        var testData = GetTestCollection().OrderBy(x => x.SomeText).ToList();

        (int index, bool isSelected, TestListerItem data) eventCallBack = new(-1, false, new TestListerItem());

        const string id = "some-name";
        var cut = RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.Data, testData)
            .Add(p => p.Template, item => $"<span id='item'>{item.Id}</span>")
            .Add(p => p.Virtualize, false)
            .Add(p => p.CanClick, item => item.SomeText == "some 10")
            .Add(p => p.OnClickedItem, data => eventCallBack = data)
        );

        cut.WaitForElement(".item-row");

        var item = cut.Find($"#{id}-item-row-2");
        await cut.InvokeAsync(() => item.Click(new MouseEventArgs()));

        Assert.Equal(2, eventCallBack.index);
    }

    [Fact]
    public void CheckboxOnclickStopPropagationEnabled()
    {

        var testData = GetTestCollection();

        const string id = "some-name";
        var cut = RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.Data, testData)
            .Add(p => p.Template, item => $"<span id='item'>{item.Id}</span>")
            .Add(p => p.SelectionType, ListerSelectionType.Multiple)
        );

        cut.WaitForElement(".item-row");
        var checkBoxMarkup = cut.Find($"#{id}-select-{2}").OuterHtml;

        Assert.Contains("blazor:onclick:stoppropagation", checkBoxMarkup);
    }

    [Theory]
    [InlineData(ListerSelectionType.Multiple, 2, 2, 2, true, 2, true, 1, 10)]
    [InlineData(ListerSelectionType.Multiple, 2, 2, 2, false, 2, false, 0, null)]
    [InlineData(ListerSelectionType.Multiple, 2, 3, 3, true, -1, false, 0, null)]
    [InlineData(ListerSelectionType.Multiple, 2, 3, 3, false, -1, false, 0, null)]
    [InlineData(ListerSelectionType.Multiple, 2, 3, 2, true, 2, false, 0, null)]
    [InlineData(ListerSelectionType.Multiple, 2, 3, 2, false, 2, false, 0, null)]
    [InlineData(ListerSelectionType.None, 2, 2, 2, true, 2, false, 0, null)]
    [InlineData(ListerSelectionType.None, 2, 2, 2, false, 2, false, 0, null)]
    public void SelectionWithItemClickFunctionsCorrectly(ListerSelectionType selectionType, int clickedItemIndex, int canSelectIndex, int canClickIndex,
        bool? allowSelectionWithItemClick, int onClickedItemExpectedIndex, bool expectedIsSelected, int expectedSelectionCount, int? onSelectedChangedExpectedIndex)
    {
        AddGetElementBoundRectFromQuery(EmptyBoundingRect());
        AddScrollIntoViewFromQuery();

        var testData = GetTestCollection().OrderBy(x => x.SomeText).ToList();
        (int index, bool isSelected, TestListerItem data) eventCallBack = new(-1, false, new TestListerItem());
        List<TestListerItem> newSelection = new();
        const string id = "some-name";
        var cut = RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.Data, testData)
            .Add(p => p.Template, item => $"<span id='item'>{item.Id}</span>")
            .Add(p => p.OnSelectedChanged, data => newSelection = data)
            .Add(p => p.Virtualize, false)
            .Add(p => p.CanSelect, item => testData.IndexOf(item) == canSelectIndex)
            .Add(p => p.CanClick, item => testData.IndexOf(item) == canClickIndex)
            .Add(p => p.OnClickedItem, data => eventCallBack = data)
            .Add(p => p.AllowSelectionWithItemClick, allowSelectionWithItemClick)
            .Add(p => p.SelectionType, selectionType)
        );

        cut.WaitForElement(".item-row");
        var item = cut.Find($"#{id}-item-row-{clickedItemIndex}");
        item.Click(new MouseEventArgs());
        cut.Render();

        cut.WaitForAssertion(() => cut.Find($"#{id}-item-row-{clickedItemIndex}"));

        Assert.Equal(onClickedItemExpectedIndex, eventCallBack.index);
        Assert.Equal(expectedIsSelected, eventCallBack.isSelected);
        Assert.Equal(expectedSelectionCount, newSelection.Count);
        Assert.Equal(onSelectedChangedExpectedIndex, newSelection.FirstOrDefault()?.Id);
    }

    [Fact]
    public void OnLoadCompleteFiresForNonVirt()
    {

        var testData = GetTestCollection();

        const string id = "some-name";
        int? countLoaded = null;
        int? startIndex = null;
        int? numItems = null;

        var cut = RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.Data, testData)
            .Add(p => p.Template, item => $"<span id='item'>{item.Id}</span>")
            .Add(p => p.Virtualize, false)
            .Add(p => p.OnLoadComplete, callback =>
            {
                countLoaded = callback.totalCount;
                startIndex = callback.startIndex;
                numItems = callback.numItems;
            })
        );

        cut.WaitForElement($"#{id}-item-row-99");

        Assert.Equal(100, countLoaded);
        Assert.Null(startIndex);
        Assert.Null(numItems);
    }

    [Fact]
    public void OnLoadCompleteFiresForLoadingFunc()
    {

        var testData = GetTestCollection();

        const string id = "some-name";
        int? countLoaded = null;
        int? startIndex = null;
        int? numItems = null;

        var cut = RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.ListerLoadingFunc, _ => Task.FromResult(testData))
            .Add(p => p.Template, item => $"<span id='item'>{item.Id}</span>")
            .Add(p => p.Virtualize, true)
            .Add(p => p.OnLoadComplete, callback =>
            {
                countLoaded = callback.totalCount;
                startIndex = callback.startIndex;
                numItems = callback.numItems;
            })
        );

        cut.WaitForElement($"#{id}-item-row-99");

        Assert.Equal(100, countLoaded);
        Assert.Null(startIndex);
        Assert.Null(numItems);
    }

    [Fact]
    public void CountsCorrectForLoadingFunc()
    {

        var testData = GetTestCollection();

        const string id = "some-name";
        (int? total, int? selected, int? included) allCounts = (null, null, null);

        var cut = RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.ListerLoadingFunc, _ => Task.FromResult(testData))
            .Add(p => p.Template, item => $"<span id='item'>{item.Id}</span>")
            .Add(p => p.Virtualize, true)
            .Add(p => p.CurrentCounts, callback => allCounts = callback)
        );

        cut.WaitForElement($"#{id}-item-row-99");

        Assert.Equal((testData.Count, null, testData.Count), allCounts);
    }

    [Fact]
    public void CountsCorrectForNonVirt()
    {

        var testData = GetTestCollection();

        const string id = "some-name";
        (int? total, int? selected, int? included) allCounts = (null, null, null);

        var cut = RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.Data, testData)
            .Add(p => p.Template, item => $"<span id='item'>{item.Id}</span>")
            .Add(p => p.Virtualize, false)
            .Add(p => p.CurrentCounts, callback => allCounts = callback)
        );

        cut.WaitForElement($"#{id}-item-row-99");

        Assert.Equal((testData.Count, null, testData.Count), allCounts);
    }

    [Fact]
    public void OnLoadCompleteFiresForAllDataVirt()
    {

        var testData = GetTestCollection();

        const string id = "some-name";
        int? countLoaded = null;
        int? startIndex = null;
        int? numItems = null;

        var cut = RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.Data, testData)
            .Add(p => p.Template, item => $"<span id='item'>{item.Id}</span>")
            .Add(p => p.Virtualize, true)
            .Add(p => p.OnLoadComplete, callback =>
                {
                    countLoaded = callback.totalCount;
                    startIndex = callback.startIndex;
                    numItems = callback.numItems;
                })
        );

        cut.WaitForElement($"#{id}-item-row-99");

        Assert.Equal(100, countLoaded);
        Assert.Null(startIndex);
        Assert.Null(numItems);
    }

    [Fact]
    public void CountsCorrectForAllDataVirt()
    {

        var testData = GetTestCollection();

        const string id = "some-name";
        (int? total, int? selected, int? included) allCounts = (null, null, null);

        var cut = RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.Data, testData)
            .Add(p => p.Template, item => $"<span id='item'>{item.Id}</span>")
            .Add(p => p.Virtualize, true)
            .Add(p => p.CurrentCounts, callback => allCounts = callback)
        );

        cut.WaitForElement($"#{id}-item-row-99");

        Assert.Equal((testData.Count, null, testData.Count), allCounts);
    }

    [Fact]
    public void OnLoadCompleteFiresForDataProviderVirt()
    {
        var testSchedulers = new TestSchedulers();
        Services.AddTestSchedulers(sp => testSchedulers);
        var testData = GetTestCollection();

        const string id = "some-name";
        int? countLoaded = null;
        int? startIndex = null;
        int? numItems = null;

        var mockItemsService = new Mock<IItemsService<TestListerItem>>();
        mockItemsService.Setup(x =>
            x.GetItemsAsync(It.IsAny<int>(), It.IsAny<int>(), It.IsAny<string>(), It.IsAny<SortDir>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync((10, testData));
        mockItemsService.SetupGet(x => x.ObservableThrottleMs).Returns(500);

        var cut = RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.ItemSizePixels, 50)
            .Add(p => p.OverscanCount, 20)
            .Add(p => p.ItemsService, mockItemsService.Object)
            .Add(p => p.Template, item => $"<span id='item'>{item.Id}</span>")
            .Add(p => p.Virtualize, true)
            .Add(p => p.OnLoadComplete, callback =>
            {
                countLoaded = callback.totalCount;
                startIndex = callback.startIndex;
                numItems = callback.numItems;
            })
        );

        testSchedulers.Default.AdvanceBy(TimeSpan.FromMilliseconds(mockItemsService.Object.ObservableThrottleMs).Ticks);

        //cut.WaitForElement($"#{name}-item-row-9");

        Assert.Equal(10, countLoaded);
        Assert.NotNull(startIndex);
        Assert.NotNull(numItems);
    }

    [Fact]
    public void CountsCorrectForDataProviderVirt()
    {
        var testSchedulers = new TestSchedulers();
        Services.AddTestSchedulers(sp => testSchedulers);
        var testData = GetTestCollection();

        const string id = "some-name";
        (int? total, int? selected, int? included) allCounts = (null, null, null);

        var fakeTotal = testData.Count;

        var mockItemsService = new Mock<IItemsService<TestListerItem>>();
        mockItemsService.Setup(x =>
                x.GetItemsAsync(It.IsAny<int>(), It.IsAny<int>(), It.IsAny<string>(), It.IsAny<SortDir>(),
                    It.IsAny<CancellationToken>()))
            .ReturnsAsync((fakeTotal, testData));
        mockItemsService.SetupGet(x => x.ObservableThrottleMs).Returns(1000);

        var cut = RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.Template, item => $"<span id='item'>{item.Id}</span>")
            .Add(p => p.Virtualize, true)
            .Add(p => p.ItemsService, mockItemsService.Object)
            .Add(p => p.ItemSizePixels, 50)
            .Add(p => p.OverscanCount, 20)
            .Add(p => p.CurrentCounts, callback => allCounts = callback)
        );

        testSchedulers.Default.AdvanceBy(TimeSpan.FromMilliseconds(mockItemsService.Object.ObservableThrottleMs).Ticks);

        cut.WaitForAssertion(() =>
        {
            Assert.Equal((fakeTotal, null, fakeTotal), allCounts);
        });
    }

    [Fact]
    public void EmptyListShowsTemplateWhenGiven()
    {

        const string id = "some-name";

        var content = "empty list fragment";
        RenderFragment emptyListTemplate = builder =>
        {
            builder.OpenElement(0, "div");
            builder.AddAttribute(1, "loaded");
            builder.AddContent(2, new MarkupString(content));
            builder.CloseElement();
        };

        var cut = RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.Data, new List<TestListerItem>())
            .Add(p => p.Template, item => $"<span id='item'>{item.Id}</span>")
            .Add(p => p.Virtualize, false)
            .Add(p => p.EmptyListTemplate, emptyListTemplate)
        );

        cut.WaitForAssertion(() => cut.Find("[loaded]"));
        Assert.Contains(content, cut.Markup);
    }

    [Fact]
    public void ItemShowsDeleteIconWhenCanDeleteItems()
    {

        const string id = "some-name";
        var testData = GetTestCollection();

        var cut = RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.Data, testData)
            .Add(p => p.Template, item => $"<span id='item'>{item.Id}</span>")
            .Add(p => p.Virtualize, false)
            .Add(p => p.CanDeleteItems, true)
        );

        cut.WaitForAssertion(() => cut.Find($"#{id}-item-row-3"));

        var del = cut.Find($"#{id}-delete-3");
        Assert.NotNull(del);
    }

    [Fact]
    public void ItemShowsMoveIconsWhenCanReorderItems()
    {

        const string id = "some-name";
        var testData = GetTestCollection();

        var cut = RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.Data, testData)
            .Add(p => p.Template, item => $"<span id='item'>{item.Id}</span>")
            .Add(p => p.Virtualize, false)
            .Add(p => p.CanReOrder, true)
        );

        cut.WaitForAssertion(() => cut.Find($"#{id}-item-row-3"));

        var moveUp = cut.Find($"#{id}-moveup-3");
        Assert.NotNull(moveUp);
        var moveDown = cut.Find($"#{id}-movedown-3");
        Assert.NotNull(moveDown);
    }

    [Fact]
    public void ItemDoesNotShowMoveUpIconsWhenCanReorderItemsButIndex0()
    {

        const string id = "some-name";
        var testData = GetTestCollection();

        var cut = RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.Data, testData)
            .Add(p => p.Template, item => $"<span id='item'>{item.Id}</span>")
            .Add(p => p.Virtualize, false)
            .Add(p => p.CanReOrder, true)
        );

        cut.WaitForAssertion(() => cut.Find($"#{id}-item-row-3"));

        Assert.DoesNotContain($"{id}-moveup-0", cut.Markup);
    }

    [Fact]
    public void ItemDoesNotShowMoveDownIconsWhenCanReorderItemsButLastItem()
    {

        const string id = "some-name";
        var testData = GetTestCollection();

        var cut = RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.Data, testData)
            .Add(p => p.Template, item => $"<span id='item'>{item.Id}</span>")
            .Add(p => p.Virtualize, false)
            .Add(p => p.CanReOrder, true)
        );

        cut.WaitForAssertion(() => cut.Find($"#{id}-item-row-3"));

        var last = testData.MaxBy(x => x.Id)?.Id;

        Assert.DoesNotContain($"{id}-movedown-{last}", cut.Markup);
    }

    //deleting

    [Fact]
    public void DeletingItemsMaintainsIntegrityOfListerForNonVirt()
    {
        const string id = "some-name";
        var testData = GetTestCollection();
        (int index, TestListerItem? item) deletedItem = (-1, null);
        (int? total, int? selected, int? included) allCounts = (null, null, null);

        var cut = RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.Data, testData)
            .Add(p => p.Template, item => $"<span id='item'>{item.Id}</span>")
            .Add(p => p.Virtualize, false)
            .Add(p => p.CanDeleteItems, true)
            .Add(p => p.OnItemDeleted, callback => deletedItem = callback)
            .Add(p => p.CurrentCounts, callback => allCounts = callback)
        );

        cut.WaitForAssertion(() => cut.Find($"#{id}-item-row-99"));
        Assert.Equal(-1, deletedItem.index);
        Assert.Null(deletedItem.item);
        Assert.Equal((testData.Count, null, testData.Count), allCounts);

        //try delete item 25
        var deleteId = $"{id}-delete-25";
        var delDiv = cut.Find($"#{deleteId}");
        delDiv.Click();

        cut.Render();
        cut.WaitForAssertion(() => cut.Find($"#{id}-item-row-20"));

        Assert.Equal(25, deletedItem.index);
        Assert.Equal("some 25", testData.Find(x => x.Id == 25)?.SomeText);
        Assert.Equal((testData.Count - 1, null, testData.Count - 1), allCounts);
    }

    [Fact]
    public void DeletingItemsMaintainsIntegrityOfListerAllDataVirt()
    {

        const string id = "some-name";
        var testData = GetTestCollection().OrderBy(x => x.SomeText).ToList();
        (int index, TestListerItem? item) deletedItem = (-1, null);
        (int? total, int? selected, int? included) allCounts = (null, null, null);

        var cut = RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.Data, testData)
            .Add(p => p.Template, item => $"<span id='item'>{item.Id}</span>")
            .Add(p => p.Virtualize, true)
            .Add(p => p.CanDeleteItems, true)
            .Add(p => p.OnItemDeleted, callback => deletedItem = callback)
            .Add(p => p.CurrentCounts, callback => allCounts = callback)
        );

        cut.WaitForAssertion(() => cut.Find($"#{id}-item-row-99"));
        Assert.Equal(-1, deletedItem.index);
        Assert.Null(deletedItem.item);
        Assert.Equal((testData.Count, null, testData.Count), allCounts);

        //try delete item 25
        var deleteId = $"{id}-delete-25";
        var delDiv = cut.Find($"#{deleteId}");
        delDiv.Click();

        Assert.Equal(25, deletedItem.index);
        Assert.Equal("some 25", testData.Find(x => x.Id == 25)?.SomeText);
        Assert.Equal((testData.Count - 1, null, testData.Count - 1), allCounts);
    }

    [Fact]
    public void DeletingItemsForDataProviderVirtNotPermitted()
    {

        const string id = "some-name";
        var testData = GetTestCollection();

        var mockItemsService = new Mock<IItemsService<TestListerItem>>();
        mockItemsService.Setup(x =>
                x.GetItemsAsync(It.IsAny<int>(), It.IsAny<int>(), It.IsAny<string>(), It.IsAny<SortDir>()))
            .ReturnsAsync((testData.Count, testData));

        Assert.Throws<InvalidOperationException>(() => RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.ItemsService, mockItemsService.Object)
            .Add(p => p.CanDeleteItems, true)
        ));
    }

    [Fact]
    public void MoveUpAndDownWorksNonVirt()
    {

        const string id = "some-name";
        var testData = GetTestCollection().Take(10).OrderBy(x => x.Id).ToList();

        var orderCallBack = new List<TestListerItem>();

        var cut = RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.Data, testData)
            .Add(p => p.Template, item => $"<span id='item'>item {item.Id}</span>")
            .Add(p => p.Virtualize, false)
            .Add(p => p.CanReOrder, true)
            .Add(p => p.OnOrderedDataChanged, callback => orderCallBack = callback)
        );

        cut.WaitForAssertion(() => cut.Find($"#{id}-item-row-9"));

        //check current correct
        var item2 = cut.Find($"#{id}-item-row-1");
        Assert.Contains("""<span id="item">item 1</span""", item2.ToMarkup());
        var item3 = cut.Find($"#{id}-item-row-2");
        Assert.Contains("""<span id="item">item 2</span""", item3.ToMarkup());

        //move number 3 up to 2
        var item3Icon = cut.Find($"#{id}-moveup-2");
        item3Icon.Click();

        var item2Now = cut.Find($"#{id}-item-row-1");
        Assert.Contains("""<span id="item">item 2</span""", item2Now.ToMarkup());
        var item3Now = cut.Find($"#{id}-item-row-2");
        Assert.Contains("""<span id="item">item 1</span""", item3Now.ToMarkup());

        //check order callback correct
        //item 2 is the 3rd item but has been swapped into position 2 (index 1)
        Assert.Equal("some 2", orderCallBack[1].SomeText);
        Assert.Equal("some 1", orderCallBack[2].SomeText);

        //now move back down i.e. move number 2 up to 3
        var item2Icon = cut.Find($"#{id}-movedown-1");
        item2Icon.Click();

        item2Now = cut.Find($"#{id}-item-row-1");
        Assert.Contains("""<span id="item">item 1</span""", item2Now.ToMarkup());
        item3Now = cut.Find($"#{id}-item-row-2");
        Assert.Contains("""<span id="item">item 2</span""", item3Now.ToMarkup());

        //check order callback correct
        //item 2 and 3 back in original position
        Assert.Equal("some 1", orderCallBack[1].SomeText);
        Assert.Equal("some 2", orderCallBack[2].SomeText);
    }

    [Fact]
    public void MoveUpAndDownWorksAllDataVirt()
    {

        const string id = "some-name";
        var testData = GetTestCollection().Take(10).OrderBy(x => x.Id).ToList();

        var orderCallBack = new List<TestListerItem>();

        var cut = RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.Data, testData)
            .Add(p => p.Template, item => $"<span id='item'>item {item.Id}</span>")
            .Add(p => p.Virtualize, true)
            .Add(p => p.CanReOrder, true)
            .Add(p => p.OnOrderedDataChanged, callback => orderCallBack = callback)
        );

        cut.WaitForAssertion(() => cut.Find($"#{id}-item-row-9"));

        //check current correct
        var item2 = cut.Find($"#{id}-item-row-1");
        Assert.Contains("""<span id="item">item 1</span""", item2.ToMarkup());
        var item3 = cut.Find($"#{id}-item-row-2");
        Assert.Contains("""<span id="item">item 2</span""", item3.ToMarkup());

        //move number 3 up to 2
        var item3Icon = cut.Find($"#{id}-moveup-2");
        item3Icon.Click();

        cut.WaitForAssertion(() => cut.Find($"#{id}-item-row-1"));

        cut.WaitForAssertion(() => cut.Find($"#{id}-item-row-1"));

        var item2Now = cut.Find($"#{id}-item-row-1");
        Assert.Contains("""<span id="item">item 2</span""", item2Now.ToMarkup());
        var item3Now = cut.Find($"#{id}-item-row-2");
        Assert.Contains("""<span id="item">item 1</span""", item3Now.ToMarkup());

        //check order callback correct
        //item 2 is the 3rd item but has been swapped into position 2 (index 1)
        Assert.Equal("some 2", orderCallBack[1].SomeText);
        Assert.Equal("some 1", orderCallBack[2].SomeText);

        //now move back down i.e. move number 2 up to 3
        var item2Icon = cut.Find($"#{id}-movedown-1");
        item2Icon.Click();

        item2Now = cut.Find($"#{id}-item-row-1");
        Assert.Contains("""<span id="item">item 1</span""", item2Now.ToMarkup());
        item3Now = cut.Find($"#{id}-item-row-2");
        Assert.Contains("""<span id="item">item 2</span""", item3Now.ToMarkup());

        //check order callback correct
        //item 2 and 3 back in original position
        Assert.Equal("some 1", orderCallBack[1].SomeText);
        Assert.Equal("some 2", orderCallBack[2].SomeText);
    }

    [Fact]
    public void ReorderingItemsForDataProviderVirtNotPermitted()
    {

        const string id = "some-name";
        var testData = GetTestCollection();

        var mockItemsService = new Mock<IItemsService<TestListerItem>>();
        mockItemsService.Setup(x =>
                x.GetItemsAsync(It.IsAny<int>(), It.IsAny<int>(), It.IsAny<string>(), It.IsAny<SortDir>()))
            .ReturnsAsync((testData.Count, testData));

        Assert.Throws<InvalidOperationException>(() => RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.ItemsService, mockItemsService.Object)
            .Add(p => p.CanReOrder, true)
        ));
    }

    const int ComponentDefaultThrottleMs = 2000;

    [Fact]
    public async Task FilterNonVirtSuccessful()
    {
        var testSchedulers = new TestSchedulers();
        Services.AddTestSchedulers(sp => testSchedulers);

        const string id = "some-name";
        var testData = GetTestCollection();
        (int? total, int? selected, int? included) allCounts = (null, null, null);
        var filteredList = new List<TestListerItem>();

        var cut = RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.Data, testData)
            .Add(p => p.Template, item => $"<span id='item'>{item.Id}</span>")
            .Add(p => p.Virtualize, false)
            .Add(p => p.CanFilter, true)
            .Add(p => p.OnFilteredItems, callback => filteredList = callback)
            .Add(p => p.CurrentCounts, callback => allCounts = callback)
        );

        cut.WaitForAssertion(() => cut.HasComponent<Text>());

        //apply the search text
        var textInput = cut.FindComponent<Text>();
        await textInput.InvokeAsync(() =>
        {
            textInput.Find("input").Input("some 2");
            testSchedulers.Default.AdvanceBy(TimeSpan.FromMilliseconds(ComponentDefaultThrottleMs).Ticks);
        });

        //expect 11 which is item 'some 2' and items 'some 20', 'some 21' etc.
        Assert.Equal(11, filteredList.Count);
        Assert.Equal((100, null, 11), allCounts);
    }

    [Fact]
    public async Task FilterAllDataVirtSuccessful()
    {
        var testSchedulers = new TestSchedulers();
        Services.AddTestSchedulers(sp => testSchedulers);

        const string id = "some-name";
        var testData = GetTestCollection();
        (int? total, int? selected, int? included) allCounts = (null, null, null);
        var filteredList = new List<TestListerItem>();

        var cut = RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.Data, testData)
            .Add(p => p.Template, item => $"<span id='item'>{item.Id}</span>")
            .Add(p => p.Virtualize, true)
            .Add(p => p.CanFilter, true)
            .Add(p => p.CountDisplay, ListerCountDisplay.FilterAndTotal)
            .Add(p => p.OnFilteredItems, callback => filteredList = callback)
            .Add(p => p.CurrentCounts, callback => allCounts = callback)
            .Add(p => p.CountDisplayPostText, "some further text")
        );

        cut.WaitForAssertion(() => cut.HasComponent<Text>());

        //apply the search text
        var textInput = cut.FindComponent<Text>();
        await textInput.InvokeAsync(() =>
        {
            textInput.Find("input").Input("some 2");
            testSchedulers.Default.AdvanceBy(TimeSpan.FromMilliseconds(ComponentDefaultThrottleMs).Ticks);
        });

        //expect 11 which is item 'some 2' and items 'some 20', 'some 21' etc.
        Assert.Equal(11, filteredList.Count);
        Assert.Equal((100, null, 11), allCounts);
        var counts = cut.Find($"#{id}-display-counts");
        Assert.Equal("11 of 100 some further text", counts.TextContent);
    }

    [Fact]
    public async Task FilterLoadingFuncSuccessful()
    {
        var testSchedulers = new TestSchedulers();
        Services.AddTestSchedulers(sp => testSchedulers);

        const string id = "some-name";
        var testData = GetTestCollection();
        (int? total, int? selected, int? included) allCounts = (null, null, null);
        var filteredList = new List<TestListerItem>();

        var cut = RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.ListerLoadingFunc, _ => Task.FromResult(testData))
            .Add(p => p.Template, item => $"<span id='item'>{item.Id}</span>")
            .Add(p => p.Virtualize, true)
            .Add(p => p.CanFilter, true)
            .Add(p => p.CountDisplay, ListerCountDisplay.FilterAndTotal)
            .Add(p => p.OnFilteredItems, callback => filteredList = callback)
            .Add(p => p.CurrentCounts, callback => allCounts = callback)
            .Add(p => p.CountDisplayPostText, "some further text")
        );

        cut.WaitForAssertion(() => cut.HasComponent<Text>());

        //apply the search text
        var textInput = cut.FindComponent<Text>();
        await textInput.InvokeAsync(() =>
        {
            textInput.Find("input").Input("some 2");
            testSchedulers.Default.AdvanceBy(TimeSpan.FromMilliseconds(ComponentDefaultThrottleMs).Ticks);
        });

        //expect 11 which is item 'some 2' and items 'some 20', 'some 21' etc.
        Assert.Equal(11, filteredList.Count);
        Assert.Equal((100, null, 11), allCounts);
        var counts = cut.Find($"#{id}-display-counts");
        Assert.Equal("11 of 100 some further text", counts.TextContent);
    }

    [Fact]
    public async Task FilterItemsProviderVirtSuccessful()
    {
        var testSchedulers = new TestSchedulers();
        Services.AddTestSchedulers(sp => testSchedulers);

        var testData = GetTestCollection();
        const string id = "some-name";
        (int? total, int? selected, int? included) allCounts = (null, null, null);
        var fakeTotal = testData.Count;
        var filteredList = new List<TestListerItem>();

        var filterApplied = "some 2";
        var mockItemsService = new Mock<IItemsService<TestListerItem>>();
        //no filter - used to make the initial request
        mockItemsService.Setup(x =>
                x.GetItemsAsync(
                    It.IsAny<int>(), It.IsAny<int>(), It.IsAny<string>(),
                    It.IsAny<SortDir>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((testData.Count, testData));
        //with filter - used to make the request with filter on
        mockItemsService.Setup(x =>
                x.GetItemsAsync(
                    filterApplied, It.IsAny<FilterComparisonType>(),
                    It.IsAny<int>(), It.IsAny<int>(), It.IsAny<string>(),
                    It.IsAny<SortDir>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((testData.Count, testData.Count(x => x.SomeText.StartsWith(filterApplied)),
                testData.Where(x => x.SomeText.StartsWith(filterApplied))));
        mockItemsService.SetupGet(x => x.ObservableThrottleMs).Returns(1000);

        var cut = RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.Template, item => $"<span id='item'>{item.Id}</span>")
            .Add(p => p.Virtualize, true)
            .Add(p => p.ItemsService, mockItemsService.Object)
            .Add(p => p.CanFilter, true)
            .Add(p => p.CountDisplay, ListerCountDisplay.FilterAndTotal)
            .Add(p => p.OnFilteredItems, callback => filteredList = callback)
            .Add(p => p.CurrentCounts, callback => allCounts = callback)
            .Add(p => p.CountDisplayPostText, "some further text")
        );

        testSchedulers.Default.AdvanceBy(TimeSpan.FromMilliseconds(mockItemsService.Object.ObservableThrottleMs).Ticks);

        //check initial counts
        cut.WaitForAssertion(() =>
        {
            Assert.Equal(0, filteredList.Count);
            Assert.Equal((fakeTotal, null, fakeTotal), allCounts);
        });

        //apply the search text
        var textInput = cut.FindComponent<Text>();
        await textInput.InvokeAsync(() =>
        {
            textInput.Find("input").Input(filterApplied);
            testSchedulers.Default.AdvanceBy(TimeSpan.FromMilliseconds(ComponentDefaultThrottleMs).Ticks);
        });

        testSchedulers.Default.AdvanceBy(TimeSpan.FromMilliseconds(mockItemsService.Object.ObservableThrottleMs).Ticks);

        cut.WaitForAssertion(() =>
        {
            //expect 11 which is item 'some 2' and items 'some 20', 'some 21' etc.
            Assert.Equal(11, filteredList.Count);
            Assert.Equal((fakeTotal, null, 11), allCounts);
        });

        cut.Render();
        cut.WaitForElement($"#{id}-display-counts");
        var counts = cut.Find($"#{id}-display-counts");

        cut.WaitForAssertion(() =>
        {
            Assert.Equal($"11 of {fakeTotal} some further text", counts.TextContent);
        });

        mockItemsService.Verify(x => x.GetItemsAsync(It.IsAny<int>(), It.IsAny<int>(), It.IsAny<string>(),
            It.IsAny<SortDir>(), It.IsAny<CancellationToken>()), Times.Once);
        mockItemsService.Verify(x => x.GetItemsAsync(filterApplied, It.IsAny<FilterComparisonType>(), It.IsAny<int>(),
            It.IsAny<int>(), It.IsAny<string>(), It.IsAny<SortDir>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public void CanFilterWithNoFilterAppliedDisplaysCorrectly()
    {
        const string id = "some-name";
        var testData = GetTestCollection().OrderBy(x => x.Id).ToList();

        var cut = RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.Data, testData)
            .Add(p => p.Template, item => $"<span id='item'>item {item.Id}</span>")
            .Add(p => p.Virtualize, false)
            .Add(p => p.CanFilter, true)
            .Add(p => p.CountDisplay, ListerCountDisplay.FilterAndTotal)
            .Add(p => p.CountDisplayPostText, "some further text")
        );

        cut.WaitForElement($"#{id}-display-counts");
        cut.WaitForAssertion(() => cut.Find($"#{id}-display-counts"));
        var counts = cut.Find($"#{id}-display-counts");
        Assert.Equal("100 of 100 some further text", counts.TextContent);
    }

    [Fact]
    public void FilterAllDataWithExternalFilterAppliedFiltersCorrectly()
    {
        const string id = "some-name";
        var testData = GetTestCollection().OrderBy(x => x.Id).ToList();
        var filteredList = new List<TestListerItem>();

        var cut = RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.Data, testData)
            .Add(p => p.Template, item => $"<span id='item'>item {item.Id}</span>")
            .Add(p => p.Virtualize, false)
            .Add(p => p.CanFilter, true)
            .Add(p => p.SetExternalFilter, "some 9")
            .Add(p => p.CountDisplay, ListerCountDisplay.FilterAndTotal)
            .Add(p => p.OnFilteredItems, callback => filteredList = callback)
        );

        cut.WaitForAssertion(() => cut.HasComponent<Text>());
        cut.Render();

        Assert.Equal(11, filteredList.Count);
        var counts = cut.Find($"#{id}-display-counts");
        Assert.Equal("11 of 100", counts.TextContent);
    }

    [Fact]
    public void FilterItemsProviderVirtWithExternalFilterAppliedFiltersCorrectly()
    {
        var testSchedulers = new TestSchedulers();
        Services.AddTestSchedulers(sp => testSchedulers);

        var testData = GetTestCollection();
        const string id = "some-name";
        (int? total, int? selected, int? included) allCounts = (null, null, null);
        var filteredList = new List<TestListerItem>();

        var filterApplied = "some 9";
        var mockItemsService = new Mock<IItemsService<TestListerItem>>();
        //no filter - used to make the initial request
        mockItemsService.Setup(x =>
                x.GetItemsAsync(
                    It.IsAny<int>(), It.IsAny<int>(), It.IsAny<string>(), It.IsAny<SortDir>()))
            .ReturnsAsync((testData.Count, testData));
        //with filter - used to make the request with filter on
        mockItemsService.Setup(x =>
                x.GetItemsAsync(
                    It.IsAny<string>(), It.IsAny<FilterComparisonType>(),
                    It.IsAny<int>(), It.IsAny<int>(), It.IsAny<string>(), It.IsAny<SortDir>(),
                    It.IsAny<CancellationToken>()))
            .ReturnsAsync((testData.Count, testData.Count(x => x.SomeText.StartsWith(filterApplied)),
                testData.Where(x => x.SomeText.StartsWith(filterApplied))));
        mockItemsService.SetupGet(x => x.ObservableThrottleMs).Returns(1000);

        var cut = RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.Template, item => $"<span id='item'>{item.Id}</span>")
            .Add(p => p.Virtualize, true)
            .Add(p => p.ItemsService, mockItemsService.Object)
            .Add(p => p.CanFilter, true)
            .Add(p => p.CountDisplay, ListerCountDisplay.FilterAndTotal)
            .Add(p => p.OnFilteredItems, callback => filteredList = callback)
            .Add(p => p.CurrentCounts, callback => allCounts = callback)
            .Add(p => p.SetExternalFilter, filterApplied)
        );

        testSchedulers.Default.AdvanceBy(TimeSpan.FromMilliseconds(mockItemsService.Object.ObservableThrottleMs).Ticks);

        //expect 11 which is item 'some 9' and items 'some 90', 'some 91' etc.
        Assert.Equal(11, filteredList.Count);
        Assert.Equal((100, null, 11), allCounts);
        cut.WaitForElement($"#{id}-display-counts");
        var counts = cut.Find($"#{id}-display-counts");
        Assert.Equal("11 of 100", counts.TextContent);
    }

    [Fact]
    public async Task FilterCanForceComplete()
    {
        AddGetCursorSelection(new CursorSelection(0, 0));

        const string id = "some-name";
        var testData = GetTestCollection();
        var filteredList = new List<TestListerItem>();

        var cut = RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.Data, testData)
            .Add(p => p.Template, item => $"<span id='item'>item {item.Id}</span>")
            .Add(p => p.Virtualize, false)
            .Add(p => p.CanFilter, true)
            .Add(p => p.CountDisplay, ListerCountDisplay.FilterAndTotal)
            .Add(p => p.OnFilteredItems, callback => filteredList = callback)
        );

        var textInput = cut.FindComponent<Text>();
        await textInput.InvokeAsync(() =>
        {
            var filterInputElement = cut.Find("cctc-input[data-cctc-input-type=\"text\"] input");
            filterInputElement.Input("Some 9");
            filterInputElement.KeyUp("Enter");

            cut.Render();

            Assert.Equal(11, filteredList.Count);
            var counts = cut.Find($"#{id}-display-counts");
            Assert.Equal("11 of 100", counts.TextContent);
        });
    }

    [Fact]
    public void SelectionTypeNoneHidesCheckbox()
    {
        const string id = "some-name";
        var testData = GetTestCollection().OrderBy(x => x.Id).ToList();

        var cut = RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.Data, testData)
            .Add(p => p.Template, item => $"<span id='item'>item {item.Id}</span>")
            .Add(p => p.Virtualize, false)
            .Add(p => p.SelectionType, ListerSelectionType.None)
        );

        cut.WaitForAssertion(() => cut.Find($"#{id}-no-virtualize"));

        //check inputs not found
        TestHelpers.AssertNotFound(() => cut.Find($"#{id}-1"));
    }

    [Fact]
    public void SelectionTypeSingleForItemsProviderThrowsError()
    {
        const string id = "some-name";
        var testData = GetTestCollection();

        var mockItemsService = new Mock<IItemsService<TestListerItem>>();
        mockItemsService.Setup(x =>
                x.GetItemsAsync(It.IsAny<int>(), It.IsAny<int>(), It.IsAny<string>(), It.IsAny<SortDir>()))
            .ReturnsAsync((testData.Count, testData));

        Assert.Throws<InvalidOperationException>(() => RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.ItemsService, mockItemsService.Object)
            .Add(p => p.SelectionType, ListerSelectionType.Single)
        ));
    }

    [Fact]
    public void SelectionTypeMultiForItemsProviderThrowsError()
    {
        const string id = "some-name";
        var testData = GetTestCollection();

        var mockItemsService = new Mock<IItemsService<TestListerItem>>();
        mockItemsService.Setup(x =>
                x.GetItemsAsync(It.IsAny<int>(), It.IsAny<int>(), It.IsAny<string>(), It.IsAny<SortDir>()))
            .ReturnsAsync((testData.Count, testData));

        Assert.Throws<InvalidOperationException>(() => RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.ItemsService, mockItemsService.Object)
            .Add(p => p.SelectionType, ListerSelectionType.Multiple)
        ));
    }

    [Fact]
    public void CanSelectAllForItemsProviderThrowsError()
    {
        const string id = "some-name";
        var testData = GetTestCollection();

        var mockItemsService = new Mock<IItemsService<TestListerItem>>();
        mockItemsService.Setup(x =>
                x.GetItemsAsync(It.IsAny<int>(), It.IsAny<int>(), It.IsAny<string>(), It.IsAny<SortDir>()))
            .ReturnsAsync((testData.Count, testData));

        Assert.Throws<InvalidOperationException>(() => RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.ItemsService, mockItemsService.Object)
            .Add(p => p.SelectionType, ListerSelectionType.None)
            .Add(p => p.CanSelectAll, true)
        ));
    }

    [Fact]
    public void PreSelectedIndexesForItemsProviderThrowsError()
    {
        const string id = "some-name";
        var testData = GetTestCollection();

        var mockItemsService = new Mock<IItemsService<TestListerItem>>();
        mockItemsService.Setup(x =>
                x.GetItemsAsync(It.IsAny<int>(), It.IsAny<int>(), It.IsAny<string>(), It.IsAny<SortDir>()))
            .ReturnsAsync((testData.Count, testData));

        Assert.Throws<InvalidOperationException>(() => RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.ItemsService, mockItemsService.Object)
            .Add(p => p.SelectionType, ListerSelectionType.None)
            .Add(p => p.PreSelectedItemsAtIndices, new List<int>())
        ));
    }

    [Fact]
    public void CanSelectForItemsProviderThrowsError()
    {
        const string id = "some-name";
        var testData = GetTestCollection();

        var mockItemsService = new Mock<IItemsService<TestListerItem>>();
        mockItemsService.Setup(x =>
                x.GetItemsAsync(It.IsAny<int>(), It.IsAny<int>(), It.IsAny<string>(), It.IsAny<SortDir>()))
            .ReturnsAsync((testData.Count, testData));

        Assert.Throws<InvalidOperationException>(() => RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.ItemsService, mockItemsService.Object)
            .Add(p => p.SelectionType, ListerSelectionType.None)
            .Add(p => p.CanSelect, _ => true)
        ));
    }

    [Fact]
    public void AllowSelectionWithItemClickForItemsProviderThrowsError()
    {
        const string id = "some-name";
        var testData = GetTestCollection();

        var mockItemsService = new Mock<IItemsService<TestListerItem>>();
        mockItemsService.Setup(x =>
                x.GetItemsAsync(It.IsAny<int>(), It.IsAny<int>(), It.IsAny<string>(), It.IsAny<SortDir>()))
            .ReturnsAsync((testData.Count, testData));

        Assert.Throws<InvalidOperationException>(() => RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.ItemsService, mockItemsService.Object)
            .Add(p => p.SelectionType, ListerSelectionType.None)
            .Add(p => p.AllowSelectionWithItemClick, true)
        ));
    }

    [Fact]
    public void SelectionTypeSingleShowsCheckboxes()
    {
        const string id = "some-name";
        var testData = GetTestCollection().OrderBy(x => x.Id).ToList();

        var cut = RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.Data, testData)
            .Add(p => p.Template, item => $"<span id='item'>item {item.Id}</span>")
            .Add(p => p.Virtualize, false)
            .Add(p => p.SelectionType, ListerSelectionType.Single)
        );

        cut.WaitForAssertion(() => cut.Find($"#{id}-no-virtualize"));

        //check inputs not found
        TestHelpers.AssertFound(() => cut.Find($"#{id}-select-1"));
    }

    [Fact]
    public void SelectionTypeMultiShowsCheckboxes()
    {
        const string id = "some-name";
        var testData = GetTestCollection().OrderBy(x => x.Id).ToList();

        var cut = RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.Data, testData)
            .Add(p => p.Template, item => $"<span id='item'>item {item.Id}</span>")
            .Add(p => p.Virtualize, false)
            .Add(p => p.SelectionType, ListerSelectionType.Multiple)
        );

        cut.WaitForAssertion(() => cut.Find($"#{id}-no-virtualize"));

        //check inputs not found
        TestHelpers.AssertFound(() => cut.Find($"#{id}-select-1"));
    }

    [Fact]
    public void SelectionTypeSingleSelectReturnsCorrectly()
    {
        const string id = "some-name";
        var testData = GetTestCollection().OrderBy(x => x.Id).ToList();
        var selectedItems = new List<TestListerItem>();

        var cut = RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.Data, testData)
            .Add(p => p.Template, item => $"<span id='item'>item {item.Id}</span>")
            .Add(p => p.Virtualize, false)
            .Add(p => p.SelectionType, ListerSelectionType.Single)
            .Add(p => p.OnSelectedChanged, callback => selectedItems = callback)
        );

        cut.WaitForAssertion(() => cut.Find($"#{id}-no-virtualize"));

        //select an item
        var item = cut.Find($"#{id}-select-2");
        item.Change(new ChangeEventArgs { Value = true });
        cut.Render();
        //check returned
        Assert.Collection(selectedItems, testItem => Assert.Equal("some 10", testItem.SomeText));

        //now select another but only one item should be returned in single mode
        var secondItem = cut.Find($"#{id}-select-6");
        secondItem.Change(new ChangeEventArgs { Value = true });

        //check returned
        Assert.Collection(selectedItems, testItem => Assert.Equal("some 14", testItem.SomeText));
        Assert.Equal(1, selectedItems.Count);
    }

    [Fact]
    public void SelectionTypeMultiSelectReturnsCorrectly()
    {
        const string id = "some-name";
        var testData = GetTestCollection().OrderBy(x => x.Id).ToList();

        var selectedItems = new List<TestListerItem>();

        var cut = RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.Data, testData)
            .Add(p => p.Template, item => $"<span id='item'>item {item.Id}</span>")
            .Add(p => p.Virtualize, false)
            .Add(p => p.SelectionType, ListerSelectionType.Multiple)
            .Add(p => p.OnSelectedChanged, callback => selectedItems = callback)
        );

        cut.WaitForAssertion(() => cut.Find($"#{id}-no-virtualize"));

        //select an item
        var item = cut.Find($"#{id}-select-2");
        item.Change(new ChangeEventArgs { Value = true });
        cut.Render();
        cut.WaitForAssertion(() => cut.Find($"#{id}-no-virtualize"));

        //check returned
        Assert.Collection(selectedItems, testItem => Assert.Equal("some 10", testItem.SomeText));

        //now select another which should also be included on multiple mode
        var secondItem = cut.Find($"#{id}-select-6");
        secondItem.Change(new ChangeEventArgs { Value = true });

        //check returned
        Assert.Collection(selectedItems,
            testItem => Assert.Equal("some 10", testItem.SomeText),
                                    testItem => Assert.Equal("some 14", testItem.SomeText)
            );
        Assert.Equal(2, selectedItems.Count);
    }

    [Fact]
    public void SelectionTypeMultiSelectAllReturnsCorrectly()
    {
        AddAddTooltip();
        AddUpdateTooltipTitle();
        Services.AddTestSchedulers();
        const string id = "some-name";
        var testData = GetTestCollection().OrderBy(x => x.Id).ToList();
        var selectedItems = new List<TestListerItem>();

        var cut = RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.Data, testData)
            .Add(p => p.Template, item => $"<span id='item'>item {item.Id}</span>")
            .Add(p => p.Virtualize, false)
            .Add(p => p.SelectionType, ListerSelectionType.Multiple)
            .Add(p => p.CanSelectAll, true)
            .Add(p => p.OnSelectedChanged, callback => selectedItems = callback)
        );

        cut.WaitForAssertion(() => cut.Find($"#{id}-no-virtualize"));

        //select all
        cut.Find($"#{id}-select-all").Click();

        cut.WaitForElement($"#{id}-select-all");

        cut.WaitForState(() => cut.Find($"#{id}-select-all").TextContent == "deselect all");

        Assert.Equal(testData.Count, selectedItems.Count);

        //select none
        cut.Find($"#{id}-select-all").Click();
        cut.WaitForState(() => cut.Find($"#{id}-select-all").TextContent == "select all");
        Assert.Equal(0, selectedItems.Count);
    }

    [Fact]
    public void SelectionTypeMultiSelectAllReturnsCorrectlyWhenFilterGiven()
    {
        AddAddTooltip();
        AddUpdateTooltipTitle();
        Services.AddTestSchedulers();
        const string id = "some-name";
        var filterApplied = "some 9";
        var testData = GetTestCollection().OrderBy(x => x.Id).ToList();
        var selectedItems = new List<TestListerItem>();

        var cut = RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.Data, testData)
            .Add(p => p.Template, item => $"<span id='item'>item {item.Id}</span>")
            .Add(p => p.Virtualize, false)
            .Add(p => p.SetExternalFilter, filterApplied)
            .Add(p => p.SelectionType, ListerSelectionType.Multiple)
            .Add(p => p.CanSelectAll, true)
            .Add(p => p.OnSelectedChanged, callback => selectedItems = callback)
        );

        cut.WaitForAssertion(() => cut.Find($"#{id}-no-virtualize"));

        //select all
        cut.Find($"#{id}-select-all").Click();
        cut.WaitForState(() => cut.Find($"#{id}-select-all").TextContent == "deselect all");
        Assert.Equal(11, selectedItems.Count);

        //select none
        cut.Find($"#{id}-select-all").Click();
        cut.WaitForState(() => cut.Find($"#{id}-select-all").TextContent == "select all");
        Assert.Equal(0, selectedItems.Count);
    }

    [Fact]
    public void PreSelectingIndicesCorrectlySetsOnInitialization()
    {
        AddAddTooltip();
        AddUpdateTooltipTitle();
        Services.AddTestSchedulers();
        const string id = "some-name";
        var testData = GetTestCollection().OrderBy(x => x.Id).ToList();

        var cut = RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.Data, testData)
            .Add(p => p.Template, item => $"<span id='item'>item {item.Id}</span>")
            .Add(p => p.Virtualize, false)
            .Add(p => p.SelectionType, ListerSelectionType.Multiple)
            .Add(p => p.CanSelectAll, true)
            .Add(p => p.PreSelectedItemsAtIndices, new List<int> { 1, 3, 10 })
        );

        cut.WaitForAssertion(() => cut.Find($"#{id}-no-virtualize"));

        TestHelpers.AssertChecked(() => cut.Find($"#{id}-select-1"));
        //just check one is not checked
        TestHelpers.AssertNotChecked(() => cut.Find($"#{id}-select-2"));
        TestHelpers.AssertChecked(() => cut.Find($"#{id}-select-3"));
        TestHelpers.AssertChecked(() => cut.Find($"#{id}-select-10"));
    }

    [Fact]
    public void PreSelectingIndicesCorrectlySetsOnInitializationWhenSingleSelectionMode()
    {
        const string id = "some-name";
        var testData = GetTestCollection().OrderBy(x => x.Id).ToList();

        var cut = RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.Data, testData)
            .Add(p => p.Template, item => $"<span id='item'>item {item.Id}</span>")
            .Add(p => p.Virtualize, false)
            .Add(p => p.SelectionType, ListerSelectionType.Single)
            .Add(p => p.CanSelectAll, true)
            .Add(p => p.PreSelectedItemsAtIndices, new List<int> { 3, 1, 10 })
        );

        cut.WaitForAssertion(() => cut.Find($"#{id}-no-virtualize"));

        //NOTE: failed here when run as a batch but passing when run alone
        TestHelpers.AssertChecked(() => cut.Find($"#{id}-select-3"));
        //rest of items should be ignored
        TestHelpers.AssertNotChecked(() => cut.Find($"#{id}-select-1"));
        TestHelpers.AssertNotChecked(() => cut.Find($"#{id}-select-10"));
    }

    [Fact]
    public void PreSelectingIndicesCorrectlySetsOnInitializationWithFilter()
    {
        AddAddTooltip();
        AddUpdateTooltipTitle();
        Services.AddTestSchedulers();
        const string id = "some-name";
        var testData = GetTestCollection().OrderBy(x => x.Id).ToList();

        var cut = RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.Data, testData)
            .Add(p => p.Template, item => $"<span id='item'>item {item.Id}</span>")
            .Add(p => p.Virtualize, false)
            .Add(p => p.SelectionType, ListerSelectionType.Multiple)
            .Add(p => p.CanSelectAll, true)
            .Add(p => p.PreSelectedItemsAtIndices, new List<int> { 10 })
            .Add(p => p.SetExternalFilter, "some 1")
        );

        cut.WaitForAssertion(() => cut.Find($"#{id}-no-virtualize"));

        //is not preselected
        TestHelpers.AssertNotChecked(() => cut.Find($"#{id}-select-1"));
        //is preselected
        TestHelpers.AssertChecked(() => cut.Find($"#{id}-select-10"));
    }

    [Fact]
    public void SelectAllNotShownWhenDisabled()
    {
        const string id = "some-name";
        var testData = GetTestCollection().OrderBy(x => x.Id).ToList();

        var cut = RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.Data, testData)
            .Add(p => p.Template, item => $"<span id='item'>item {item.Id}</span>")
            .Add(p => p.Virtualize, false)
            .Add(p => p.SelectionType, ListerSelectionType.Multiple)
            .Add(p => p.CanSelectAll, true)
            .Add(p => p.Disabled, true)
        );

        cut.WaitForAssertion(() => cut.Find($"#{id}-no-virtualize"));
        TestHelpers.AssertNotFound(() => cut.Find($"#{id}-select-all"));
    }

    [Fact]
    public void SelectAllNotShownWhenReadOnly()
    {
        const string id = "some-name";
        var testData = GetTestCollection().OrderBy(x => x.Id).ToList();

        var cut = RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.Data, testData)
            .Add(p => p.Template, item => $"<span id='item'>item {item.Id}</span>")
            .Add(p => p.Virtualize, false)
            .Add(p => p.SelectionType, ListerSelectionType.Multiple)
            .Add(p => p.CanSelectAll, true)
            .Add(p => p.ReadOnly, true)
        );

        cut.WaitForAssertion(() => cut.Find($"#{id}-no-virtualize"));
        TestHelpers.AssertNotFound(() => cut.Find($"#{id}-select-all"));
    }

    [Fact]
    public void FilterNotShownWhenDisabled()
    {
        const string id = "some-name";
        var testData = GetTestCollection().OrderBy(x => x.Id).ToList();

        var cut = RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.Data, testData)
            .Add(p => p.Template, item => $"<span id='item'>item {item.Id}</span>")
            .Add(p => p.Virtualize, false)
            .Add(p => p.SelectionType, ListerSelectionType.Multiple)
            .Add(p => p.CanFilter, true)
            .Add(p => p.Disabled, true)
        );

        cut.WaitForAssertion(() => cut.Find($"#{id}-no-virtualize"));
        TestHelpers.AssertNotFound(() => cut.Find($"#{id}-filter-input"));
    }

    [Fact]
    public void ReadOnlyListerGivesCheckboxesReadonlyAttribute()
    {

        const string id = "some-name";
        var testData = GetTestCollection().OrderBy(x => x.Id).ToList();

        var cut = RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.Data, testData)
            .Add(p => p.Template, item => $"<span id='item'>item {item.Id}</span>")
            .Add(p => p.Virtualize, false)
            .Add(p => p.SelectionType, ListerSelectionType.Multiple)
            .Add(p => p.CanFilter, true)
            .Add(p => p.ReadOnly, true)
        );

        cut.WaitForAssertion(() => cut.Find($"#{id}-no-virtualize"));
        TestHelpers.AssertReadonly(() => cut.Find($"#{id}-select-1"));
    }

    [Fact]
    public async Task AllDataListerOnSortInvokesCallbackWhenKeysUnique()
    {
        const string id = "some-name";
        var testData = GetTestCollection().OrderBy(_ => Guid.NewGuid()).ToList();
        SetSortParameters("sometext", SortDir.Asc);

        bool onOrderedDataChangedRaised = false;
        List<TestListerItem> actualOrderedData = new();

        var cut = RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.Data, testData)
            .Add(p => p.Template, item => $"<span id='item'>item {item.Id}</span>")
            .Add(p => p.Virtualize, false)
            .Add(p => p.OnOrderedDataChanged, args => { onOrderedDataChangedRaised = true; actualOrderedData = args; })
        );

        cut.WaitForElement(".item-row");
        await cut.Instance.SortData();

        var expectedOrderedData = GetTestCollectionOrderedBySomeTextAsc(testData);
        Assert.True(onOrderedDataChangedRaised);
        Assert.Equal(expectedOrderedData, actualOrderedData);
    }

    [Fact]
    public async Task AllDataListerOnSortStillInvokesCallbackWhenKeysNotUnique()
    {

        const string id = "some-name";
        var testData = GetTestCollectionWithNonUniqueUrns().OrderBy(_ => Guid.NewGuid()).ToList();

        var onOrderedDataChangedRaised = false;

        var cut = RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.Data, testData)
            .Add(p => p.Template, item => $"<span id='item'>item {item.Id}</span>")
            .Add(p => p.Virtualize, false)
            .Add(p => p.OnOrderedDataChanged, args => { onOrderedDataChangedRaised = true; })
        );

        cut.WaitForElement(".item-row");
        await cut.Instance.SortData();

        Assert.True(onOrderedDataChangedRaised);
    }

    [Fact]
    public async Task DataProviderListerOnSortDoesNotInvokeCallback()
    {
        var testSchedulers = new TestSchedulers();
        Services.AddTestSchedulers(sp => testSchedulers);

        const string id = "some-name";
        var testData = GetTestCollection();

        var mockItemsService = new Mock<IItemsService<TestListerItem>>();
        mockItemsService.Setup(x =>
                x.GetItemsAsync(It.IsAny<int>(), It.IsAny<int>(), It.IsAny<string>(), It.IsAny<SortDir>()))
            .ReturnsAsync((testData.Count, testData));
        mockItemsService.SetupGet(x => x.ObservableThrottleMs).Returns(1000);

        bool onOrderedDataChangedRaised = false;

        var cut = RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.ItemsService, mockItemsService.Object)
            .Add(p => p.Template, item => $"<span id='item'>item {item.Id}</span>")
            .Add(p => p.OnOrderedDataChanged, args => { onOrderedDataChangedRaised = true; })
        );

        testSchedulers.Default.AdvanceBy(TimeSpan.FromMilliseconds(mockItemsService.Object.ObservableThrottleMs).Ticks);
        await cut.Instance.SortData();

        Assert.False(onOrderedDataChangedRaised);
    }

    [Fact]
    public async Task AllDataListerOnSortIndexesCorrect()
    {
        const string id = "some-name";
        var testData = GetTestCollection().OrderBy(_ => Guid.NewGuid()).ToList();
        SetSortParameters("sometext", SortDir.Asc);

        var cut = RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.Data, testData)
            .Add(p => p.Template, item => $"<span id='item'>{item.SomeText}</span>")
            .Add(p => p.Virtualize, false)
        );

        cut.WaitForElement(".item-row");
        await cut.Instance.SortData();
        cut.Render();

        var expected = GetTestCollectionOrderedBySomeTextAsc(testData).Select(x => x.SomeText);
        var rows = cut.FindAll(".item-row");
        var actual = rows.Select(x => x.TextContent.Trim()).ToList();
        Assert.Equal(expected, actual);
    }

    //tests for loading, cancelling and refreshing
    [Fact]
    public void ItemsProviderShowsWorkingOnInitialLoad()
    {
        var testSchedulers = new TestSchedulers();
        Services.AddTestSchedulers(sp => testSchedulers);

        const string id = "some-name";
        var testData = GetTestCollection().OrderBy(_ => Guid.NewGuid()).Take(10).ToList();

        var mockItemsService = new Mock<IItemsService<TestListerItem>>();
        mockItemsService.Setup(x =>
                x.GetItemsAsync(It.IsAny<int>(), It.IsAny<int>(), It.IsAny<string>(), It.IsAny<SortDir>(),
                    It.IsAny<CancellationToken>()))
            .ReturnsAsync((10, testData));
        mockItemsService.SetupGet(x => x.ObservableThrottleMs).Returns(500);

        var cut = RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.ItemSizePixels, 50)
            .Add(p => p.OverscanCount, 20)
            .Add(p => p.ItemsService, mockItemsService.Object)
            .Add(p => p.CountDisplay, ListerCountDisplay.TotalOnly)
            .Add(p => p.Template, item => $"<span id='item'>{item.Id}</span>")
            .Add(p => p.Virtualize, true)
        );

        TestHelpers.AssertFound(() => cut.Find($"#{id}-working-template"));
        TestHelpers.AssertNotFound(() => cut.Find($"#{id}-display-counts"));

        testSchedulers.Default.AdvanceBy(TimeSpan.FromMilliseconds(mockItemsService.Object.ObservableThrottleMs).Ticks);
        cut.Render();

        cut.WaitForElement($"#{id}-display-counts");

        TestHelpers.AssertNotFound(() => cut.Find($"#{id}-working-template"));
        TestHelpers.AssertFound(() => cut.Find($"#{id}-display-counts"));
    }

    [Fact]
    public void ItemsProviderCancelsAndRefreshesLoading()
    {
        var testSchedulers = new TestSchedulers();
        Services.AddTestSchedulers(sp => testSchedulers);

        const string id = "some-name";
        var testData = GetTestCollection().OrderBy(_ => Guid.NewGuid()).Take(10).ToList();

        var mockItemsService = new Mock<IItemsService<TestListerItem>>();
        mockItemsService.Setup(x =>
                x.GetItemsAsync(It.IsAny<int>(), It.IsAny<int>(), It.IsAny<string>(), It.IsAny<SortDir>(),
                    It.IsAny<CancellationToken>()))
            .ReturnsAsync((10, testData));
        mockItemsService.SetupGet(x => x.ObservableThrottleMs).Returns(500);

        var cut = RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.ItemSizePixels, 50)
            .Add(p => p.OverscanCount, 20)
            .Add(p => p.ItemsService, mockItemsService.Object)
            .Add(p => p.CountDisplay, ListerCountDisplay.TotalOnly)
            .Add(p => p.Template, item => $"<span id='item'>{item.Id}</span>")
            .Add(p => p.Virtualize, true)
            .Add(p => p.CanCancelThenRefresh, true)
        );

        // Advance halfway to see cancel button before throttle completes
        testSchedulers.Default.AdvanceBy(TimeSpan.FromMilliseconds(mockItemsService.Object.ObservableThrottleMs).Ticks / 2);

        string cancelButtonSelector = $"#{id}-cancel-button";
        string refreshButtonSelector = $"#{id}-refresh-button";
        string displayCountsSelector = $"#{id}-display-counts";

        TestHelpers.AssertFound(() => cut.Find(cancelButtonSelector));
        TestHelpers.AssertNotFound(() => cut.Find(refreshButtonSelector));
        cut.Find(cancelButtonSelector).Click();
        TestHelpers.AssertNotFound(() => cut.Find(cancelButtonSelector));
        TestHelpers.AssertFound(() => cut.Find(refreshButtonSelector));

        cut.WaitForElement(displayCountsSelector);
        Assert.Equal("cancelled", cut.Find(displayCountsSelector).TextContent);

        cut.Find(refreshButtonSelector).Click();
        TestHelpers.AssertFound(() => cut.Find(cancelButtonSelector));
        TestHelpers.AssertNotFound(() => cut.Find(refreshButtonSelector));

        testSchedulers.Default.AdvanceBy(TimeSpan.FromMilliseconds(mockItemsService.Object.ObservableThrottleMs).Ticks);

        // Only expect one items service GetItemsAsync method invocation since the first request was cancelled before the throttle completed
        mockItemsService.Verify(x => x.GetItemsAsync(It.IsAny<int>(), It.IsAny<int>(), It.IsAny<string>(),
            It.IsAny<SortDir>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public void NoSetFocusOnIndexSoNoItemHasFocusAtInit()
    {
        var testData = GetTestCollection();
        var cut = RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, "lister")
            .Add(p => p.Data, testData)
            .Add(p => p.Template, item => $"<span id='item'>{item.Id}</span>")
            .Add(p => p.Virtualize, true)
        );

        cut.WaitForElement("#lister");

        //initially no item has focus
        TestHelpers.AssertNotFound(() => cut.Find(".item-row-has-focus"));
    }

    [Fact]
    public void CanSetRowInFocusWithMouse()
    {
        AddScrollIntoViewFromQuery();
        var testData = GetTestCollection();

        const string id = "lister";
        const int i = 2;
        var cut = RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.Data, testData)
            .Add(p => p.Template, item => $"<span id='item'>{item.Id}</span>")
            .Add(p => p.Virtualize, true)
            .Add(p => p.FocusItemOption, FocusItemOption.Allow)
        );

        cut.WaitForElement($"#{id}-item-row-{i}");

        //select another item
        const int newI = 5;

        cut.WaitForElement($"#{id}-item-row-{newI}");
        var targetRow = cut.Find($"#{id}-item-row-{newI}");
        targetRow.Click();
        cut.Render();

        TestHelpers.AssertFound(() => cut.Find($"#{id}-item-row-{newI}[{DataHasFocusAttr}]"));
        Assert.Equal(newI, cut.Instance.GetCurrentFocusIndex);
    }

    [Fact]
    public void CanScrollByKeyboardSelectsNextWhenSelectionGiven()
    {
        AddScrollIntoViewFromQuery();
        AddElementExists(true);
        var testData = GetTestCollection();

        const string id = "scroll-by-keyboard";
        const int i = 0;
        var cut = RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.Data, testData)
            .Add(p => p.Template, item => $"<span id='item'>{item.Id}</span>")
            .Add(p => p.Virtualize, true)
            .Add(p => p.FocusItemOption, FocusItemOption.AllowAndAlwaysFocusOnLoad)
        );

        cut.WaitForElement($"#{id}-item-row-{i}");
        cut.Render();

        //initially item i has focus
        var lister = cut.Find("cctc-lister-items-container");

        TestHelpers.AssertFound(() => cut.Find($"#{id}-item-row-{i}[{DataHasFocusAttr}]"));
        Assert.Equal(i, cut.Instance.GetCurrentFocusIndex);

        //make a keyup
        lister.KeyDown(Key.Down);
        cut.Render();
        TestHelpers.AssertFound(() => cut.Find($"#{id}-item-row-{i + 1}[{DataHasFocusAttr}]"));
        Assert.Equal(i + 1, cut.Instance.GetCurrentFocusIndex);

        //second keyup
        lister.KeyDown(Key.Down);
        cut.Render();
        TestHelpers.AssertFound(() => cut.Find($"#{id}-item-row-{i + 2}[{DataHasFocusAttr}]"));
        Assert.Equal(i + 2, cut.Instance.GetCurrentFocusIndex);

        //third keyup
        lister.KeyDown(Key.Up);
        cut.Render();
        TestHelpers.AssertFound(() => cut.Find($"#{id}-item-row-{i + 1}[{DataHasFocusAttr}]"));
        Assert.Equal(i + 1, cut.Instance.GetCurrentFocusIndex);
    }

    [Fact]
    public void CantScrollBeyondEnd()
    {
        AddScrollIntoViewFromQuery();
        AddElementExists(true);
        const int totalItems = 3;
        var testData = GetTestCollection().Take(totalItems).ToList();

        const string id = "scroll-by-keyboard";
        const int i = 0;
        var cut = RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.Data, testData)
            .Add(p => p.Template, item => $"<span id='item'>{item.Id}</span>")
            .Add(p => p.Virtualize, true)
            .Add(p => p.FocusItemOption, FocusItemOption.AllowAndAlwaysFocusOnLoad)
            .Add(p => p.ItemSizePixels, 10f)
            .Add(p => p.ItemsContainerHeightPixels, 30)
        );

        cut.WaitForElement($"#{id}-item-row-{i}");
        cut.Render();

        //initially item i has focus
        TestHelpers.AssertFound(() => cut.Find($"#{id}-item-row-{i}[{DataHasFocusAttr}]"));
        Assert.Equal(i, cut.Instance.GetCurrentFocusIndex);

        //move down one
        var lister = cut.Find("cctc-lister-items-container");
        lister.KeyDown(Key.Down);
        cut.Render();
        TestHelpers.AssertFound(() => cut.Find($"#{id}-item-row-{i + 1}[{DataHasFocusAttr}]"));
        Assert.Equal(i + 1, cut.Instance.GetCurrentFocusIndex);

        //move down one more
        lister = cut.Find("cctc-lister-items-container");
        lister.KeyDown(Key.Down);
        cut.Render();
        TestHelpers.AssertFound(() => cut.Find($"#{id}-item-row-{i + 2}[{DataHasFocusAttr}]"));
        Assert.Equal(i + 2, cut.Instance.GetCurrentFocusIndex);

        //try move down again - no change as at bottom
        lister = cut.Find("cctc-lister-items-container");
        lister.KeyDown(Key.Down);
        cut.Render();
        TestHelpers.AssertFound(() => cut.Find($"#{id}-item-row-{i + 2}[{DataHasFocusAttr}]"));
        Assert.Equal(i + 2, cut.Instance.GetCurrentFocusIndex);
    }

    [Fact]
    public void SelectItemWithMouseThenKeyWorks()
    {

        AddScrollIntoViewFromQuery();
        AddElementExists(true);
        AddHasElementGotFocus(true);

        const int totalItems = 5;
        var testData = GetTestCollection().Take(totalItems).ToList();

        const string id = "scroll-by-keyboard";
        const int i = 0;
        var cut = RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.Data, testData)
            .Add(p => p.Template, item => $"<span id='item'>{item.Id}</span>")
            .Add(p => p.Virtualize, true)
            .Add(p => p.FocusItemOption, FocusItemOption.AllowAndAlwaysFocusOnLoad)
            .Add(p => p.ItemSizePixels, 10f)
            .Add(p => p.ItemsContainerHeightPixels, 50)
        );

        cut.WaitForElement($"#{id}-item-row-{i}");
        cut.Render();

        //initially item i has focus
        TestHelpers.AssertFound(() => cut.Find($"#{id}-item-row-{i}[{DataHasFocusAttr}]"));
        Assert.Equal(i, cut.Instance.GetCurrentFocusIndex);

        //now select penultimate item with mouse
        var targetItemRow = cut.Find($"#{id}-item-row-3");
        targetItemRow.Click();
        cut.Render();
        TestHelpers.AssertFound(() => cut.Find($"#{id}-item-row-{3}[{DataHasFocusAttr}]"));
        Assert.Equal(3, cut.Instance.GetCurrentFocusIndex);

        //now arrow down
        var lister = cut.Find("cctc-lister-items-container");
        lister.KeyDown(Key.Down);
        cut.Render();
        //should be last item
        TestHelpers.AssertFound(() => cut.Find($"#{id}-item-row-{totalItems - 1}[{DataHasFocusAttr}]"));
        Assert.Equal(totalItems - 1, cut.Instance.GetCurrentFocusIndex);

        //now arrow down again - current index should remain the same
        lister = cut.Find("cctc-lister-items-container");
        lister.KeyDown(Key.Down);
        cut.Render();

        TestHelpers.AssertFound(() => cut.Find($"#{id}-item-row-{totalItems - 1}[{DataHasFocusAttr}]"));
        Assert.Equal(totalItems - 1, cut.Instance.GetCurrentFocusIndex);
    }

    [Fact]
    public void FocusItemOptionNonePreventsKeyboardScroll()
    {
        var scrollDownHandler = AddScrollDown();
        AddScrollIntoViewFromQuery();
        AddElementExists(true);
        const int totalItems = 5;
        var testData = GetTestCollection().Take(totalItems).ToList();

        const string id = "scroll-by-keyboard";
        const int i = -1;
        var cut = RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.Data, testData)
            .Add(p => p.Template, item => $"<span id='item'>{item.Id}</span>")
            .Add(p => p.Virtualize, true)
            .Add(p => p.FocusItemOption, FocusItemOption.None)
            .Add(p => p.ItemSizePixels, 10f)
            .Add(p => p.ItemsContainerHeightPixels, 50)
        );

        cut.WaitForElement($"cctc-lister-items-container");
        cut.Render();
        Assert.Equal(i, cut.Instance.GetCurrentFocusIndex);

        var lister = cut.Find("cctc-lister-items-container");

        //key down prevented
        lister.KeyDown(Key.Down);
        scrollDownHandler.SetVoidResult();
        scrollDownHandler.VerifyNotInvoke("scrollDown");

        Assert.Equal(i, cut.Instance.GetCurrentFocusIndex);

        //no items have data has focus attribute applied
        TestHelpers.AssertNotFound(() => cut.Find("cctc-lister-item-row[{DataHasFocusAttr}]"));
    }

    [Fact]
    public void FocusItemOptionNonePreventsMouseSelection()
    {
        var scrollDownHandler = AddScrollDown();
        var scrollIntoView = AddScrollIntoViewFromQuery();
        AddElementExists(true);
        const int totalItems = 5;
        var testData = GetTestCollection().Take(totalItems).ToList();

        const string id = "scroll-by-mouse";
        const int i = -1;
        var cut = RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.Data, testData)
            .Add(p => p.Template, item => $"<span id='item'>{item.Id}</span>")
            .Add(p => p.Virtualize, true)
            .Add(p => p.FocusItemOption, FocusItemOption.None)
            .Add(p => p.ItemSizePixels, 10f)
            .Add(p => p.ItemsContainerHeightPixels, 50)
        );

        cut.WaitForElement($"cctc-lister-items-container");
        cut.Render();
        Assert.Equal(i, cut.Instance.GetCurrentFocusIndex);

        //click index 7
        var index7 = cut.Find($"#{id}-item-row-{2}");
        index7.Click();
        scrollIntoView.SetVoidResult();

        Assert.Equal(i, cut.Instance.GetCurrentFocusIndex);

        //no items have data has focus attribute applied
        TestHelpers.AssertNotFound(() => cut.Find("cctc-lister-item-row[{DataHasFocusAttr}]"));
    }

    [Fact]
    public void FocusItemOptionAllowsPermitsKeyboardScroll()
    {
        AddScrollIntoViewFromQuery();
        AddElementExists(true);
        const int totalItems = 5;
        var testData = GetTestCollection().Take(totalItems).ToList();

        const string id = "scroll-by-keyboard";
        const int i = 0;
        var cut = RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.Data, testData)
            .Add(p => p.Template, item => $"<span id='item'>{item.Id}</span>")
            .Add(p => p.Virtualize, true)
            .Add(p => p.FocusItemOption, FocusItemOption.Allow)
            .Add(p => p.ItemSizePixels, 10f)
            .Add(p => p.ItemsContainerHeightPixels, 50)
        );

        cut.WaitForElement($"#{id}-item-row-{i}");
        cut.Render();

        //no items have data has focus attribute applied as a user must select first
        TestHelpers.AssertNotFound(() => cut.Find("cctc-lister-item-row[{DataHasFocusAttr}]"));

        //can firstly select an item with mouse
        cut.Find($"#{id}-item-row-{i}").Click();
        TestHelpers.AssertFound(() => cut.Find($"#{id}-item-row-{i}[{DataHasFocusAttr}]"));

        //now selecting an item by keyboard is permitted and sets the attribute
        var lister = cut.Find("cctc-lister-items-container");
        lister.KeyDown(Key.Down);
        cut.Render();
        //should be first item
        TestHelpers.AssertFound(() => cut.Find($"#{id}-item-row-{i + 1}[{DataHasFocusAttr}]"));
        Assert.Equal(i + 1, cut.Instance.GetCurrentFocusIndex);
    }

    [Fact]
    public void FocusItemOptionAllowAndAlwaysFocusOnLoadPermitsKeyboardScrollAndSelectsFirstItem()
    {
        AddScrollIntoViewFromQuery();
        AddElementExists(true);
        const int totalItems = 5;
        var testData = GetTestCollection().Take(totalItems).ToList();

        const string id = "scroll-by-keyboard";
        const int i = 0;
        var cut = RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.Data, testData)
            .Add(p => p.Template, item => $"<span id='item'>{item.Id}</span>")
            .Add(p => p.Virtualize, true)
            .Add(p => p.FocusItemOption, FocusItemOption.AllowAndAlwaysFocusOnLoad)
            .Add(p => p.ItemSizePixels, 10f)
            .Add(p => p.ItemsContainerHeightPixels, 50)
        );

        cut.WaitForElement($"#{id}-item-row-{i}");
        cut.Render();

        //no items have data has focus attribute applied as a user must select first
        TestHelpers.AssertFound(() => cut.Find($"#{id}-item-row-{i}[{DataHasFocusAttr}]"));

        //selecting an item by keyboard is permitted and sets the attribute
        var lister = cut.Find("cctc-lister-items-container");
        lister.KeyDown(Key.Down);
        cut.Render();

        //should be second item
        TestHelpers.AssertFound(() => cut.Find($"#{id}-item-row-{i + 1}[{DataHasFocusAttr}]"));
        Assert.Equal(i + 1, cut.Instance.GetCurrentFocusIndex);
    }



    void AssertRangeState(IRenderedComponent<Lister<TestListerItem>> cut, string id,
        int currIndex, (int top, int bottom) visibleRange)
    {
        AddElementExists(true);
        TestHelpers.AssertFound(() => cut.Find($"#{id}-item-row-{currIndex}[{DataHasFocusAttr}]"));
        Assert.Equal(currIndex, cut.Instance.GetCurrentFocusIndex);
        Assert.Equal(visibleRange, cut.Instance.VisibleRange);
    }

    void PrintDetails(Lister<TestListerItem> lister)
    {
        Output.WriteLine($"{lister.GetCurrentFocusIndex} - {lister.VisibleRange}");
    }

    [Fact]
    public void MouseAndKeyboardSelectionMaintainCorrectIndexAndVisibleRange_1()
    {
        //just key up and down one at a time

        var scrollIntoView = AddScrollIntoViewFromQuery();
        AddGetElementBoundRectFromQuery(EmptyBoundingRect());
        var scrollDownHandler = AddScrollDown();
        var scrollUpHandler = AddScrollUp();

        var testData = GetTestCollection();
        const string id = "scroll-by-keyboard";
        var i = 0;
        var cut = RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.Data, testData)
            .Add(p => p.Template, item => $"<span id='item'>{item.Id}</span>")
            .Add(p => p.Virtualize, true)
            .Add(p => p.OverscanCount, 20)
            .Add(p => p.FocusItemOption, FocusItemOption.AllowAndAlwaysFocusOnLoad)

            //expecting 8 items per page
            .Add(p => p.ItemsContainerHeightPixels, 400)
            .Add(p => p.ItemSizePixels, 50)
        );

        cut.WaitForElement($"#{id}-item-row-{i}");
        cut.Render();

        //initially item i has focus
        var lister = cut.Find("cctc-lister-items-container");

        //check curr index and visible range
        AssertRangeState(cut, id, i, (0,7));

        //keydown one by one within the visible range is correct
        for (var j = 0; j <= 6; j++)
        {
            lister.KeyDown(Key.Down);
            scrollDownHandler.SetVoidResult();
            AssertRangeState(cut, id, j + 1, (0, 7));
        }

        //keydown again should increment the visible range as the focussed item has left the range
        lister.KeyDown(Key.Down);
        scrollIntoView.SetVoidResult();
        AssertRangeState(cut, id, 8, (1, 8));

        //keydown again should increment the visible range as the focussed item has left the range
        lister.KeyDown(Key.Down);
        AssertRangeState(cut, id, 9, (2, 9));

        //key up - index changes but visible range unchanged as no scrolling required
        lister.KeyDown(Key.Up);
        AssertRangeState(cut, id, 8, (2, 9));

        //key up to top again
        for (var j = 7; j >= 2; j--)
        {
            lister.KeyDown(Key.Up);
            scrollUpHandler.SetVoidResult();
            AssertRangeState(cut, id, j, (2, 9));
        }

        lister.KeyDown(Key.Up);
        AssertRangeState(cut, id, 1, (1, 8));

        lister.KeyDown(Key.Up);
        AssertRangeState(cut, id, 0, (0, 7));
    }

    [Fact]
    public void MouseAndKeyboardSelectionMaintainCorrectIndexAndVisibleRange_2()
    {
        //page down and page up only

        var scrollIntoView = AddScrollIntoViewFromQuery();
        AddGetElementBoundRectFromQuery(new BoundingRect(0, 0, 0,0,400,0,0,0));
        var scrollDownHandler = AddScrollDown();
        var scrollUpHandler = AddScrollUp();

        var testData = GetTestCollection();
        const string id = "scroll-by-keyboard";
        var i = 0;
        var cut = RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.Data, testData)
            .Add(p => p.Template, item => $"<span id='item'>{item.Id}</span>")
            .Add(p => p.Virtualize, true)
            .Add(p => p.OverscanCount, 20)
            .Add(p => p.FocusItemOption, FocusItemOption.AllowAndAlwaysFocusOnLoad)

            //expecting 8 items per page
            .Add(p => p.ItemsContainerHeightPixels, 400)
            .Add(p => p.ItemSizePixels, 50)
        );

        cut.WaitForElement($"#{id}-item-row-{i}");
        cut.Render();

        //initially item i has focus
        var lister = cut.Find("cctc-lister-items-container");

        //check curr index and visible range
        AssertRangeState(cut, id, i, (0,7));

        //page down correct
        lister.KeyDown(Key.PageDown);
        scrollDownHandler.SetVoidResult();
        scrollIntoView.SetVoidResult();
        AssertRangeState(cut, id, 8, (8, 15));

        //page down again correct
        cut.Render();
        lister.KeyDown(Key.PageDown);
        scrollDownHandler.SetVoidResult();
        AssertRangeState(cut, id, 16, (16, 23));

        //page down again correct
        cut.Render();
        lister.KeyDown(Key.PageDown);
        scrollDownHandler.SetVoidResult();
        AssertRangeState(cut, id, 24, (24, 31));

        //page up again correct
        cut.Render();
        lister.KeyDown(Key.PageUp);
        scrollUpHandler.SetVoidResult();
        AssertRangeState(cut, id, 16, (16, 23));

        //page up again correct
        cut.Render();
        lister.KeyDown(Key.PageUp);
        scrollUpHandler.SetVoidResult();
        AssertRangeState(cut, id, 8, (8, 15));

        //page up again correct
        cut.Render();
        lister.KeyDown(Key.PageUp);
        scrollUpHandler.SetVoidResult();
        AssertRangeState(cut, id, 0, (0, 7));
    }

    [Fact]
    public void MouseAndKeyboardSelectionMaintainCorrectIndexAndVisibleRange_3()
    {
        //combined single scroll and paging

        var scrollIntoView = AddScrollIntoViewFromQuery();
        AddGetElementBoundRectFromQuery(new BoundingRect(0, 0, 0,0,400,0,0,0));
        var scrollDownHandler = AddScrollDown();
        var scrollUpHandler = AddScrollUp();

        var testData = GetTestCollection();
        const string id = "scroll-by-keyboard";
        var i = 0;
        var cut = RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.Data, testData)
            .Add(p => p.Template, item => $"<span id='item'>{item.Id}</span>")
            .Add(p => p.Virtualize, true)
            .Add(p => p.OverscanCount, 20)
            .Add(p => p.FocusItemOption, FocusItemOption.AllowAndAlwaysFocusOnLoad)

            //expecting 8 items per page
            .Add(p => p.ItemsContainerHeightPixels, 400)
            .Add(p => p.ItemSizePixels, 50)
        );

        cut.WaitForElement($"#{id}-item-row-{i}");
        cut.Render();

        //initially item i has focus
        var lister = cut.Find("cctc-lister-items-container");

        //check curr index and visible range
        AssertRangeState(cut, id, i, (0,7));

        //keydown correct
        lister.KeyDown(Key.Down);
        scrollDownHandler.SetVoidResult();
        scrollIntoView.SetVoidResult();
        AssertRangeState(cut, id, 1, (0, 7));

        //page down correct
        lister.KeyDown(Key.PageDown);
        scrollDownHandler.SetVoidResult();
        AssertRangeState(cut, id, 9, (8, 15));

        //keydown correct
        lister.KeyDown(Key.Down);
        scrollDownHandler.SetVoidResult();
        AssertRangeState(cut, id, 10, (8, 15));

        //keydown correct
        lister.KeyDown(Key.Down);
        scrollDownHandler.SetVoidResult();
        AssertRangeState(cut, id, 11, (8, 15));

        //page down correct
        lister.KeyDown(Key.PageDown);
        scrollDownHandler.SetVoidResult();
        AssertRangeState(cut, id, 19, (16, 23));

        //keyup correct
        lister.KeyDown(Key.Up);
        scrollUpHandler.SetVoidResult();
        AssertRangeState(cut, id, 18, (16, 23));

        //keyup correct
        lister.KeyDown(Key.Up);
        scrollUpHandler.SetVoidResult();
        AssertRangeState(cut, id, 17, (16, 23));

        //keyup correct
        lister.KeyDown(Key.Up);
        scrollUpHandler.SetVoidResult();
        AssertRangeState(cut, id, 16, (16, 23));

        //keyup correct
        lister.KeyDown(Key.Up);
        scrollUpHandler.SetVoidResult();
        AssertRangeState(cut, id, 15, (15, 22));

        //pageup correct
        lister.KeyDown(Key.PageUp);
        scrollUpHandler.SetVoidResult();
        AssertRangeState(cut, id, 7, (7, 14));

        //keydown correct
        lister.KeyDown(Key.Down);
        scrollDownHandler.SetVoidResult();
        AssertRangeState(cut, id, 8, (7, 14));
    }

        [Fact]
    public void MouseAndKeyboardSelectionMaintainCorrectIndexAndVisibleRange_4()
    {
        // this routine of navigation results in the focused item not in view
        // even though the current item is correct. Its odd.
        // Just can't seem to fathom what the issue is here.
        // the curr item and visible range both show correctly so why not the UI????

        /*
         * click on index 7
         * page down
         * move down 1
         * page down
         * issue -> the selected item is not in view and the visible range is off by one
         */

        AddGetElementBoundRectFromQuery(new BoundingRect(0, 0, 0,0,400,0,0,0));
        var scrollDownHandler = AddScrollDown();
        var scrollUpHandler = AddScrollUp();
        var scrollIntoView = AddScrollIntoViewFromQuery();

        var testData = GetTestCollection();
        const string id = "scroll-by-keyboard";
        var i = 0;
        var cut = RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.Data, testData)
            .Add(p => p.Template, item => $"<span id='item'>{item.Id}</span>")
            .Add(p => p.Virtualize, true)
            .Add(p => p.OverscanCount, 20)
            .Add(p => p.FocusItemOption, FocusItemOption.AllowAndAlwaysFocusOnLoad)

            //expecting 8 items per page
            .Add(p => p.ItemsContainerHeightPixels, 400)
            .Add(p => p.ItemSizePixels, 50)
        );

        cut.WaitForElement($"#{id}-item-row-{i}");
        cut.Render();

        //initially item i has focus
        var lister = cut.Find("cctc-lister-items-container");

        //check curr index and visible range
        AssertRangeState(cut, id, i, (0,7));

        //click index 7
        var index7 = cut.Find($"#{id}-item-row-{7}");
        index7.Click();
        scrollIntoView.SetVoidResult();

        AssertRangeState(cut, id, 7, (0, 7));

        //page down correct
        lister.KeyDown(Key.PageDown);
        scrollDownHandler.SetVoidResult();
        scrollDownHandler.VerifyInvoke("scrollDown", 1);
        AssertRangeState(cut, id, 15, (8, 15));

        //keydown correct
        lister.KeyDown(Key.Down);
        scrollDownHandler.SetVoidResult();
        scrollDownHandler.VerifyInvoke("scrollDown", 2);
        AssertRangeState(cut, id, 16, (9, 16));

        //page down correct
        //however, when following these steps in the ui, the currently selected item
        //row 24 is not in view
        lister.KeyDown(Key.PageDown);
        scrollDownHandler.VerifyInvoke("scrollDown", 3);
        scrollDownHandler.SetVoidResult();
        AssertRangeState(cut, id, 24, (17, 24));
    }


    [Fact]
    public async Task FilterLoadingFuncWithFocusItem()
    {
        var scrollIntoView = AddScrollIntoViewFromQuery();
        var testSchedulers = new TestSchedulers();
        Services.AddTestSchedulers(sp => testSchedulers);

        const string id = "some-name";
        var testData = GetTestCollection();
        (int? total, int? selected, int? included) allCounts = (null, null, null);
        var filteredList = new List<TestListerItem>();

        var cut = RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.ListerLoadingFunc, _ => Task.FromResult(testData))
            .Add(p => p.Template, item => $"<span id='item'>{item.Id}</span>")
            .Add(p => p.Virtualize, true)
            .Add(p => p.CanFilter, true)
            .Add(p => p.CountDisplay, ListerCountDisplay.FilterAndTotal)
            .Add(p => p.OnFilteredItems, callback => filteredList = callback)
            .Add(p => p.CurrentCounts, callback => allCounts = callback)
            .Add(p => p.CountDisplayPostText, "some further text")
            .Add(p => p.OverscanCount, 20)
            .Add(p => p.FocusItemOption, FocusItemOption.Allow)

            //expecting 8 items per page
            .Add(p => p.ItemsContainerHeightPixels, 400)
            .Add(p => p.ItemSizePixels, 50)
        );

        cut.WaitForAssertion(() => cut.HasComponent<Text>());

        //apply the search text
        var textInput = cut.FindComponent<Text>();
        await textInput.InvokeAsync(() =>
        {
            textInput.Find("input").Input("some 2");
            testSchedulers.Default.AdvanceBy(TimeSpan.FromMilliseconds(ComponentDefaultThrottleMs).Ticks);
        });

        //expect 11 which is item 'some 2' and items 'some 20', 'some 21' etc.
        Assert.Equal(11, filteredList.Count);
        Assert.Equal((100, null, 11), allCounts);
        var counts = cut.Find($"#{id}-display-counts");
        Assert.Equal("11 of 100 some further text", counts.TextContent);

        //now click an item

        var index2 = cut.Find($"#{id}-item-row-{2}");
        index2.Click();
        scrollIntoView.SetVoidResult();

        cut.Render();

        Assert.Equal("some 2", cut.FindComponent<Text>().Instance.Value);
    }

    [Fact]
    public async Task IfFilteringCannotReorderItems()
    {
        var testSchedulers = new TestSchedulers();
        Services.AddTestSchedulers(sp => testSchedulers);

        const string id = "some-name";
        var testData = GetTestCollection();

        var cut = RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.ListerLoadingFunc, _ => Task.FromResult(testData))
            .Add(p => p.Template, item => $"<span id='item'>{item.Id}</span>")
            .Add(p => p.Virtualize, true)
            .Add(p => p.CanFilter, true)
            .Add(p => p.CanReOrder, true)
            .Add(p => p.CountDisplay, ListerCountDisplay.FilterAndTotal)
            .Add(p => p.FocusItemOption, FocusItemOption.Allow)
        );

        cut.WaitForAssertion(() => cut.HasComponent<Text>());

        //move up and move down are present as no filter given
        TestHelpers.AssertFound(cut, $"#{id}-movedown-0");
        TestHelpers.AssertFound(cut, $"#{id}-moveup-1");
        TestHelpers.AssertFound(cut, $"#{id}-movedown-1");

        //apply the search text
        var textInput = cut.FindComponent<Text>();
        await textInput.InvokeAsync(() =>
        {
            textInput.Find("input").Input("some 2");
            testSchedulers.Default.AdvanceBy(TimeSpan.FromMilliseconds(ComponentDefaultThrottleMs).Ticks);
        });

        //the move up and move down icons are no longer available
        TestHelpers.AssertNotFound(cut, $"#{id}-movedown-0");
        TestHelpers.AssertNotFound(cut, $"#{id}-moveup-1");
        TestHelpers.AssertNotFound(cut, $"#{id}-movedown-1");
    }


    [Fact]
    public async Task FilterAppliedThenUndoneMaintainsOrder()
    {
        var testSchedulers = new TestSchedulers();
        Services.AddTestSchedulers(sp => testSchedulers);

        const string id = "some-name";
        var testData = GetTestCollection();

        var cut = RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.ListerLoadingFunc, _ => Task.FromResult(testData))
            .Add(p => p.Template, item => $"<span id='item'>{item.Id}</span>")
            .Add(p => p.Virtualize, false)
            .Add(p => p.CanFilter, true)
            .Add(p => p.CountDisplay, ListerCountDisplay.FilterAndTotal)
            .Add(p => p.CountDisplayPostText, "some further text")
            .Add(p => p.FocusItemOption, FocusItemOption.Allow)
        );

        //get the items in original order
        var originalOrder =
            cut.Instance.WrappedData.ToList()
                .Where(x => x.Included)
                .Select(x => x.ItemData.Id).ToArray();

        //the original order is as expected
        var expOrig = new int[100];
        for (var i = 0; i < 100; i++)
        {
            expOrig[i] = i;
        }

        Assert.Equal(expOrig, originalOrder);

        //apply the search text
        var textInput = cut.FindComponent<Text>();
        await textInput.InvokeAsync(() =>
        {
            textInput.Find("input").Input("some 2");
            testSchedulers.Default.AdvanceBy(TimeSpan.FromMilliseconds(ComponentDefaultThrottleMs).Ticks);
        });

        //the original order has changed due to filter
        var newOrder =
            cut.Instance.WrappedData
                .Where(x => x.Included)
                .Select(x => x.ItemData.Id).ToArray();

        Assert.Equal(new [] { 2, 20, 21, 22, 23 ,24 ,25 ,26 ,27 ,28 ,29 }, newOrder);

        //remove filter and order reverts back to original
        await textInput.InvokeAsync(() =>
        {
            textInput.Find("input").Input("");
            testSchedulers.Default.AdvanceBy(TimeSpan.FromMilliseconds(ComponentDefaultThrottleMs).Ticks);
        });

        Assert.Equal(expOrig, originalOrder);
    }

    [Fact]
    public void FocusItemOptionAllowsScrollingButNoItemIsSelectedInitially()
    {
        AddScrollIntoViewFromQuery();
        AddElementExists(true);
        var testData = GetTestCollection();

        const string id = "scroll-by-keyboard";
        var i = -1;
        var cut = RenderComponent<Lister<TestListerItem>>(parameters => parameters
            .Add(p => p.Id, id)
            .Add(p => p.Data, testData)
            .Add(p => p.Template, item => $"<span id='item'>{item.Id}</span>")
            .Add(p => p.Virtualize, true)
            .Add(p => p.FocusItemOption, FocusItemOption.Allow)
        );

        //initially no item
        cut.WaitForElement($"cctc-lister-items-container");
        var lister = cut.Find("cctc-lister-items-container");

        TestHelpers.AssertNotFound(() => cut.Find($"[{DataHasFocusAttr}]"));
        Assert.Equal(i, cut.Instance.GetCurrentFocusIndex);

        //make the initial selection with mouse
        i = 3;

        cut.WaitForElement($"#{id}-item-row-{i}");

        var item = cut.Find($"#{id}-item-row-{i}");
        item.Click(new MouseEventArgs());

        Assert.Equal(i, cut.Instance.GetCurrentFocusIndex);

        //now can use keyboard as usual

        //make a keyup
        lister.KeyDown(Key.Down);
        cut.Render();
        TestHelpers.AssertFound(() => cut.Find($"#{id}-item-row-{i + 1}[{DataHasFocusAttr}]"));
        Assert.Equal(i + 1, cut.Instance.GetCurrentFocusIndex);

        //second keyup
        lister.KeyDown(Key.Down);
        cut.Render();
        TestHelpers.AssertFound(() => cut.Find($"#{id}-item-row-{i + 2}[{DataHasFocusAttr}]"));
        Assert.Equal(i + 2, cut.Instance.GetCurrentFocusIndex);

        //third keyup
        lister.KeyDown(Key.Up);
        cut.Render();
        TestHelpers.AssertFound(() => cut.Find($"#{id}-item-row-{i + 1}[{DataHasFocusAttr}]"));
        Assert.Equal(i + 1, cut.Instance.GetCurrentFocusIndex);
    }
}