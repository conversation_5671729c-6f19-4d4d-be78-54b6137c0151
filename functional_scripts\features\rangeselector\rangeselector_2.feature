@component @rangeselector @rangeselector_2
Feature: all range selector data types are displayed correctly
    <PERSON>ena<PERSON>: the range selector displays integer values correctly
        Given the user is at the home page
        When the user selects the "Range selector" component in the container "Input"
        Then the range selector displays "integer" values from "0" to "100"
        And the "lower" value is 25
        And the "upper" value is 75

    Scenario: the range selector displays double values correctly
        Given the user is at the home page
        And the user selects the "Range selector" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Double Range"
        When the range selector displays "double" values from "0.00" to "10.00"
        Then the values are formatted to 2 decimal places
        And the "lower" value is "2.50"
        And the "upper" value is "7.80"

    Scenario: the range selector displays date values correctly
        Given the user is at the home page
        And the user selects the "Range selector" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Date Range"
        When the range selector displays "date" values from "2024-01-01" to "2024-12-31"
        Then the values are formatted "YYYY-MM-DD"
        And the "lower" value is "2024-03-15"
        And the "upper" value is "2024-09-15"

    Scenario: the range selector displays time values correctly
        Given the user is at the home page
        And the user selects the "Range selector" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Time Range (TimeSpan)"
        When the range selector displays "time" values from "00:00" to "24:00"
        Then the values are formatted "HH:mm"
        And the "lower" value is "09:00"
        And the "upper" value is "17:00"