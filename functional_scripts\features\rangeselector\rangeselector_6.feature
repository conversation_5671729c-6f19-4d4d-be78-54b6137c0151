@component @rangeselector @rangeselector_6
Feature: the range selector supports accessibility and keyboard navigation
    Scenario: the range selector has proper ARIA attributes
        Given the user is at the home page
        And the user selects the "Range selector" component in the container "Input"
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "Integer Range"
        Then the lower thumb has role "slider"
        And the lower thumb has aria-label "Lower range handle"
        And the upper thumb has role "slider"

        # the above is possibly ai rubbish- dont know if this is over the top and not required 

    Scenario: the range selector responds to keyboard navigation horizontally
        Given the user is at the home page
        And the user selects the "Range selector" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Integer Range"
        And the "lower" value is 20
        And the "upper" value is 80
        When the user focuses on the lower thumb
        And the user presses the right arrow key 5 times
        Then the "lower" value is 25
        When the user focuses on the upper thumb
        And the user presses the left arrow key 5 times
        Then the "upper" value is 75

    Scenario: the range selector responds to keyboard navigation vertically
        Given the user is at the home page 
        And the user selects the "Range selector" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Vertical Orientation"
        And the "lower" value is 30
        And the "upper" value is 70
        When the user focuses on the lower thumb
        And the user presses the up arrow key 5 times
        Then the "lower" value is 35
        When the user focuses on the upper thumb
        And the user presses the down arrow key 5 times
        Then the "upper" value is 65
