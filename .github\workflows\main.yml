name: CCTC_Components
on:
  push:
    branches:
    - main
  pull_request:
    branches:
    - main

env:
  CONFIG: Release

jobs:
  build:
    runs-on: windows-latest

    steps:

    # required to set up paket
    - uses: actions/checkout@v4
    - name: Setup .NET 9
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: 9.0.x

    - name: restore dotnet tools
      run: dotnet tool restore

    - name: paket restore
      env:
        GITHUBUSERNAME: ${{ secrets.GITHUBUSERNAME}}
        GITHUBPASSWORD: ${{ secrets.GITHUBPASSWORD}}
      run: dotnet paket restore

    - name: install sass globally
      run: npm install -g sass

    - name: install node packages
      run: |
        cd src/CCTC_Components_UI/wwwroot/css
        npm install
        
    - name: build
      run: dotnet build --configuration ${{ env.CONFIG }}

    - name: test
      run: dotnet test --no-build --configuration ${{ env.CONFIG }} --verbosity normal
