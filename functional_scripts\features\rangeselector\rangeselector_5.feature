@component @rangeselector @rangeselector_5
Feature: multiple range selectors work independently and simultaneously
    Scenario: multiple range selectors display different data types independently
        Given the user is at the home page
        And the user selects the "Range selector" component in the container "Input"
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "Multiple Sliders"
        Then the range selector titled "price" displays "double" data type values
        And the range selector titled "quantity" displays "integer" data type values
        And the range selector label displays "Price: £100.50 - £500.75 Quantity: 5 - 25"


    Scenario: changing one range selector does not affect others
        Given the user is at the home page
        And the user selects the "Range selector" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Multiple Sliders"
        And the range selector titled "Price" has the lower thumb at position 10%
        And the range selector titled "Price" has the upper thumb at position 50%
        And the range selector titled "Quantity" has the lower thumb at position 10%
        And the range selector titled "Quantity" has the upper thumb at position 50%
        When the user drags the range selector titled "Price" lower thumb to position 20%
        And the user drags the range selector titled "Price" upper thumb to position 70%
        Then the range selector titled "Quantity" has the lower thumb at position 10%
        And the range selector titled "Quantity" has the upper thumb at position 50%


    Scenario: multiple vertical range selectors work independently
        Given the user is at the home page
        And the user selects the "Range selector" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Multiple Vertical Controls"
        And the vertical range selector titled "Bass" has the lower thumb at position 20%
        And the vertical range selector titled "Bass" has the upper thumb at position 60%
        And the vertical range selector titled "Mid" has the lower thumb at position 30%
        And the vertical range selector titled "Mid" has the upper thumb at position 80%
        And the vertical range selector titled "Treble" has the lower thumb at position 40%
        And the vertical range selector titled "Treble" has the upper thumb at position 90%
        When the user drags the range selector titled "Bass" lower thumb to position 30%
        And the user drags the range selector titled "Bass" upper thumb to position 70%
        Then the vertical range selector titled "Mid" has the lower thumb at position 30%
        And the vertical range selector titled "Mid" has the upper thumb at position 70%
        And the vertical range selector titled "Treble" has the lower thumb at position 40%
        And the vertical range selector titled "Treble" has the upper thumb at position 90%
        When the user drags the range selector titled "Mid" lower thumb to position 40%
        And the user drags the range selector titled "Mid" upper thumb to position 80%
        Then the vertical range selector titled "Bass" has the lower thumb at position 30%
        And the vertical range selector titled "Bass" has the upper thumb at position 70%
        And the vertical range selector titled "Treble" has the lower thumb at position 40%
        And the vertical range selector titled "Treble" has the upper thumb at position 90%
        When the user drags the range selector titled "Treble" lower thumb to position 25%
        And the user drags the range selector titled "Treble" upper thumb to position 85%
        Then the vertical range selector titled "Bass" has the lower thumb at position 30%
        And the vertical range selector titled "Bass" has the upper thumb at position 70%
        And the vertical range selector titled "Mid" has the lower thumb at position 40%
        And the vertical range selector titled "Mid" has the upper thumb at position 80%
