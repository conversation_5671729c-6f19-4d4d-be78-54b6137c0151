import { ICustomWorld } from './custom-world';
import { hasDifferences } from '../utils/compareImages';
import { config } from '../support/config';
import { Page, Locator, PageScreenshotOptions } from '@playwright/test';

/**
 * A function to provide an element locator for the sampler tabs selected content
 * @param customWorld - Custom world
 * @returns Returns an element locator for the sampler tabs selected content
 */
function getSamplerTabsSelectedContent(customWorld: ICustomWorld) {
  return customWorld.page!.locator('[id$=sampler] cctc-tabs-selected-content');
}

/**
 * A function which determines if any element satisfies a condition
 * @param locator - The locator which can point to a single element or a collection of elements
 * @param predicate - The function used to test the locator
 * @returns Promise<boolean> true if the collection is not empty and at least one locator in the collection satisfies the predicate; otherwise false
 */
async function any(locator: Locator, predicate: (locator: Locator) => Promise<boolean>) {
  for (const loctr of await locator.all()) {
    if (await predicate(loctr)) {
      return true;
    }
  }

  return false;
}

/**
 * A function which determines if all elements satisfy a condition
 * @param locator - The locator which can point to a single element or a collection of elements
 * @param predicate - The function used to test the locator
 * @returns Promise<boolean> true if every locator in the collection satisfies the predicate or if the collection is empty; otherwise false
 */
async function all(locator: Locator, predicate: (locator: Locator) => Promise<boolean>) {
  for (const loctr of await locator.all()) {
    if (!(await predicate(loctr))) {
      return false;
    }
  }

  return true;
}

/**
 * A function to provide a stable screenshot if possible before the maximum number of retries is exceeded
 * @param locator - The locator for the screenshot
 * @param threshold the difference threshold
 * @param maxRetries - The maximum number of retries
 * @returns Promise<Buffer> the stable screenshot if possible
 */
async function tryGetStableScreenshot(
  locator: Locator,
  threshold?: { threshold: number },
  maxRetries = config.SCREENSHOT_MAX_RETRIES
) {
  let difference: boolean;
  let screenshot1 = await locator.screenshot();
  let screenshot2 = Buffer.from([]) as Buffer;
  for (let i = 0; i < maxRetries; i++) {
    screenshot2 = await locator.screenshot();
    difference = hasDifferences(screenshot1, screenshot2, threshold);
    if (!difference) {
      break;
    }
    screenshot1 = screenshot2;
  }
  return screenshot2;
}

/**
 * A function to provide a stable page screenshot if possible before the maximum number of retries is exceeded
 * @param page - The page for the screenshot
 * @param options - The page screenshot options
 * @param threshold the difference threshold
 * @param maxRetries - The maximum number of retries
 * @returns Promise<Buffer> the stable screenshot if possible
 */
async function tryGetStablePageScreenshot(
  page: Page,
  options?: PageScreenshotOptions,
  threshold?: { threshold: number },
  maxRetries = config.SCREENSHOT_MAX_RETRIES
) {
  let difference: boolean;
  let screenshot1 = await page.screenshot(options);
  let screenshot2 = Buffer.from([]) as Buffer;
  for (let i = 0; i < maxRetries; i++) {
    screenshot2 = await page.screenshot(options);
    difference = hasDifferences(screenshot1, screenshot2, threshold);
    if (!difference) {
      break;
    }
    screenshot1 = screenshot2;
  }
  return screenshot2;
}

/**
 * Waits for all animations to complete on a specified element
 * @param page - The Playwright Page object
 * @param selector - CSS selector for the target element
 * @param timeout - Maximum time to wait for animations to complete (default: 1000ms)
 * @param polling - How frequently to check animation state (default: 50ms)
 * @throws {TimeoutError} When the element is not found or animations don't complete within the timeout period
 * @returns Promise<void> Resolves when all animations have completed
 */
async function waitForAnimationCompletion(page: Page, selector: string, timeout = 1000, polling = 50) {
  await page.waitForFunction(
    ([selector]) => {
      const element = document.querySelector(selector);
      if (!element) return false;

      const animations = element.getAnimations();
      return animations.length === 0 || animations.every(animation => animation.playState === 'finished');
    },
    [selector],
    { timeout: timeout, polling: polling }
  );
}

export {
  any,
  all,
  getSamplerTabsSelectedContent,
  tryGetStableScreenshot,
  tryGetStablePageScreenshot,
  waitForAnimationCompletion
};
