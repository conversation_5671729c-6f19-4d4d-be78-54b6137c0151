﻿/*
    Use this file to;

    1. provide reusable variables that can be applied for the theme and across components
    2. provide new themes by overriding the style variables consumed by components
    3. override any default bootstrap variables
    4. override any default styling

    a default style is hard styled in the CCTC_Components library
 */

// prefix for CSS vars for cctc
// use of the _ in _prefix prevents overwrite of bootstrap names as bootstrap uses prefix
$_prefix: cctc-;

//from trialview and delete at some point
//--rz-text-title-color: white;
//--rz-root-font-size: 14px;
//--background-blue: #0f2537;
//--background-blue-2: #193E5D;
//--nice-blue-color: #609CEA;
//--nice-blue-color2: #2e4d74;
//--nice-lightblue-color: #35a0d7;
//--top-banner-background: #20374c;
//--nice-green-color: #cee741;
//--hr-grey: #EBEBEB;
//--disabled: #acb3b9;

/*
    --------------------------------------------------------------------------------------------------------------
    to override bootstrap variables set them here
    e.g. $blue: yellow;
    however, colours are usually defined in cctc specific variables
 */

/*
    enabling CSS Grid for Bootstrap 5
    https://getbootstrap.com/docs/5.3/layout/css-grid/
 */
$enable-grid-classes: false;
$enable-cssgrid: true;




/*
    --------------------------------------------------------------------------------------------------------------
    override any key variables that should apply globally
 */

//the font size bootstrap uses to calculate element heights
//note: changing this value means you MUST change the FontSizePixels in CCTCBase.razor
$font-size-base: .875rem; /* i.e. 14px */






@import "node_modules/bootstrap/scss/bootstrap";

$border-radius-sm: 3px;

/*
    --------------------------------------------------------------------------------------------------------------
    override any styling here

    its possible to use the bootstrap variables from this point forward
    e.g. $tv-selected-color: $teal;
 */

code {
    color: var(--cctc-code-color);
}

a {
    color: var(--cctc-link-color);
}

.card {
    background-color: var(--cctc-active-background-color);
    color: var(--cctc-color);
}

/*
    at some point would be nice to set variables for these in media queries, but
    despite following the advice in this article it doesn't seem to work
    https://bholmes.dev/blog/alternative-to-css-variable-media-queries/
 */

//general use variables (in root in cctc-components.css)
//tv theme
$tv-color: white;
$tv-success-color: var(--bs-success);
$tv-info-color: var(--bs-info);
$tv-warning-color: var(--bs-warning);
$tv-danger-color: var(--bs-danger);
$tv-alt-color: #3a474d;
$tv-inactive-color: #2E4D74;
$tv-selected-color: #35a0d7;
$tv-highlight-color: #35a0d7;
$tv-background-color: #0f2537;
$tv-border-radius: $border-radius-sm;
$tv-selected-background-color: #193E5D;
$tv-highlight-background-color: #2E4D74;
$tv-hover-background-color: #193E5D;
$tv-active-background-color: #2E4D74;
$tv-emphasis: #b28b00;
$tv-header-selected-background-color: #0a152a;

//rangeselector theme variables
$tv-rangeselector-track-height: 10px;
$tv-rangeselector-track-background-color: #2E4D74;
$tv-rangeselector-track-border-radius: 3px;
$tv-rangeselector-fill-background-color: #35a0d7;
$tv-rangeselector-thumb-size: 20px;
$tv-rangeselector-thumb-background-color: #35a0d7;
$tv-rangeselector-thumb-border: 2px solid white;
$tv-rangeselector-thumb-border-radius: 50%;
$tv-rangeselector-thumb-hover-box-shadow: 0 0 0 8px rgba(53, 160, 215, 0.2);
$tv-rangeselector-thumb-dragging-box-shadow: 0 0 0 12px rgba(53, 160, 215, 0.3);
$tv-rangeselector-thumb-focus-box-shadow: 0 0 0 8px rgba(53, 160, 215, 0.3);
$tv-rangeselector-thumb-focus-outline-color: #35a0d7;
$tv-rangeselector-labels-font-size: 12px;
$tv-rangeselector-labels-color: #8da5b3;
$tv-rangeselector-disabled-opacity: 0.5;

//TODO: imitate above tv styling for any further themes

.tv {
    --#{$_prefix}color: #{$tv-color};
    --#{$_prefix}success-color: #{$tv-success-color};
    --#{$_prefix}info-color: #{$tv-info-color};
    --#{$_prefix}warning-color: #{$tv-warning-color};
    --#{$_prefix}danger-color: #{$tv-danger-color};
    --#{$_prefix}selected-color: #{$tv-selected-color};
    --#{$_prefix}highlight-color: #{$tv-highlight-color};
    --#{$_prefix}background-color: #{$tv-background-color};
    --#{$_prefix}active-background-color: #{$tv-highlight-background-color};
    --#{$_prefix}border-radius: #{$tv-border-radius};
    --#{$_prefix}highlight-background-color: #{$tv-highlight-background-color};
    --#{$_prefix}icon-color: #{$tv-color};
    --#{$_prefix}icon-active-color: #{$tv-highlight-color};
    --#{$_prefix}readonly-icon-color: #{$tv-alt-color};
    --#{$_prefix}link-color: orange;
    --#{$_prefix}code-color: #ff3ea0;
}

.tabs-tv {
    --#{$_prefix}tabs-color: #{$tv-selected-color};
    --#{$_prefix}tabs-background-color: #{$tv-background-color};
    --#{$_prefix}tabs-header-color: #{$tv-selected-color};
    --#{$_prefix}tabs-header-selected-color: #{$tv-color};
    --#{$_prefix}tabs-header-background-color: #{$tv-background-color};
    --#{$_prefix}tabs-header-selected-background-color: #{$tv-header-selected-background-color};
    --#{$_prefix}tabs-header-highlight-color: #{$tv-color};
}

.panelmenu-tv {
    --#{$_prefix}panelmenu-background-color: #{$tv-background-color};
    --#{$_prefix}panelmenu-hover-background-color: #{$tv-highlight-background-color};
    --#{$_prefix}panelmenu-active-background-color: #{$tv-selected-background-color};
    --#{$_prefix}panelmenu-border-color: #{$tv-background-color};
    --#{$_prefix}panelmenu-highlight-border-color: #{$tv-highlight-color};
}

.concertina-tv {
    --#{$_prefix}concertina-icon-color: #{$tv-color};
    --#{$_prefix}concertina-icon-hover-color: #{$tv-highlight-color};
    --#{$_prefix}concertina-header-color: #{$tv-color};
    --#{$_prefix}concertina-header-background-color: #{$tv-background-color};
    --#{$_prefix}concertina-subheader-color: #{$tv-highlight-color};
    --#{$_prefix}concertina-subheader-background-color: #{$tv-background-color};

    --#{$_prefix}concertinaitem-icon-color: #{$tv-color};
    --#{$_prefix}concertinaitem-icon-hover-color: #{$tv-highlight-color};
    --#{$_prefix}concertinaitem-header-color: #{$tv-color};
    --#{$_prefix}concertinaitem-header-background-color: #{$tv-background-color};
    --#{$_prefix}concertinaitem-subheader-color: #{$tv-highlight-color};
    --#{$_prefix}concertinaitem-subheader-background-color: #{$tv-background-color};
}

.lister-tv {
    --#{$_prefix}lister-color: #{$tv-color};
    --#{$_prefix}lister-background-color: #{$tv-background-color};
    --#{$_prefix}lister-hover-background-color: #{$tv-hover-background-color};
    --#{$_prefix}lister-highlight-background-color: #{$tv-highlight-background-color};
    --#{$_prefix}lister-highlight-color: #{$tv-highlight-color};
    --#{$_prefix}lister-working-template-color: #{$tv-highlight-color};
    --#{$_prefix}lister-filter-header-color: #{$tv-color};
    --#{$_prefix}lister-filter-header-background-color: #{$tv-background-color};
    --#{$_prefix}lister-items-header-color: #{$tv-inactive-color};
    --#{$_prefix}lister-items-header-background-color: #{$tv-header-selected-background-color};
    --#{$_prefix}lister-display-counts-color: #{$tv-color};
    --#{$_prefix}lister-display-counts-background-color: #{$tv-background-color};
}

.input-tv {
    --#{$_prefix}input-color: #{$tv-alt-color};
    --#{$_prefix}input-icon-color: #{$tv-highlight-color};
    --#{$_prefix}input-icon-hover-color: #{$tv-hover-background-color};
    --#{$_prefix}input-readonly-icon-color: #{$tv-inactive-color};
    --#{$_prefix}input-background-color: #{$tv-color};
    --#{$_prefix}input-border-radius: #{$tv-border-radius};
    --#{$_prefix}input-border-width: 0px;
    --#{$_prefix}input-border-style: none;
}

.datatextbox-tv {

    --#{$_prefix}datatextbox-input-color: #{$tv-color};
    --#{$_prefix}datatextbox-input-background-color: #{$tv-background-color};
    --#{$_prefix}datatextbox-input-border-color: #{$tv-color};
    --#{$_prefix}datatextbox-input-border-style: solid;
    --#{$_prefix}datatextbox-input-border-width: 1px;


    --#{$_prefix}datatextbox-dropdown-border-color: #{$tv-color};
    --#{$_prefix}datatextbox-dropdown-background-color: #{$tv-active-background-color};

    --#{$_prefix}datatextbox-icon-color: #{$tv-color};
    --#{$_prefix}datatextbox-icon-active-color: #{$tv-highlight-color};
    --#{$_prefix}datatextbox-icon-hover-color: #{$tv-color};
}

.steps-tv {
    --#{$_prefix}steps-header-background-color: #{$tv-background-color};
    --#{$_prefix}steps-header-color: #{$tv-background-color};
    --#{$_prefix}steps-icon-disabled-color: #{$tv-inactive-color};
    --#{$_prefix}steps-background-color: #{$tv-background-color};
    --#{$_prefix}steps-icon-active-color: #{$tv-color};


    //--cctc-steps-color: var(--cctc-color);
    //--cctc-steps-icon-hover-color: var(--cctc-icon-hover-color);
    //--cctc-steps-icon-disabled-color: var(--cctc-disabled-color);
    //--cctc-steps-header-color: var(--cctc-selected-background-color);
    //--cctc-steps-header-background-color: var(--cctc-icon-active-color);
    //--cctc-steps-header-inactive-color: var(--cctc-inactive-color);
    //--cctc-steps-header-inactive-background-color: var(--cctc-background-color);
}

.infotext-tv {
    --#{$_prefix}infotext-icon-color: #{$tv-color};
    --#{$_prefix}infotext-icon-hover-color: #{$tv-highlight-color};
    --#{$_prefix}infotext-popover-color: #{$tv-color};
    --#{$_prefix}infotext-popover-background-color: #{$tv-highlight-background-color};
}

.infoicon-tv {
    --#{$_prefix}infoicon-image-container-background-color: #{$tv-background-color};
    --#{$_prefix}infoicon-image-container-border-radius: #{$tv-border-radius};
    --#{$_prefix}infoicon-image-border-radius: #{$tv-border-radius};
    --#{$_prefix}infoicon-popover-color: #{$tv-color};
    --#{$_prefix}infoicon-popover-header-color: #{$tv-color};
    --#{$_prefix}infoicon-popover-background-color: #{$tv-highlight-background-color};
    --#{$_prefix}infoicon-popover-header-background-color: #{$tv-selected-background-color};
}

.animatedplaceholder-tv {
    --#{$_prefix}animatedplaceholder-background-color: rgb(21, 53, 79);
    --#{$_prefix}animatedplaceholder-background-image: linear-gradient(to left, rgb(35, 88, 131, .05), rgb(35, 88, 131, .3), rgb(35, 88, 131, .6), rgb(35, 88, 131, .3), rgb(35, 88, 131, .05));
    --#{$_prefix}animatedplaceholder-border-radius: #{$tv-border-radius};
}

.progress-tv {
    --#{$_prefix}progress-accent-color: #{$tv-highlight-color};
}

.pill-tv {
    --#{$_prefix}pill-color: #{$tv-color};
    --#{$_prefix}pill-background-color: #{$tv-highlight-background-color};
    --#{$_prefix}pill-border-color: #{$tv-color};
    --#{$_prefix}pill-success-color: #{$tv-color};
    --#{$_prefix}pill-success-background-color: #{$tv-success-color};
    --#{$_prefix}pill-success-border-color: #{$tv-success-color};
    --#{$_prefix}pill-info-color: #{$tv-color};
    --#{$_prefix}pill-info-background-color: #{$tv-info-color};
    --#{$_prefix}pill-info-border-color: #{$tv-info-color};
    --#{$_prefix}pill-warning-color: #{$tv-color};
    --#{$_prefix}pill-warning-background-color: #{$tv-warning-color};
    --#{$_prefix}pill-warning-border-color: #{$tv-warning-color};
    --#{$_prefix}pill-danger-color: #{$tv-color};
    --#{$_prefix}pill-danger-background-color: #{$tv-danger-color};
    --#{$_prefix}pill-danger-border-color: #{$tv-danger-color};
}

.confirmmodal-tv {
    --#{$_prefix}confirmmodal-background-color: #{$tv-active-background-color};
    --#{$_prefix}confirmmodal-border-radius: #{$tv-border-radius};
}

.button-tv {
    --#{$_prefix}button-color: #{$tv-color};
    --#{$_prefix}button-hover-color: #{$tv-highlight-color};
    --#{$_prefix}button-icon-color: #{$tv-color};
    --#{$_prefix}button-icon-hover-color: #{$tv-highlight-color};
    --#{$_prefix}button-border-radius: #{$tv-border-radius};
    --#{$_prefix}button-border-color: #{$tv-color};
    --#{$_prefix}button-border-hover-color: #{$tv-highlight-color};
}

.switch-tv {
    --#{$_prefix}switch-color: #{$tv-color};
    --#{$_prefix}switch-disabled-color: #{$tv-inactive-color};
    --#{$_prefix}switch-background-color: #{$tv-color};
    --#{$_prefix}switch-active-hover-box-shadow: 0 0 0 1px #{$tv-inactive-color};
    --#{$_prefix}switch-inactive-hover-box-shadow: 0 0 0 1px #{$tv-highlight-color};
    --#{$_prefix}switch-slider-active-background-color: #{$tv-highlight-color};
    --#{$_prefix}switch-slider-inactive-background-color: #{$tv-inactive-color};
    --#{$_prefix}switch-slider-disabled-background-color: #{$tv-inactive-color};
}

.rangeselector-tv {
    --#{$_prefix}rangeselector-track-height: #{$tv-rangeselector-track-height};
    --#{$_prefix}rangeselector-track-background-color: #{$tv-rangeselector-track-background-color};
    --#{$_prefix}rangeselector-track-border-radius: #{$tv-rangeselector-track-border-radius};
    --#{$_prefix}rangeselector-fill-background-color: #{$tv-rangeselector-fill-background-color};
    --#{$_prefix}rangeselector-thumb-size: #{$tv-rangeselector-thumb-size};
    --#{$_prefix}rangeselector-thumb-background-color: #{$tv-rangeselector-thumb-background-color};
    --#{$_prefix}rangeselector-thumb-border: #{$tv-rangeselector-thumb-border};
    --#{$_prefix}rangeselector-thumb-border-radius: #{$tv-rangeselector-thumb-border-radius};
    --#{$_prefix}rangeselector-thumb-hover-box-shadow: #{$tv-rangeselector-thumb-hover-box-shadow};
    --#{$_prefix}rangeselector-thumb-dragging-box-shadow: #{$tv-rangeselector-thumb-dragging-box-shadow};
    --#{$_prefix}rangeselector-thumb-focus-box-shadow: #{$tv-rangeselector-thumb-focus-box-shadow};
    --#{$_prefix}rangeselector-thumb-focus-outline-color: #{$tv-rangeselector-thumb-focus-outline-color};
    --#{$_prefix}rangeselector-labels-font-size: #{$tv-rangeselector-labels-font-size};
    --#{$_prefix}rangeselector-labels-color: #{$tv-rangeselector-labels-color};
    --#{$_prefix}rangeselector-disabled-opacity: #{$tv-rangeselector-disabled-opacity};
}

//light theme
$light-color: #141414;
$light-success-color: var(--bs-success);
$light-info-color: var(--bs-info);
$light-warning-color: var(--bs-warning);
$light-danger-color: var(--bs-danger);
$light-selected-color: #4a5353;
$light-highlight-color: #818181;
$light-background-color: white;
$light-border-color: #767676;
$light-border-radius: $border-radius-sm;
$light-selected-background-color: #b4b4b4;
$light-highlight-background-color: #b4b4b4;

//rangeselector theme variables
$light-rangeselector-track-height: 10px;
$light-rangeselector-track-background-color: #d0d0d0;
$light-rangeselector-track-border-radius: 3px;
$light-rangeselector-fill-background-color: #818181;
$light-rangeselector-thumb-size: 20px;
$light-rangeselector-thumb-background-color: #818181;
$light-rangeselector-thumb-border: 2px solid white;
$light-rangeselector-thumb-border-radius: 50%;
$light-rangeselector-thumb-hover-box-shadow: 0 0 0 8px rgba(129, 129, 129, 0.2);
$light-rangeselector-thumb-dragging-box-shadow: 0 0 0 12px rgba(129, 129, 129, 0.3);
$light-rangeselector-thumb-focus-box-shadow: 0 0 0 8px rgba(129, 129, 129, 0.3);
$light-rangeselector-thumb-focus-outline-color: #818181;
$light-rangeselector-labels-font-size: 12px;
$light-rangeselector-labels-color: #666;
$light-rangeselector-disabled-opacity: 0.5;

.light {
    --#{$_prefix}color: #{$light-color};
    --#{$_prefix}success-color: #{$light-success-color};
    --#{$_prefix}info-color: #{$light-info-color};
    --#{$_prefix}warning-color: #{$light-warning-color};
    --#{$_prefix}danger-color: #{$light-danger-color};
    --#{$_prefix}selected-color: #{$light-selected-color};
    --#{$_prefix}highlight-color: #{$light-highlight-color};
    --#{$_prefix}background-color: #{$light-background-color};
    --#{$_prefix}border-radius: #{$light-border-radius};
    --#{$_prefix}highlight-background-color: #{$light-highlight-background-color};
    --#{$_prefix}icon-color: #{$light-color};
    --#{$_prefix}icon-active-color: #{$light-highlight-color};
    --#{$_prefix}readonly-icon-color: #{$light-color};
    --#{$_prefix}link-color: blue;
    --#{$_prefix}code-color: red;
}

.tabs-light {
    --#{$_prefix}tabs-color: #{$light-color};
    --#{$_prefix}tabs-background-color: #{$light-background-color};
    --#{$_prefix}tabs-header-color: #{$light-color};
    --#{$_prefix}tabs-header-selected-color: #{$light-selected-color};
    --#{$_prefix}tabs-header-background-color: #{$light-background-color};
    --#{$_prefix}tabs-header-selected-background-color: #{$light-selected-background-color};
    --#{$_prefix}tabs-header-highlight-color: #{$light-selected-color};
}

.panelmenu-light {
    --#{$_prefix}panelmenu-background-color: #{$light-background-color};
    --#{$_prefix}panelmenu-hover-background-color: #{$light-highlight-background-color};
    --#{$_prefix}panelmenu-active-background-color: #{$light-selected-background-color};
    --#{$_prefix}panelmenu-border-color: #{$light-background-color};
    --#{$_prefix}panelmenu-highlight-border-color: #{$light-highlight-color};
}

.concertina-light {
    --#{$_prefix}concertina-icon-color: #{$light-color};
    --#{$_prefix}concertina-icon-hover-color: #{$light-highlight-color};
    --#{$_prefix}concertina-header-color: #{$light-color};
    --#{$_prefix}concertina-header-background-color: #{$light-background-color};
    --#{$_prefix}concertina-subheader-color: #{$light-highlight-color};
    --#{$_prefix}concertina-subheader-background-color: #{$light-background-color};

    --#{$_prefix}concertinaitem-icon-color: #{$light-color};
    --#{$_prefix}concertinaitem-icon-hover-color: #{$light-highlight-color};
    --#{$_prefix}concertinaitem-header-color: #{$light-color};
    --#{$_prefix}concertinaitem-header-background-color: #{$light-background-color};
    --#{$_prefix}concertinaitem-subheader-color: #{$light-highlight-color};
    --#{$_prefix}concertinaitem-subheader-background-color: #{$light-background-color};
}

.input-light {
    --#{$_prefix}input-color: #{$light-color};
    --#{$_prefix}input-icon-color: #{$light-selected-color};
    --#{$_prefix}input-icon-hover-color: #{$light-highlight-color};
    --#{$_prefix}input-readonly-icon-color: #{$light-color};
    --#{$_prefix}input-background-color: #{$light-background-color};
    --#{$_prefix}input-border-color: #{$light-border-color};
    --#{$_prefix}input-border-radius: #{$light-border-radius};
}

.infotext-light {
    --#{$_prefix}infotext-icon-color: #{$light-color};
    --#{$_prefix}infotext-icon-hover-color: #{$light-highlight-color};
    --#{$_prefix}infotext-popover-color: #{$light-color};
    --#{$_prefix}infotext-popover-background-color: #{$light-highlight-background-color};
}

.infoicon-light {
    --#{$_prefix}infoicon-image-container-background-color: #{$light-background-color};
    --#{$_prefix}infoicon-image-container-border-radius: #{$light-border-radius};
    --#{$_prefix}infoicon-image-border-radius: #{$light-border-radius};
    --#{$_prefix}infoicon-popover-color: #{$light-color};
    --#{$_prefix}infoicon-popover-header-color: #{$light-color};
    --#{$_prefix}infoicon-popover-background-color: #{$light-highlight-background-color};
    --#{$_prefix}infoicon-popover-header-background-color: #{$light-highlight-color};
}

.animatedplaceholder-light {
    --#{$_prefix}animatedplaceholder-background-color: rgb(167, 167, 167);
    --#{$_prefix}animatedplaceholder-background-image: linear-gradient(to left, rgb(243, 243, 243, .05), rgb(243, 243, 243, .3), rgb(243, 243, 243, .6), rgb(243, 243, 243, .3), rgb(243, 243, 243, .05));
    --#{$_prefix}animatedplaceholder-border-radius: #{$light-border-radius};
}

.progress-light {
    --#{$_prefix}progress-accent-color: #{$light-highlight-color};
}

.pill-light {
    --#{$_prefix}pill-color: #{$light-color};
    --#{$_prefix}pill-background-color: #{$light-highlight-background-color};
    --#{$_prefix}pill-border-color: #{$light-border-color};
    --#{$_prefix}pill-success-color: #{$light-color};
    --#{$_prefix}pill-success-background-color: #{$light-success-color};
    --#{$_prefix}pill-success-border-color: #{$light-success-color};
    --#{$_prefix}pill-info-color: #{$light-color};
    --#{$_prefix}pill-info-background-color: #{$light-info-color};
    --#{$_prefix}pill-info-border-color: #{$light-info-color};
    --#{$_prefix}pill-warning-color: #{$light-color};
    --#{$_prefix}pill-warning-background-color: #{$light-warning-color};
    --#{$_prefix}pill-warning-border-color: #{$light-warning-color};
    --#{$_prefix}pill-danger-color: #{$light-color};
    --#{$_prefix}pill-danger-background-color: #{$light-danger-color};
    --#{$_prefix}pill-danger-border-color: #{$light-danger-color};
}

.confirmmodal-light {
    --#{$_prefix}confirmmodal-background-color: #{$light-selected-color};
    --#{$_prefix}confirmmodal-border-radius: #{$light-border-radius};
}

.button-light {
    --#{$_prefix}button-color: #{$light-color};
    --#{$_prefix}button-hover-color: #{$light-highlight-color};
    --#{$_prefix}button-icon-color: #{$light-color};
    --#{$_prefix}button-icon-hover-color: #{$light-highlight-color};
    --#{$_prefix}button-border-radius: #{$light-border-radius};
    --#{$_prefix}button-border-color: #{$light-border-color};
    --#{$_prefix}button-border-hover-color: #{$light-highlight-color};
}

.switch-light {
    --#{$_prefix}switch-color: #{$light-color};
    --#{$_prefix}switch-background-color: #{$light-color};
    --#{$_prefix}switch-active-hover-box-shadow: 0 0 0 1px #{$light-highlight-background-color};
    --#{$_prefix}switch-inactive-hover-box-shadow: 0 0 0 1px #{$light-highlight-color};
    --#{$_prefix}switch-slider-active-background-color: #{$light-highlight-color};
    --#{$_prefix}switch-slider-inactive-background-color: #{$light-highlight-background-color};
    --#{$_prefix}switch-slider-disabled-background-color: #{$light-highlight-color};
}

.rangeselector-light {
    --#{$_prefix}rangeselector-track-height: #{$light-rangeselector-track-height};
    --#{$_prefix}rangeselector-track-background-color: #{$light-rangeselector-track-background-color};
    --#{$_prefix}rangeselector-track-border-radius: #{$light-rangeselector-track-border-radius};
    --#{$_prefix}rangeselector-fill-background-color: #{$light-rangeselector-fill-background-color};
    --#{$_prefix}rangeselector-thumb-size: #{$light-rangeselector-thumb-size};
    --#{$_prefix}rangeselector-thumb-background-color: #{$light-rangeselector-thumb-background-color};
    --#{$_prefix}rangeselector-thumb-border: #{$light-rangeselector-thumb-border};
    --#{$_prefix}rangeselector-thumb-border-radius: #{$light-rangeselector-thumb-border-radius};
    --#{$_prefix}rangeselector-thumb-hover-box-shadow: #{$light-rangeselector-thumb-hover-box-shadow};
    --#{$_prefix}rangeselector-thumb-dragging-box-shadow: #{$light-rangeselector-thumb-dragging-box-shadow};
    --#{$_prefix}rangeselector-thumb-focus-box-shadow: #{$light-rangeselector-thumb-focus-box-shadow};
    --#{$_prefix}rangeselector-thumb-focus-outline-color: #{$light-rangeselector-thumb-focus-outline-color};
    --#{$_prefix}rangeselector-labels-font-size: #{$light-rangeselector-labels-font-size};
    --#{$_prefix}rangeselector-labels-color: #{$light-rangeselector-labels-color};
    --#{$_prefix}rangeselector-disabled-opacity: #{$light-rangeselector-disabled-opacity};
}

