NUGET
  remote: https://api.nuget.org/v3/index.json
    Azure.Core (1.47.1) - restriction: >= net9.0
      Microsoft.Bcl.AsyncInterfaces (>= 8.0) - restriction: || (>= net462) (>= netstandard2.0)
      System.ClientModel (>= 1.5.1) - restriction: || (>= net462) (>= netstandard2.0)
      System.Memory.Data (>= 8.0.1) - restriction: || (>= net462) (>= netstandard2.0)
    Azure.Identity (1.14.2) - restriction: >= net9.0
      Azure.Core (>= 1.46.1) - restriction: >= netstandard2.0
      Microsoft.Identity.Client (>= 4.73.1) - restriction: >= netstandard2.0
      Microsoft.Identity.Client.Extensions.Msal (>= 4.73.1) - restriction: >= netstandard2.0
      System.Memory (>= 4.5.5) - restriction: >= netstandard2.0
    BlackFox.VsWhere (1.1) - restriction: >= netstandard2.0
      FSharp.Core (>= 4.0.0.1) - restriction: >= net45
      FSharp.Core (>= 4.2.3) - restriction: && (< net45) (>= netstandard2.0)
      Microsoft.Win32.Registry (>= 4.7) - restriction: && (< net45) (>= netstandard2.0)
    BouncyCastle.Cryptography (2.6.1) - restriction: >= net9.0
    Dapper (2.1.66) - restriction: >= net9.0
    Expecto (10.2.3) - restriction: >= net9.0
      FSharp.Core (>= 7.0.200) - restriction: >= net6.0
      Mono.Cecil (>= 0.11.4 < 1.0) - restriction: >= net6.0
    Fake.Api.GitHub (6.0)
      FSharp.Core (>= 6.0.3) - restriction: >= netstandard2.0
      Octokit (>= 0.50) - restriction: >= netstandard2.0
    Fake.BuildServer.GitHubActions (6.0)
      Fake.Core.Environment (>= 6.0) - restriction: >= netstandard2.0
      Fake.Core.Trace (>= 6.0) - restriction: >= netstandard2.0
      FSharp.Core (>= 6.0.3) - restriction: >= netstandard2.0
    Fake.Core.CommandLineParsing (6.1.3) - restriction: >= netstandard2.0
      FParsec (>= 1.1.1) - restriction: >= netstandard2.0
      FSharp.Core (>= 8.0.301) - restriction: >= netstandard2.0
    Fake.Core.Context (6.1.3) - restriction: >= netstandard2.0
      FSharp.Core (>= 8.0.301) - restriction: >= netstandard2.0
    Fake.Core.Environment (6.0)
      FSharp.Core (>= 6.0.3) - restriction: >= netstandard2.0
    Fake.Core.FakeVar (6.1.3) - restriction: >= netstandard2.0
      Fake.Core.Context (>= 6.1.3) - restriction: >= netstandard2.0
      FSharp.Core (>= 8.0.301) - restriction: >= netstandard2.0
    Fake.Core.Process (6.0)
      Fake.Core.Environment (>= 6.0) - restriction: >= netstandard2.0
      Fake.Core.FakeVar (>= 6.0) - restriction: >= netstandard2.0
      Fake.Core.String (>= 6.0) - restriction: >= netstandard2.0
      Fake.Core.Trace (>= 6.0) - restriction: >= netstandard2.0
      Fake.IO.FileSystem (>= 6.0) - restriction: >= netstandard2.0
      FSharp.Core (>= 6.0.3) - restriction: >= netstandard2.0
      System.Collections.Immutable (>= 6.0) - restriction: >= netstandard2.0
    Fake.Core.ReleaseNotes (6.0)
      Fake.Core.SemVer (>= 6.0) - restriction: >= netstandard2.0
      Fake.Core.String (>= 6.0) - restriction: >= netstandard2.0
      FSharp.Core (>= 6.0.3) - restriction: >= netstandard2.0
    Fake.Core.SemVer (6.1.3) - restriction: >= netstandard2.0
      FSharp.Core (>= 8.0.301) - restriction: >= netstandard2.0
    Fake.Core.String (6.1.3) - restriction: >= netstandard2.0
      FSharp.Core (>= 8.0.301) - restriction: >= netstandard2.0
    Fake.Core.Target (6.0)
      Fake.Core.CommandLineParsing (>= 6.0) - restriction: >= netstandard2.0
      Fake.Core.Context (>= 6.0) - restriction: >= netstandard2.0
      Fake.Core.Environment (>= 6.0) - restriction: >= netstandard2.0
      Fake.Core.FakeVar (>= 6.0) - restriction: >= netstandard2.0
      Fake.Core.Process (>= 6.0) - restriction: >= netstandard2.0
      Fake.Core.String (>= 6.0) - restriction: >= netstandard2.0
      Fake.Core.Trace (>= 6.0) - restriction: >= netstandard2.0
      FSharp.Control.Reactive (>= 5.0.2) - restriction: >= netstandard2.0
      FSharp.Core (>= 6.0.3) - restriction: >= netstandard2.0
    Fake.Core.Tasks (6.0) - restriction: >= netstandard2.0
      Fake.Core.Trace (>= 6.0) - restriction: >= netstandard2.0
      FSharp.Core (>= 6.0.3) - restriction: >= netstandard2.0
    Fake.Core.Trace (6.0) - restriction: >= netstandard2.0
      Fake.Core.Environment (>= 6.0) - restriction: >= netstandard2.0
      Fake.Core.FakeVar (>= 6.0) - restriction: >= netstandard2.0
      FSharp.Core (>= 6.0.3) - restriction: >= netstandard2.0
    Fake.Core.UserInput (6.1.3)
      FSharp.Core (>= 8.0.301) - restriction: >= netstandard2.0
    Fake.Core.Xml (6.1.3) - restriction: >= netstandard2.0
      Fake.Core.String (>= 6.1.3) - restriction: >= netstandard2.0
      FSharp.Core (>= 8.0.301) - restriction: >= netstandard2.0
    Fake.Documentation.DocFx (6.0)
      Fake.Core.Process (>= 6.0) - restriction: >= netstandard2.0
      Fake.Core.String (>= 6.0) - restriction: >= netstandard2.0
      Fake.Core.Trace (>= 6.0) - restriction: >= netstandard2.0
      Fake.IO.FileSystem (>= 6.0) - restriction: >= netstandard2.0
      FSharp.Core (>= 6.0.3) - restriction: >= netstandard2.0
    Fake.DotNet.AssemblyInfoFile (6.0)
      Fake.Core.Environment (>= 6.0) - restriction: >= netstandard2.0
      Fake.Core.String (>= 6.0) - restriction: >= netstandard2.0
      Fake.Core.Trace (>= 6.0) - restriction: >= netstandard2.0
      Fake.IO.FileSystem (>= 6.0) - restriction: >= netstandard2.0
      FSharp.Core (>= 6.0.3) - restriction: >= netstandard2.0
    Fake.DotNet.Cli (6.0)
      Fake.Core.Environment (>= 6.0) - restriction: >= netstandard2.0
      Fake.Core.Process (>= 6.0) - restriction: >= netstandard2.0
      Fake.Core.String (>= 6.0) - restriction: >= netstandard2.0
      Fake.Core.Trace (>= 6.0) - restriction: >= netstandard2.0
      Fake.DotNet.MSBuild (>= 6.0) - restriction: >= netstandard2.0
      Fake.DotNet.NuGet (>= 6.0) - restriction: >= netstandard2.0
      Fake.IO.FileSystem (>= 6.0) - restriction: >= netstandard2.0
      FSharp.Core (>= 6.0.3) - restriction: >= netstandard2.0
      Mono.Posix.NETStandard (>= 1.0) - restriction: >= netstandard2.0
      Newtonsoft.Json (>= 13.0.1) - restriction: >= netstandard2.0
    Fake.DotNet.MSBuild (6.0)
      BlackFox.VsWhere (>= 1.1) - restriction: >= netstandard2.0
      Fake.Core.Environment (>= 6.0) - restriction: >= netstandard2.0
      Fake.Core.Process (>= 6.0) - restriction: >= netstandard2.0
      Fake.Core.String (>= 6.0) - restriction: >= netstandard2.0
      Fake.Core.Trace (>= 6.0) - restriction: >= netstandard2.0
      Fake.IO.FileSystem (>= 6.0) - restriction: >= netstandard2.0
      FSharp.Core (>= 6.0.3) - restriction: >= netstandard2.0
      MSBuild.StructuredLogger (>= 2.1.545) - restriction: >= netstandard2.0
    Fake.DotNet.NuGet (6.0)
      Fake.Core.Environment (>= 6.0) - restriction: >= netstandard2.0
      Fake.Core.Process (>= 6.0) - restriction: >= netstandard2.0
      Fake.Core.SemVer (>= 6.0) - restriction: >= netstandard2.0
      Fake.Core.String (>= 6.0) - restriction: >= netstandard2.0
      Fake.Core.Tasks (>= 6.0) - restriction: >= netstandard2.0
      Fake.Core.Trace (>= 6.0) - restriction: >= netstandard2.0
      Fake.Core.Xml (>= 6.0) - restriction: >= netstandard2.0
      Fake.IO.FileSystem (>= 6.0) - restriction: >= netstandard2.0
      Fake.Net.Http (>= 6.0) - restriction: >= netstandard2.0
      FSharp.Core (>= 6.0.3) - restriction: >= netstandard2.0
      Newtonsoft.Json (>= 13.0.1) - restriction: >= netstandard2.0
      NuGet.Protocol (>= 6.0) - restriction: >= netstandard2.0
    Fake.DotNet.Paket (6.0)
      Fake.Core.Process (>= 6.0) - restriction: >= netstandard2.0
      Fake.Core.String (>= 6.0) - restriction: >= netstandard2.0
      Fake.Core.Trace (>= 6.0) - restriction: >= netstandard2.0
      Fake.DotNet.Cli (>= 6.0) - restriction: >= netstandard2.0
      Fake.IO.FileSystem (>= 6.0) - restriction: >= netstandard2.0
      FSharp.Core (>= 6.0.3) - restriction: >= netstandard2.0
    Fake.IO.FileSystem (6.0)
      Fake.Core.String (>= 6.0) - restriction: >= netstandard2.0
      Fake.Core.Trace (>= 6.0) - restriction: >= netstandard2.0
      FSharp.Core (>= 6.0.3) - restriction: >= netstandard2.0
    Fake.Net.Http (6.0) - restriction: >= netstandard2.0
      Fake.Core.Trace (>= 6.0) - restriction: >= netstandard2.0
      FSharp.Core (>= 6.0.3) - restriction: >= netstandard2.0
    Fake.Tools.Git (6.0)
      Fake.Core.Environment (>= 6.0) - restriction: >= netstandard2.0
      Fake.Core.Process (>= 6.0) - restriction: >= netstandard2.0
      Fake.Core.SemVer (>= 6.0) - restriction: >= netstandard2.0
      Fake.Core.String (>= 6.0) - restriction: >= netstandard2.0
      Fake.Core.Trace (>= 6.0) - restriction: >= netstandard2.0
      Fake.IO.FileSystem (>= 6.0) - restriction: >= netstandard2.0
      FSharp.Core (>= 6.0.3) - restriction: >= netstandard2.0
    FParsec (1.1.1) - restriction: >= netstandard2.0
      FSharp.Core (>= 4.3.4) - restriction: || (>= net45) (>= netstandard2.0)
      System.ValueTuple (>= 4.4) - restriction: >= net45
    FSharp.Control.Reactive (6.1.2) - restriction: >= netstandard2.0
      FSharp.Core (>= 6.0.7) - restriction: >= netstandard2.0
      System.Reactive (>= 6.0.1) - restriction: >= netstandard2.0
    FSharp.Core (9.0.300)
    FSharp.Data (6.6)
      FSharp.Core (>= 6.0.1) - restriction: >= netstandard2.0
      FSharp.Data.Csv.Core (>= 6.6) - restriction: >= netstandard2.0
      FSharp.Data.Html.Core (>= 6.6) - restriction: >= netstandard2.0
      FSharp.Data.Http (>= 6.6) - restriction: >= netstandard2.0
      FSharp.Data.Json.Core (>= 6.6) - restriction: >= netstandard2.0
      FSharp.Data.Runtime.Utilities (>= 6.6) - restriction: >= netstandard2.0
      FSharp.Data.WorldBank.Core (>= 6.6) - restriction: >= netstandard2.0
      FSharp.Data.Xml.Core (>= 6.6) - restriction: >= netstandard2.0
    FSharp.Data.Csv.Core (6.6) - restriction: >= netstandard2.0
      FSharp.Core (>= 6.0.1) - restriction: >= netstandard2.0
      FSharp.Data.Runtime.Utilities (>= 6.6) - restriction: >= netstandard2.0
    FSharp.Data.Html.Core (6.6) - restriction: >= netstandard2.0
      FSharp.Core (>= 6.0.1) - restriction: >= netstandard2.0
      FSharp.Data.Csv.Core (>= 6.6) - restriction: >= netstandard2.0
      FSharp.Data.Runtime.Utilities (>= 6.6) - restriction: >= netstandard2.0
    FSharp.Data.Http (6.6) - restriction: >= netstandard2.0
      FSharp.Core (>= 6.0.1) - restriction: >= netstandard2.0
    FSharp.Data.Json.Core (6.6) - restriction: >= netstandard2.0
      FSharp.Core (>= 6.0.1) - restriction: >= netstandard2.0
      FSharp.Data.Http (>= 6.6) - restriction: >= netstandard2.0
      FSharp.Data.Runtime.Utilities (>= 6.6) - restriction: >= netstandard2.0
    FSharp.Data.Runtime.Utilities (6.6) - restriction: >= netstandard2.0
      FSharp.Core (>= 6.0.1) - restriction: >= netstandard2.0
      FSharp.Data.Http (>= 6.6) - restriction: >= netstandard2.0
    FSharp.Data.WorldBank.Core (6.6) - restriction: >= netstandard2.0
      FSharp.Core (>= 6.0.1) - restriction: >= netstandard2.0
      FSharp.Data.Http (>= 6.6) - restriction: >= netstandard2.0
      FSharp.Data.Json.Core (>= 6.6) - restriction: >= netstandard2.0
      FSharp.Data.Runtime.Utilities (>= 6.6) - restriction: >= netstandard2.0
    FSharp.Data.Xml.Core (6.6) - restriction: >= netstandard2.0
      FSharp.Core (>= 6.0.1) - restriction: >= netstandard2.0
      FSharp.Data.Http (>= 6.6) - restriction: >= netstandard2.0
      FSharp.Data.Json.Core (>= 6.6) - restriction: >= netstandard2.0
      FSharp.Data.Runtime.Utilities (>= 6.6) - restriction: >= netstandard2.0
    FSharp.SystemTextJson (1.4.36) - restriction: >= net9.0
      FSharp.Core (>= 4.7) - restriction: >= netstandard2.0
      System.Text.Json (>= 6.0.10) - restriction: >= netstandard2.0
    FsToolkit.ErrorHandling (5.0.1) - restriction: >= net9.0
      FSharp.Core (>= 9.0.300) - restriction: >= net9.0
    FsToolkit.ErrorHandling.TaskResult (4.18) - restriction: >= net9.0
      FsToolkit.ErrorHandling (>= 4.18) - restriction: >= netstandard2.0
    MailKit (4.13) - restriction: >= net9.0
      MimeKit (>= 4.13) - restriction: || (&& (>= net462) (< netstandard2.0)) (&& (< net462) (>= netstandard2.0)) (>= net47) (>= netstandard2.1)
      System.Formats.Asn1 (>= 8.0.1) - restriction: || (&& (>= net462) (< netstandard2.0)) (&& (< net462) (>= netstandard2.0)) (>= net47) (>= netstandard2.1)
    Microsoft.Bcl.AsyncInterfaces (9.0.7) - restriction: || (&& (>= net462) (>= netstandard2.0)) (>= net472) (&& (< net8.0) (>= netstandard2.0)) (>= net9.0)
      System.Threading.Tasks.Extensions (>= 4.5.4) - restriction: || (>= net462) (&& (>= netstandard2.0) (< netstandard2.1))
    Microsoft.Bcl.Cryptography (9.0.7) - restriction: >= net9.0
    Microsoft.Build.Framework (17.14.8) - restriction: >= netstandard2.0
      Microsoft.Win32.Registry (>= 5.0) - restriction: && (< net472) (< net9.0) (>= netstandard2.0)
      System.Diagnostics.DiagnosticSource (>= 9.0) - restriction: || (>= net472) (&& (< net9.0) (>= netstandard2.0))
      System.Memory (>= 4.6) - restriction: || (>= net472) (&& (< net9.0) (>= netstandard2.0))
      System.Runtime.CompilerServices.Unsafe (>= 6.1) - restriction: || (>= net472) (&& (< net9.0) (>= netstandard2.0))
      System.Security.Principal.Windows (>= 5.0) - restriction: && (< net472) (< net9.0) (>= netstandard2.0)
      System.Text.Json (>= 9.0) - restriction: >= net472
      System.Threading.Tasks.Extensions (>= 4.6) - restriction: >= net472
    Microsoft.Build.Utilities.Core (17.14.8) - restriction: >= netstandard2.0
      Microsoft.Build.Framework (>= 17.14.8) - restriction: >= netstandard2.0
      Microsoft.IO.Redist (>= 6.1) - restriction: >= net472
      Microsoft.NET.StringTools (>= 17.14.8) - restriction: >= netstandard2.0
      Microsoft.Win32.Registry (>= 5.0) - restriction: && (< net472) (< net9.0) (>= netstandard2.0)
      System.Collections.Immutable (>= 9.0) - restriction: >= netstandard2.0
      System.Configuration.ConfigurationManager (>= 9.0) - restriction: >= netstandard2.0
      System.Diagnostics.DiagnosticSource (>= 9.0) - restriction: || (>= net472) (&& (< net9.0) (>= netstandard2.0))
      System.Diagnostics.EventLog (>= 9.0) - restriction: >= net9.0
      System.Memory (>= 4.6) - restriction: || (>= net472) (&& (< net9.0) (>= netstandard2.0))
      System.Runtime.CompilerServices.Unsafe (>= 6.1) - restriction: || (>= net472) (&& (< net9.0) (>= netstandard2.0))
      System.Security.Cryptography.ProtectedData (>= 9.0) - restriction: || (&& (< net472) (>= netstandard2.0)) (>= net9.0)
      System.Security.Principal.Windows (>= 5.0) - restriction: && (< net472) (< net9.0) (>= netstandard2.0)
      System.Text.Encoding.CodePages (>= 9.0) - restriction: && (< net472) (< net9.0) (>= netstandard2.0)
      System.Text.Json (>= 9.0) - restriction: >= net472
      System.Threading.Tasks.Extensions (>= 4.6) - restriction: >= net472
    Microsoft.Data.SqlClient (6.0.2) - restriction: >= net9.0
      Azure.Identity (>= 1.11.4) - restriction: || (>= net462) (>= net8.0)
      Microsoft.Bcl.Cryptography (>= 9.0.4) - restriction: >= net9.0
      Microsoft.Data.SqlClient.SNI.runtime (>= 6.0.2) - restriction: >= net8.0
      Microsoft.Extensions.Caching.Memory (>= 9.0.4) - restriction: >= net9.0
      Microsoft.IdentityModel.JsonWebTokens (>= 7.5) - restriction: || (>= net462) (>= net8.0)
      Microsoft.IdentityModel.Protocols.OpenIdConnect (>= 7.5) - restriction: || (>= net462) (>= net8.0)
      Microsoft.SqlServer.Server (>= 1.0) - restriction: >= net8.0
      System.Configuration.ConfigurationManager (>= 9.0.4) - restriction: >= net9.0
      System.Security.Cryptography.Pkcs (>= 9.0.4) - restriction: >= net9.0
    Microsoft.Data.SqlClient.SNI.runtime (6.0.2) - restriction: >= net9.0
    Microsoft.Extensions.Caching.Abstractions (9.0.7) - restriction: >= net9.0
      Microsoft.Extensions.Primitives (>= 9.0.7) - restriction: || (>= net462) (>= netstandard2.0)
    Microsoft.Extensions.Caching.Memory (9.0.7) - restriction: >= net9.0
      Microsoft.Extensions.Caching.Abstractions (>= 9.0.7) - restriction: || (>= net462) (>= netstandard2.0)
      Microsoft.Extensions.DependencyInjection.Abstractions (>= 9.0.7) - restriction: || (>= net462) (>= netstandard2.0)
      Microsoft.Extensions.Logging.Abstractions (>= 9.0.7) - restriction: || (>= net462) (>= netstandard2.0)
      Microsoft.Extensions.Options (>= 9.0.7) - restriction: || (>= net462) (>= netstandard2.0)
      Microsoft.Extensions.Primitives (>= 9.0.7) - restriction: || (>= net462) (>= netstandard2.0)
    Microsoft.Extensions.DependencyInjection.Abstractions (9.0.7) - restriction: >= net9.0
    Microsoft.Extensions.Logging.Abstractions (9.0.7) - restriction: >= net9.0
      Microsoft.Extensions.DependencyInjection.Abstractions (>= 9.0.7) - restriction: || (>= net462) (>= netstandard2.0)
    Microsoft.Extensions.Options (9.0.7) - restriction: >= net9.0
      Microsoft.Extensions.DependencyInjection.Abstractions (>= 9.0.7) - restriction: || (>= net462) (>= netstandard2.0)
      Microsoft.Extensions.Primitives (>= 9.0.7) - restriction: || (>= net462) (>= netstandard2.0)
    Microsoft.Extensions.Primitives (9.0.7) - restriction: >= net9.0
    Microsoft.Identity.Client (4.74) - restriction: >= net9.0
      Microsoft.IdentityModel.Abstractions (>= 6.35) - restriction: || (>= net462) (>= netstandard2.0)
      System.Diagnostics.DiagnosticSource (>= 6.0.1) - restriction: || (>= net462) (&& (>= net8.0) (< net8.0-android) (< net8.0-ios)) (&& (< net8.0) (>= netstandard2.0))
    Microsoft.Identity.Client.Extensions.Msal (4.74) - restriction: >= net9.0
      Microsoft.Identity.Client (>= 4.74) - restriction: >= netstandard2.0
      System.Security.Cryptography.ProtectedData (>= 4.5) - restriction: >= netstandard2.0
    Microsoft.IdentityModel.Abstractions (8.12.1) - restriction: >= net9.0
    Microsoft.IdentityModel.JsonWebTokens (8.12.1) - restriction: >= net9.0
      Microsoft.IdentityModel.Tokens (>= 8.12.1) - restriction: || (>= net462) (>= netstandard2.0)
    Microsoft.IdentityModel.Logging (8.12.1) - restriction: >= net9.0
      Microsoft.IdentityModel.Abstractions (>= 8.12.1) - restriction: || (>= net462) (>= netstandard2.0)
    Microsoft.IdentityModel.Protocols (8.12.1) - restriction: >= net9.0
      Microsoft.IdentityModel.Tokens (>= 8.12.1) - restriction: || (>= net462) (>= netstandard2.0)
    Microsoft.IdentityModel.Protocols.OpenIdConnect (8.12.1) - restriction: >= net9.0
      Microsoft.IdentityModel.Protocols (>= 8.12.1) - restriction: || (>= net462) (>= netstandard2.0)
      System.IdentityModel.Tokens.Jwt (>= 8.12.1) - restriction: || (>= net462) (>= netstandard2.0)
    Microsoft.IdentityModel.Tokens (8.12.1) - restriction: >= net9.0
      Microsoft.Extensions.Logging.Abstractions (>= 8.0) - restriction: >= net8.0
      Microsoft.IdentityModel.Logging (>= 8.12.1) - restriction: || (>= net462) (>= netstandard2.0)
    Microsoft.IO.Redist (6.1.3) - restriction: >= net472
      System.Buffers (>= 4.6.1) - restriction: >= net472
      System.Memory (>= 4.6.3) - restriction: >= net472
    Microsoft.NET.StringTools (17.14.8) - restriction: >= netstandard2.0
      System.Memory (>= 4.6) - restriction: || (>= net472) (&& (< net9.0) (>= netstandard2.0))
      System.Runtime.CompilerServices.Unsafe (>= 6.1) - restriction: || (>= net472) (&& (< net9.0) (>= netstandard2.0))
    Microsoft.NETCore.Platforms (7.0.4) - restriction: || (&& (>= netcoreapp2.0) (< netcoreapp2.1)) (&& (>= netcoreapp2.1) (< netcoreapp3.0))
    Microsoft.SqlServer.Server (1.0) - restriction: >= net9.0
    Microsoft.Win32.Registry (5.0) - restriction: || (&& (< net45) (>= netstandard2.0)) (&& (< net472) (< net9.0) (>= netstandard2.0))
      System.Buffers (>= 4.5.1) - restriction: || (&& (>= monoandroid) (< netstandard1.3)) (>= monotouch) (&& (< net46) (< netcoreapp2.0) (>= netstandard2.0)) (>= xamarinios) (>= xamarinmac) (>= xamarintvos) (>= xamarinwatchos)
      System.Memory (>= 4.5.4) - restriction: || (&& (< monoandroid) (>= netcoreapp2.0) (< netcoreapp2.1) (< xamarinios) (< xamarinmac) (< xamarintvos) (< xamarinwatchos)) (&& (< net46) (< netcoreapp2.0) (>= netstandard2.0) (< xamarinios) (< xamarinmac) (< xamarintvos) (< xamarinwatchos)) (>= uap10.1)
      System.Security.AccessControl (>= 5.0) - restriction: || (&& (>= monoandroid) (< netstandard1.3)) (&& (< monoandroid) (>= netcoreapp2.0)) (>= monotouch) (&& (< net46) (< netcoreapp2.0) (>= netstandard2.0)) (>= net461) (>= netcoreapp2.1) (>= uap10.1) (>= xamarinios) (>= xamarinmac) (>= xamarintvos) (>= xamarinwatchos)
      System.Security.Principal.Windows (>= 5.0) - restriction: || (&& (>= monoandroid) (< netstandard1.3)) (&& (< monoandroid) (>= netcoreapp2.0)) (>= monotouch) (&& (< net46) (< netcoreapp2.0) (>= netstandard2.0)) (>= net461) (>= netcoreapp2.1) (>= uap10.1) (>= xamarinios) (>= xamarinmac) (>= xamarintvos) (>= xamarinwatchos)
    MimeKit (4.13) - restriction: >= net9.0
      BouncyCastle.Cryptography (>= 2.5.1) - restriction: || (&& (>= net462) (< netstandard2.0)) (&& (< net462) (>= netstandard2.0)) (>= net47) (>= netstandard2.1)
      System.Security.Cryptography.Pkcs (>= 8.0.1) - restriction: || (&& (< net462) (>= netstandard2.0)) (>= netstandard2.1)
    Mono.Cecil (0.11.6) - restriction: >= net9.0
    Mono.Posix.NETStandard (1.0) - restriction: >= netstandard2.0
    MSBuild.StructuredLogger (2.3.17) - restriction: >= netstandard2.0
      Microsoft.Build.Framework (>= 17.5) - restriction: >= netstandard2.0
      Microsoft.Build.Utilities.Core (>= 17.5) - restriction: >= netstandard2.0
      System.Collections.Immutable (>= 8.0) - restriction: >= netstandard2.0
      System.Memory (>= 4.6) - restriction: && (< net8.0) (>= netstandard2.0)
      System.Runtime.CompilerServices.Unsafe (>= 6.1) - restriction: && (< net8.0) (>= netstandard2.0)
    Neo4j.Driver (5.28.2) - restriction: >= net9.0
      System.IO.Pipelines (>= 8.0) - restriction: >= netstandard2.0
    Newtonsoft.Json (13.0.3) - restriction: >= netstandard2.0
    NodaTime (3.2.2) - restriction: >= net9.0
    NodaTime.Serialization.SystemTextJson (1.3) - restriction: >= net9.0
      NodaTime (>= 3.0 < 4.0) - restriction: >= netstandard2.0
    NuGet.Common (6.14) - restriction: >= netstandard2.0
      NuGet.Frameworks (>= 6.14) - restriction: >= netstandard2.0
      System.Collections.Immutable (>= 8.0) - restriction: >= netstandard2.0
    NuGet.Configuration (6.14) - restriction: >= netstandard2.0
      NuGet.Common (>= 6.14) - restriction: >= netstandard2.0
      System.Security.Cryptography.ProtectedData (>= 4.4) - restriction: && (< net472) (>= netstandard2.0)
    NuGet.Frameworks (6.14) - restriction: >= netstandard2.0
    NuGet.Packaging (6.14) - restriction: >= netstandard2.0
      Newtonsoft.Json (>= 13.0.3) - restriction: >= netstandard2.0
      NuGet.Configuration (>= 6.14) - restriction: >= netstandard2.0
      NuGet.Versioning (>= 6.14) - restriction: >= netstandard2.0
      System.Formats.Asn1 (>= 8.0.1) - restriction: && (< net472) (< net8.0) (>= netstandard2.0)
      System.Memory (>= 4.5.5) - restriction: >= net472
      System.Security.Cryptography.Pkcs (>= 6.0.4) - restriction: || (&& (< net472) (>= netstandard2.0)) (>= net8.0)
      System.Text.Json (>= 8.0.5) - restriction: || (>= net472) (&& (< net8.0) (>= netstandard2.0))
    NuGet.Protocol (6.14) - restriction: >= netstandard2.0
      NuGet.Packaging (>= 6.14) - restriction: >= netstandard2.0
      System.Text.Json (>= 8.0.5) - restriction: || (>= net472) (&& (< net8.0) (>= netstandard2.0))
    NuGet.Versioning (6.14) - restriction: >= netstandard2.0
    Octokit (10.0)
    System.Buffers (4.6.1) - restriction: || (&& (>= monoandroid) (< netstandard1.3) (>= netstandard2.0)) (&& (>= monotouch) (>= netstandard2.0)) (&& (< net45) (< netcoreapp2.0) (>= netstandard2.0)) (&& (>= net462) (>= netstandard2.0)) (>= net472) (&& (< net8.0) (>= netstandard2.0)) (&& (< netcoreapp2.1) (>= netstandard2.0) (< netstandard2.1)) (&& (>= netstandard2.0) (>= xamarintvos)) (&& (>= netstandard2.0) (>= xamarinwatchos)) (>= xamarinios) (>= xamarinmac)
    System.ClientModel (1.5.1) - restriction: >= net9.0
      Microsoft.Extensions.Logging.Abstractions (>= 8.0.3) - restriction: >= netstandard2.0
      System.Memory.Data (>= 8.0.1) - restriction: >= netstandard2.0
    System.Collections.Immutable (9.0.7) - restriction: >= netstandard2.0
      System.Memory (>= 4.5.5) - restriction: || (>= net462) (&& (< net8.0) (>= netstandard2.0))
      System.Runtime.CompilerServices.Unsafe (>= 6.0) - restriction: || (>= net462) (&& (< net8.0) (>= netstandard2.0))
    System.Configuration.ConfigurationManager (9.0.7) - restriction: >= netstandard2.0
      System.Diagnostics.EventLog (>= 9.0.7) - restriction: >= net8.0
      System.Security.Cryptography.ProtectedData (>= 9.0.7) - restriction: || (&& (< net462) (>= netstandard2.0)) (>= net8.0)
    System.Diagnostics.DiagnosticSource (9.0.7) - restriction: >= netstandard2.0
      System.Memory (>= 4.5.5) - restriction: || (>= net462) (&& (< net8.0) (>= netstandard2.0))
      System.Runtime.CompilerServices.Unsafe (>= 6.0) - restriction: || (>= net462) (&& (< net8.0) (>= netstandard2.0))
    System.Diagnostics.EventLog (9.0.7) - restriction: >= net9.0
    System.Formats.Asn1 (9.0.7) - restriction: || (&& (< net472) (< net8.0) (>= netstandard2.0)) (>= net9.0)
    System.IdentityModel.Tokens.Jwt (8.12.1) - restriction: >= net9.0
      Microsoft.IdentityModel.JsonWebTokens (>= 8.12.1) - restriction: || (>= net462) (>= netstandard2.0)
      Microsoft.IdentityModel.Tokens (>= 8.12.1) - restriction: || (>= net462) (>= netstandard2.0)
    System.IO.Pipelines (9.0.7) - restriction: || (&& (>= net462) (>= netstandard2.0)) (>= net472) (&& (< net8.0) (>= netstandard2.0)) (>= net9.0)
    System.Memory (4.6.3) - restriction: || (&& (< monoandroid) (>= netcoreapp2.0) (< netcoreapp2.1) (< xamarinios) (< xamarinmac) (< xamarintvos) (< xamarinwatchos)) (>= netstandard2.0)
      System.Buffers (>= 4.6.1) - restriction: || (>= net462) (&& (< netcoreapp2.1) (>= netstandard2.0) (< netstandard2.1))
      System.Numerics.Vectors (>= 4.6.1) - restriction: || (>= net462) (&& (< netcoreapp2.1) (>= netstandard2.0) (< netstandard2.1))
      System.Runtime.CompilerServices.Unsafe (>= 6.1.2) - restriction: || (>= net462) (&& (< netcoreapp2.1) (>= netstandard2.0) (< netstandard2.1))
    System.Memory.Data (9.0.7) - restriction: >= net9.0
    System.Numerics.Vectors (4.6.1) - restriction: || (&& (>= net462) (>= netstandard2.0)) (&& (< netcoreapp2.1) (>= netstandard2.0) (< netstandard2.1))
    System.Reactive (6.0.1) - restriction: >= netstandard2.0
      System.Threading.Tasks.Extensions (>= 4.5.4) - restriction: || (>= net472) (&& (< net6.0) (>= netstandard2.0)) (>= uap10.1)
    System.Runtime.CompilerServices.Unsafe (6.1.2) - restriction: || (&& (>= net462) (>= netstandard2.0)) (>= net472) (&& (< net8.0) (>= netstandard2.0)) (&& (< net9.0) (>= netstandard2.0)) (&& (< netcoreapp2.1) (>= netstandard2.0) (< netstandard2.1))
    System.Security.AccessControl (6.0.1) - restriction: || (&& (>= monoandroid) (< netstandard1.3) (>= netstandard2.0)) (&& (< monoandroid) (>= netcoreapp2.0)) (&& (>= monotouch) (>= netstandard2.0)) (&& (< net45) (>= net461) (>= netstandard2.0)) (&& (< net45) (< netcoreapp2.0) (>= netstandard2.0)) (>= netcoreapp2.1) (&& (>= netstandard2.0) (>= uap10.1)) (&& (>= netstandard2.0) (>= xamarintvos)) (&& (>= netstandard2.0) (>= xamarinwatchos)) (>= xamarinios) (>= xamarinmac)
      System.Security.Principal.Windows (>= 5.0) - restriction: || (>= net461) (&& (< net6.0) (>= netstandard2.0))
    System.Security.Cryptography.Pkcs (9.0.7) - restriction: || (&& (< net472) (>= netstandard2.0)) (>= net8.0)
    System.Security.Cryptography.ProtectedData (9.0.7) - restriction: || (&& (< net472) (>= netstandard2.0)) (>= net9.0)
    System.Security.Principal.Windows (5.0) - restriction: || (&& (>= monoandroid) (< netstandard1.3) (>= netstandard2.0)) (&& (< monoandroid) (>= netcoreapp2.0)) (&& (>= monotouch) (>= netstandard2.0)) (&& (< net45) (>= net461) (>= netstandard2.0)) (&& (< net45) (< netcoreapp2.0) (>= netstandard2.0)) (&& (>= net461) (>= netcoreapp2.0)) (&& (< net472) (< net9.0) (>= netstandard2.0)) (>= netcoreapp2.1) (&& (>= netstandard2.0) (>= uap10.1)) (&& (>= netstandard2.0) (>= xamarintvos)) (&& (>= netstandard2.0) (>= xamarinwatchos)) (>= xamarinios) (>= xamarinmac)
      Microsoft.NETCore.Platforms (>= 5.0) - restriction: || (&& (>= netcoreapp2.0) (< netcoreapp2.1)) (&& (>= netcoreapp2.1) (< netcoreapp3.0))
    System.Text.Encoding.CodePages (9.0.7) - restriction: && (< net472) (< net9.0) (>= netstandard2.0)
      System.Memory (>= 4.5.5) - restriction: || (>= net462) (&& (< net8.0) (>= netstandard2.0))
      System.Runtime.CompilerServices.Unsafe (>= 6.0) - restriction: || (>= net462) (&& (< net8.0) (>= netstandard2.0))
    System.Text.Encodings.Web (9.0.7) - restriction: || (&& (>= net462) (>= netstandard2.0)) (>= net472) (&& (< net8.0) (>= netstandard2.0))
      System.Buffers (>= 4.5.1) - restriction: || (>= net462) (&& (< net8.0) (>= netstandard2.0))
      System.Memory (>= 4.5.5) - restriction: || (>= net462) (&& (< net8.0) (>= netstandard2.0))
      System.Runtime.CompilerServices.Unsafe (>= 6.0) - restriction: || (>= net462) (&& (< net8.0) (>= netstandard2.0))
    System.Text.Json (9.0.7) - restriction: || (>= net472) (&& (< net8.0) (>= netstandard2.0)) (>= net9.0)
      Microsoft.Bcl.AsyncInterfaces (>= 9.0.7) - restriction: || (>= net462) (&& (< net8.0) (>= netstandard2.0))
      System.Buffers (>= 4.5.1) - restriction: || (>= net462) (&& (< net8.0) (>= netstandard2.0))
      System.IO.Pipelines (>= 9.0.7) - restriction: || (>= net462) (&& (>= net8.0) (< net9.0)) (&& (< net8.0) (>= netstandard2.0))
      System.Memory (>= 4.5.5) - restriction: || (>= net462) (&& (< net8.0) (>= netstandard2.0))
      System.Runtime.CompilerServices.Unsafe (>= 6.0) - restriction: || (>= net462) (&& (< net8.0) (>= netstandard2.0))
      System.Text.Encodings.Web (>= 9.0.7) - restriction: || (>= net462) (&& (>= net8.0) (< net9.0)) (&& (< net8.0) (>= netstandard2.0))
      System.Threading.Tasks.Extensions (>= 4.5.4) - restriction: || (>= net462) (&& (< net8.0) (>= netstandard2.0))
      System.ValueTuple (>= 4.5) - restriction: >= net462
    System.Threading.Tasks.Extensions (4.6.3) - restriction: || (&& (>= net462) (>= netstandard2.0)) (>= net472) (&& (< net6.0) (>= netstandard2.0)) (&& (< net8.0) (>= netstandard2.0)) (&& (>= netstandard2.0) (< netstandard2.1)) (&& (>= netstandard2.0) (>= uap10.1))
      System.Runtime.CompilerServices.Unsafe (>= 6.1.2) - restriction: || (>= net462) (&& (< netcoreapp2.1) (>= netstandard2.0) (< netstandard2.1))
    System.ValueTuple (4.6.1) - restriction: || (&& (>= net462) (>= netstandard2.0)) (>= net472)
  remote: https://nuget.pkg.github.com/CCTC-team/index.json
    Lib (1.0.44)
      Dapper (>= 2.1.66) - restriction: >= net9.0
      Expecto (>= 10.2.3) - restriction: >= net9.0
      FSharp.Core (>= 9.0.300) - restriction: >= net9.0
      FSharp.Data (>= 6.6) - restriction: >= net9.0
      FSharp.SystemTextJson (>= 1.4.36) - restriction: >= net9.0
      FsToolkit.ErrorHandling (>= 5.0.1) - restriction: >= net9.0
      FsToolkit.ErrorHandling.TaskResult (>= 4.18) - restriction: >= net9.0
      MailKit (>= 4.13) - restriction: >= net9.0
      Microsoft.Data.SqlClient (>= 6.0.2) - restriction: >= net9.0
      Neo4j.Driver (>= 5.28.2) - restriction: >= net9.0
      NodaTime (>= 3.2.2) - restriction: >= net9.0
      NodaTime.Serialization.SystemTextJson (>= 1.3) - restriction: >= net9.0
