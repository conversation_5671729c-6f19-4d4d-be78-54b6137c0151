@component @panelmenu @panelmenu_4
Feature: the headers can be collapsed and items show tooltips when collapsed in both states
    Scenario: the header is collapsed
        Given the user is at the home page
        And the user selects the "Panel menu" component in the container "Panel menu"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "User collapsible menu"
        And the panel menu expand and collapse arrows point left
        And the panel menu item with Id "MenuItem1" has no tooltip enabled
        And the panel menu matches the base image "expanded panel menu"
        When the user clicks the panel menu expand and collapse bar
        Then the panel menu expand and collapse arrows point right
        And the panel menu item with Id "MenuItem1" has a tooltip enabled containing the title "menuitem without header"
        And the panel menu matches the base image "collapsed panel menu"



