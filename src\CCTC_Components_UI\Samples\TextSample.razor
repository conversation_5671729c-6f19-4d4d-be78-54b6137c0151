﻿@page "/textsample"
@using CCTC_Components.Components.TextBox.TextInteraction

@{
    var description = new List<string>
    {
        "A component for text input. Supports input Masks and Reactive input"
    };

    var features = new List<(string, string)>
    {
        ("Interaction", "Can be made read-only and / or disabled. The read-only icon is optional"),
        ("Constrained input", "Apply an input mask (refer to the Configuration tab), prevent whitespace and / or set a max length"),
        ("Input display", "Has the facility to redact text and / or add a placeholder"),
        ("Binding", "The <code>BindEvent</code> has a default value of <code>OnChange</code>. The component throttle speed can be changed")
    };

    var gotchas = new List<(string, string)>
    {
        ("BindEvent", "If the component is Reactive or if a <code>Mask</code> is applied, the <code>BindEvent</code> is set to <code>OnInput</code> automatically. " +
            "Note that the <code>OnChange</code> event occurs when the input loses focus after the value has changed"),
        ("ThrottleMs", "500 ms is the minimum <code>ThrottleMs</code> for masked input and will be applied automatically unless a higher value is provided"),
        ("--cctc-input-webkit-line-clamp", "CSS variable not used by this component"),
        ("PreventWhitespace", "Ensure an appropriate throttle is set, otherwise the last character cannot be changed")
    };

    var tips = new List<(string, string)>
    {
        ("Target the cctc-input component(s) on a page ignoring any child cctc-input components. For example, setting a uniform input component width adjusting according to screen size",
@"
<code>
    <pre>

    ::deep cctc-input:not(cctc-input cctc-input) {
        width: 100%;
    }

    @media (min-width: 1200px) {
        ::deep cctc-input:not(cctc-input cctc-input) {
            width: 35%;
        }
    }
    </pre>
</code>")
    };

    var usageCode = new List<(string title, string description, string code, RenderFragment fragment)>
    {
        ("With callback and max length of 20", "",
@"<Text
    Id=""usage1""
    Value=""@TextValue""
    ValueChanged=""@TextChanged""
    MaxLength=""20""
    BindEvent=""BindEvent.OnInput"">
</Text>

@code {

    string? TextValue { get; set; } = ""Some demo Text"";

    void TextChanged(string? newValue)
    {
        TextValue = newValue;
        Console.WriteLine($""New value: { newValue ?? ""value cleared"" }"");
    }
}",
        @<CCTC_Components.Components.TextBox.Text
            Id="usage1"
            Value="@TextValue"
            ValueChanged="@TextChanged"
            MaxLength="20"
            BindEvent="BindEvent.OnInput">
        </CCTC_Components.Components.TextBox.Text>),
        ("Reactive with prevent whitespace and redacted text", "",
@"<Text
    Id=""usage2""
    @bind-Value=""@TextValue7""
    ThrottleMs=""@Constants.DefaultThrottleMs""
    PreventWhitespace=""true""
    TextChanged=""@(args => Console.WriteLine(args.Value))""
    RedactText=""true"">
</Text>

@code {

    string TextValue7 { get; set; } = ""Demo Text"";
}",
        @<CCTC_Components.Components.TextBox.Text
            Id="usage2"
            @bind-Value="@TextValue7"
            ThrottleMs="@Constants.DefaultThrottleMs"
            PreventWhitespace="true"
            TextChanged="@(args => Console.WriteLine(args.Value))"
            RedactText="true">
        </CCTC_Components.Components.TextBox.Text>),
        ("Reactive with CssClass applied", "",
@"<Text
    Id=""usage3""
    CssClass=""info-background""
    Value=""@TextValue""
    ValueChanged=""@TextChanged""
    ThrottleMs=""2000"">
</Text

@code {

    string? TextValue { get; set; } = ""Some demo Text"";

    void TextChanged(string? newValue)
    {
        TextValue = newValue;
        Console.WriteLine($""New value: { newValue ?? ""value cleared"" }"");
    }
}",
        @<CCTC_Components.Components.TextBox.Text
            Id="usage3"
            CssClass="info-background"
            Value="@TextValue"
            ValueChanged="@TextChanged"
            ThrottleMs="2000">
        </CCTC_Components.Components.TextBox.Text>),
        ("With mask and style applied", "",
@"<Text
    Id=""usage4""
    Style=""--cctc-input-border-width: 2px;""
    Value=""@TextValue2""
    ValueChanged=""@TextChanged2""
    Placeholder=""(000)000/000/00""
    Mask=""(000)000/000/00"">
</Text>

@code {

    string? TextValue2 { get; set; } = string.Empty;

    void TextChanged2(string? newValue)
    {
        TextValue2 = newValue;
        Console.WriteLine($""New value: { newValue ?? ""value cleared"" }"");
    }
}",
        @<CCTC_Components.Components.TextBox.Text
            Id="usage4"
            Style="--cctc-input-border-width: 2px;"
            Value="@TextValue2"
            ValueChanged="@TextChanged2"
            Placeholder="(000)000/000/00"
            Mask="(000)000/000/00">
        </CCTC_Components.Components.TextBox.Text>),
        ("With alternative mask", "",
@"<Text
    Id=""usage5""
    Value=""@TextValue3""
    ValueChanged=""@TextChanged3""
    ThrottleMs=""@Constants.DefaultThrottleMs""
    Placeholder="">L<??-00-0""
    Mask="">L<??-00-0"">
</Text>

@code {

    string? TextValue3 { get; set; } = string.Empty;

    void TextChanged3(string? newValue)
    {
        TextValue3 = newValue;
        Console.WriteLine($""New value: { newValue ?? ""value cleared"" }"");
    }
}",
        @<CCTC_Components.Components.TextBox.Text
            Id="usage5"
            Value="@TextValue3"
            ValueChanged="@TextChanged3"
            ThrottleMs="@Constants.DefaultThrottleMs"
            Placeholder=">L<??-00-0"
            Mask=">L<??-00-0">
        </CCTC_Components.Components.TextBox.Text>),
        ("With mask containing escape characters", "",
@"<Text
    Id=""usage6""
    Value=""@TextValue4""
    ValueChanged=""@TextChanged4""
    ThrottleMs=""@Constants.DefaultThrottleMs""
    Placeholder=""NCT\000\000""
    Mask="">N\CT\\000\\000"">
</Text>

@code {

    string? TextValue4 { get; set; } = string.Empty;

    void TextChanged4(string? newValue)
    {
        TextValue4 = newValue;
        Console.WriteLine($""New value: { newValue ?? ""value cleared"" }"");
    }
}",
    @<CCTC_Components.Components.TextBox.Text
        Id="usage6"
        Value="@TextValue4"
        ValueChanged="@TextChanged4"
        ThrottleMs="@Constants.DefaultThrottleMs"
        Placeholder="NCT\000\000"
        Mask=">N\CT\\000\\000">
    </CCTC_Components.Components.TextBox.Text>),
        ("Disabled", "",
@"<Text
    Id=""usage7""
    Value=""@TextValue5""
    Disabled=""true"">
</Text>

@code {

    string? TextValue5 { get; set; } = ""Pellentesque sit amet tincidunt massa. Vivamus eget ex ullamcorper, pellentesque elit at, maximus erat. Etiam vel condimentum lectus."";
}",
        @<CCTC_Components.Components.TextBox.Text
            Id="usage7"
            Value="@TextValue5"
            Disabled="true">
        </CCTC_Components.Components.TextBox.Text>),
        ("Read-only", "",
@"<Text
    Id=""usage8""
    Value=""@TextValue""
    ReadOnly=""true"">
</Text>

@code {

    string? TextValue { get; set; } = ""Some demo Text"";
}",
        @<CCTC_Components.Components.TextBox.Text
            Id="usage8"
            Value="@TextValue"
            ReadOnly="true">
        </CCTC_Components.Components.TextBox.Text>),
        ("Disabled and read-only", "",
@"<Text
    Id=""usage9""
    Value=""@TextValue5""
    ReadOnly=""true""
    Disabled=""true"">
</Text>

@code {

    string? TextValue5 { get; set; } = ""Pellentesque sit amet tincidunt massa. Vivamus eget ex ullamcorper, pellentesque elit at, maximus erat. Etiam vel condimentum lectus."";
}",
        @<CCTC_Components.Components.TextBox.Text
            Id="usage9"
            Value="@TextValue5"
            ReadOnly="true"
            Disabled="true">
        </CCTC_Components.Components.TextBox.Text>),
        ("Disabled and read-only (hide read-only icon)", "",
@"<Text
    Id=""usage10""
    Value=""@TextValue5""
    ReadOnly=""true""
    Disabled=""true""
    HideReadOnlyIcon=""true"">
</Text>

@code {

    string? TextValue5 { get; set; } = ""Pellentesque sit amet tincidunt massa. Vivamus eget ex ullamcorper, pellentesque elit at, maximus erat. Etiam vel condimentum lectus."";
}",
        @<CCTC_Components.Components.TextBox.Text
            Id="usage10"
            Value="@TextValue5"
            ReadOnly="true"
            Disabled="true"
            HideReadOnlyIcon="true">
        </CCTC_Components.Components.TextBox.Text>),
        ("Read-only long", "",
@"<Text
    Id=""usage11""
    Value=""Some long readonly text that helps check that the lock icon does not overlap the text""
    ReadOnly=""true"">
</Text>

@code {

}",
        @<CCTC_Components.Components.TextBox.Text
            Id="usage11"
            Value="Some long readonly text that helps check that the lock icon does not overlap the text"
            ReadOnly="true">
        </CCTC_Components.Components.TextBox.Text>),
        ("Read-only long (hide read-only icon)", "",
@"<Text
    Id=""usage12""
    Value=""@TextValue5""
    ReadOnly=""true""
    HideReadOnlyIcon=""true"">
</Text>

@code {

    string? TextValue5 { get; set; } = ""Pellentesque sit amet tincidunt massa. Vivamus eget ex ullamcorper, pellentesque elit at, maximus erat. Etiam vel condimentum lectus."";
}",
        @<CCTC_Components.Components.TextBox.Text
            Id="usage12"
            Value="@TextValue5"
            ReadOnly="true"
            HideReadOnlyIcon="true">
        </CCTC_Components.Components.TextBox.Text>),
        ("With ValueChanged, Focus, Blur, BeforeCopy callbacks plus additional input attributes", "",
@"<Text
    Id=""usage13""
    Value=""@TextValue""
    ValueChanged=""@TextChanged""
    BindEvent=""BindEvent.OnInput""
    Focus=""OnFocus""
    Blur=""OnBlur""
    InputAttributes=""InputAttributes"">
</Text>

@code {

    string? TextValue { get; set; } = ""Some demo Text"";

    void TextChanged(string? newValue)
    {
        TextValue = newValue;
        Console.WriteLine($""New value: { newValue ?? ""value cleared"" }"");
    }

    Dictionary<string, object> InputAttributes => new()
    {
        { ""minlength"", 5 },
        { ""name"", ""test-name"" },
        { ""onbeforecopy"", EventCallback.Factory.Create<EventArgs>(this, OnBeforeCopy) }
    };

    void OnFocus(FocusEventArgs args)
    {
        Console.WriteLine(args.Type);
    }

    void OnBlur(FocusEventArgs args)
    {
        Console.WriteLine(args.Type);
    }

    void OnBeforeCopy(EventArgs args)
    {
        Console.WriteLine(""before copy"");
    }
}",
        @<CCTC_Components.Components.TextBox.Text
            Id="usage13"
            Value="@TextValue"
            ValueChanged="@TextChanged"
            BindEvent="BindEvent.OnInput"
            Focus="OnFocus"
            Blur="OnBlur"
            InputAttributes="InputAttributes">
        </CCTC_Components.Components.TextBox.Text>),
        ("Reactive with callback, null initial value and prevent whitespace", "",
@"<CCTC_Components.Components.TextBox.Text
    Id=""usage14""
    @bind-Value=""TextValue6""
    BindEvent=""BindEvent.OnInput""
    TextChanged=""@(args => Console.WriteLine(args.Value ?? ""value cleared""))""
    ThrottleMs=""@Constants.DefaultThrottleMs""
    PreventWhitespace=""true"">
</CCTC_Components.Components.TextBox.Text>

@code {

    string? TextValue6 { get; set; } = null;
}
",
        @<CCTC_Components.Components.TextBox.Text
            Id="usage14"
            @bind-Value="TextValue6"
            BindEvent="BindEvent.OnInput"
            TextChanged="@(args => Console.WriteLine(args.Value ?? "value cleared"))"
            ThrottleMs="@Constants.DefaultThrottleMs"
            PreventWhitespace="true">
        </CCTC_Components.Components.TextBox.Text>)
    };

    var configuration = new List<(string, string)>
    {
        ("Masked input rules",
@"<pre>
    0 User must enter a digit (0 to 9)
    9 User can enter a digit (0 to 9)
    L User must enter a letter
    ? User can enter a letter
    A User must enter a letter or a digit
    a User can enter a letter or a digit
    & User must enter either a character or a space
    C User can enter characters or spaces
    > Coverts all characters that follow to uppercase
    < Converts all characters that follow to lowercase
    \ Escape character - use \X to make any token X literal (e.g. \C, \0, \\)
</pre>"),
    ("Masked input further information", "refer to <code>Lib.Common.Helpers.StringMask</code>")
    };
}

@* The wrapper div is necessary here for ::deep isolated css rules to be applied to the Text and TextArea components *@
<div>
    <Sampler
        ComponentName="Text"
        QualifiedComponentName="TextBox.Text"
        ComponentCssName="input"
        ComponentTypeName="text"
        Description="@description"
        Features="@features"
        UsageText="Typical usages of the <code>Text</code> component are shown below"
        UsageCodeList="@usageCode"
        Configuration="@configuration"
        Gotchas="@gotchas"
        Tips="@tips"
        ContentHeightPixels="450">
        <ExampleTemplate>
            <Text
                Id="example1"
                @bind-Value="@TextValue"
                TextChanged="@(args =>  Console.WriteLine(args.Value ?? "value cleared"))"
                BindEvent="BindEvent.OnInput"
                Interaction="@(args => InvokeAsync(() => Console.WriteLine(args)))">
            </Text>
        </ExampleTemplate>
    </Sampler>
</div>

@code {

    string? TextValue { get; set; } = "Some demo Text";

    string? TextValue2 { get; set; } = string.Empty;

    string? TextValue3 { get; set; } = string.Empty;

    string? TextValue4 { get; set; } = string.Empty;

    string? TextValue5 { get; set; } = "Pellentesque sit amet tincidunt massa. Vivamus eget ex ullamcorper, pellentesque elit at, maximus erat. Etiam vel condimentum lectus.";

    string? TextValue6 { get; set; } = null;

    string TextValue7 { get; set; } = "Demo Text";

    void TextChanged(string? newValue)
    {
        TextValue = newValue;
        Console.WriteLine($"New value: { newValue ?? "value cleared" }");
    }

    void TextChanged2(string? newValue)
    {
        TextValue2 = newValue;
        Console.WriteLine($"New value: { newValue ?? "value cleared" }");
    }

    void TextChanged3(string? newValue)
    {
        TextValue3 = newValue;
        Console.WriteLine($"New value: { newValue ?? "value cleared" }");
    }

    void TextChanged4(string? newValue)
    {
        TextValue4 = newValue;
        Console.WriteLine($"New value: {newValue ?? "value cleared"}");
    }

    Dictionary<string, object> InputAttributes => new()
    {
        { "minlength", 5 },
        { "name", "test-name" },
        { "onbeforecopy", EventCallback.Factory.Create<EventArgs>(this, OnBeforeCopy) }
    };

    void OnFocus(FocusEventArgs args)
    {
        Console.WriteLine(args.Type);
    }

    void OnBlur(FocusEventArgs args)
    {
        Console.WriteLine(args.Type);
    }

    void OnBeforeCopy(EventArgs args)
    {
        Console.WriteLine("before copy");
    }
}