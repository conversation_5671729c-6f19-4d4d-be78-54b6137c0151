import { setWorldConstructor, World, IWorldOptions } from '@cucumber/cucumber';
import * as messages from '@cucumber/messages';
import { BrowserContext, Page, PlaywrightTestOptions, APIRequestContext } from '@playwright/test';

export interface CucumberWorldConstructorParams {
  parameters: Record<string, string>;
}

export interface ICustomWorld extends World {
  debug: boolean;
  feature?: messages.Pickle;
  context?: BrowserContext;
  page?: Page;
  testName?: string;
  startTime?: Date;
  server?: APIRequestContext;
  playwrightOptions?: PlaywrightTestOptions;

  // Range selector test properties
  initialLowerValue?: number;
  initialUpperValue?: number;
  initialPosition?: string;
  initialPricePosition?: string;
  initialSelectorPositions?: string[];
  initialSelectorValues?: string[];

  // custom items can be added here and then used for ICustomWorld
  // but also consider adding them to config in config.ts
  // defaultUrl: string;
}

export class CustomWorld extends World implements ICustomWorld {
  constructor(options: IWorldOptions) {
    super(options);
  }

  debug = false;
  // defaultUrl = 'https://gray-tree-00d961c03.5.azurestaticapps.net';
}

setWorldConstructor(CustomWorld);
