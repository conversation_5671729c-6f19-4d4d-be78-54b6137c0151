@component @rangeselector @rangeselector_3
Feature: the range selector functions correctly with blue section and value constraints
    Scenario: the range selector displays blue section between thumbs
        Given the user is at the home page
        And the user selects the "Range selector" component in the container "Input"
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "Integer Range"
        Then the range selector fill lies between the two thumbs

    Scenario: the range selector prevents invalid ranges
        Given the user is at the home page
        And the user selects the "Range selector" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Integer Range"
        And the range selector displays "integer" values from "0" to "100"
        And the "upper" value is 75
        When the user drags the lower thumb to position 90%
        Then the lower value does not exceed the upper value