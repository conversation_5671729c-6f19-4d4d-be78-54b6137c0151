@using Microsoft.AspNetCore.Components.Web
@using Microsoft.JSInterop
@using CCTC_Components.Components.RangeSelector
@using CCTC_Lib.Extensions
@inherits CCTC_Components.Components.__CCTC.CCTCBase
@implements IAsyncDisposable
@inject IJSRuntime JSRuntime
@typeparam TValue where TValue : IComparable<TValue>

<cctc-rangeselector id="@Id" class="@CssClass" style="@Style" data-author="cctc">
    <div class="@_componentWrapperClass">
        <div class="cctc-rangeselector-track" @ref="_trackElement">
            <div class="cctc-rangeselector-fill" style="@GetFillStyle()"></div>
            <div class="cctc-rangeselector-thumb cctc-rangeselector-thumb-lower"
                 @ref="_lowerThumbEl"
                 style="@GetThumbStyle(true);" 
                 tabindex="@GetTabIndex(true)"
                 aria-label="Lower range handle"
                 aria-disabled="@Disabled.ToString().ToLower()"
                 role="slider"
                 aria-valuemin="@GetDisplayValue(MinValue)"
                 aria-valuemax="@GetDisplayValue(MaxValue)"
                 aria-valuenow="@GetDisplayValue(_currentLower)"
                 @onmousedown="@(e => OnMouseDownAsync(e, true))"
                 @ontouchstart="@(e => OnTouchStartAsync(e, true))"
                 @onfocus="@(e => OnFocusAsync(true))"
                 @onblur="@(e => OnBlurAsync(true))"
                 @onkeydown="@(args => CCTCRangeSelectorKeyDown(args, true))"
                 @onkeydown:stopPropagation="true"
                 @onkeydown:preventDefault="true"></div>
            <div class="cctc-rangeselector-thumb cctc-rangeselector-thumb-upper"
                 @ref="_upperThumbEl"
                 style="@GetThumbStyle(false);" 
                 tabindex="@GetTabIndex(false)"
                 aria-label="Upper range handle"
                 aria-disabled="@Disabled.ToString().ToLower()"
                 role="slider"
                 aria-valuemin="@GetDisplayValue(MinValue)"
                 aria-valuemax="@GetDisplayValue(MaxValue)"
                 aria-valuenow="@GetDisplayValue(_currentUpper)"
                 @onmousedown="@(e => OnMouseDownAsync(e, false))"
                 @ontouchstart="@(e => OnTouchStartAsync(e, false))"
                 @onfocus="@(e => OnFocusAsync(false))"
                 @onblur="@(e => OnBlurAsync(false))"
                 @onkeydown="@(args => CCTCRangeSelectorKeyDown(args, false))"
                 @onkeydown:stopPropagation="true"
                 @onkeydown:preventDefault="true"></div>
        </div>
        @if (ShowLabels)
        {
            <div class="cctc-rangeselector-labels">
                @if (Orientation == RangeSelectorOrientation.Vertical)
                {
                    <span>@GetDisplayValue(MaxValue)</span>
                    <span>@GetDisplayValue(MinValue)</span>
                }
                else
                {
                    <span>@GetDisplayValue(MinValue)</span>
                    <span>@GetDisplayValue(MaxValue)</span>
                }
            </div>
        }
        @ChildContent
    </div>
</cctc-rangeselector>

@code {
    
    /// <summary>
    /// The minimum value of the range
    /// </summary>
    [Parameter, EditorRequired]
    public required TValue MinValue { get; set; }

    /// <summary>
    /// The maximum value of the range
    /// </summary>
    [Parameter, EditorRequired]
    public required TValue MaxValue { get; set; }

    /// <summary>
    /// The current lower bound of the selected range
    /// </summary>
    [Parameter]
    public TValue? LowerValue { get; set; }

    /// <summary>
    /// The current upper bound of the selected range
    /// </summary>
    [Parameter]
    public TValue? UpperValue { get; set; }

    /// <summary>
    /// Callback for when the lower value changes
    /// </summary>
    [Parameter]
    public EventCallback<TValue> LowerValueChanged { get; set; }

    /// <summary>
    /// Callback for when the upper value changes
    /// </summary>
    [Parameter]
    public EventCallback<TValue> UpperValueChanged { get; set; }

    /// <summary>
    /// Optional content to display within the range selector
    /// </summary>
    [Parameter]
    public RenderFragment? ChildContent { get; set; }

    /// <summary>
    /// Function to convert a percentage to a value for custom types
    /// </summary>
    [Parameter]
    public Func<TValue, TValue, double, TValue>? ValueFromPercentage { get; set; }

    /// <summary>
    /// Function to convert a value to a percentage for custom types
    /// </summary>
    [Parameter]
    public Func<TValue, TValue, TValue, double>? PercentageFromValue { get; set; }

    /// <summary>
    /// Function to determine step size for keyboard navigation (as percentage)
    /// </summary>
    [Parameter]
    public Func<TValue, TValue, double>? StepFunction { get; set; }

    /// <summary>
    /// Function to format values for display
    /// </summary>
    [Parameter]
    public Func<TValue, string>? DisplayFormatter { get; set; }

    /// <summary>
    /// Whether the range selector is disabled
    /// </summary>
    [Parameter]
    public bool Disabled { get; set; }

    /// <summary>
    /// Whether to show the min/max labels
    /// </summary>
    [Parameter]
    public bool ShowLabels { get; set; } = true;

    /// <summary>
    /// The orientation of the range selector (horizontal or vertical)
    /// </summary>
    [Parameter]
    public RangeSelectorOrientation Orientation { get; set; } = RangeSelectorOrientation.Horizontal;

    /// <summary>
    /// The width of the range selector (auto, small, medium, large, or full)
    /// </summary>
    [Parameter]
    public RangeSelectorWidth Width { get; set; } = RangeSelectorWidth.Auto;

    private ElementReference _trackElement;
    private ElementReference _lowerThumbEl;
    private ElementReference _upperThumbEl;
    private IJSObjectReference? _jsModule;
    private DotNetObjectReference<RangeSelector<TValue>>? _dotNetRef;
    
    private TValue _currentLower = default!;
    private TValue _currentUpper = default!;
    private bool _dragIsLowerThumb;
    private string? _componentWrapperClass;

    private double _lowerPercentage => GetPercentageFromValue(_currentLower);
    private double _upperPercentage => GetPercentageFromValue(_currentUpper);

    protected override void OnInitialized()
    {
        base.OnInitialized();
        
        
        // Validate that we have the required conversion functions for non-numeric types
        if (typeof(TValue) != typeof(int) && typeof(TValue) != typeof(double) && 
            typeof(TValue) != typeof(float) && typeof(TValue) != typeof(decimal))
        {
            if (ValueFromPercentage == null || PercentageFromValue == null)
            {
                throw new InvalidOperationException($"ValueFromPercentage and PercentageFromValue parameters are required for type {typeof(TValue).Name}");
            }
        }
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            try
            {
                _dotNetRef = DotNetObjectReference.Create(this);
                _jsModule = await SafeInvokeJsAsync(() => JSRuntime.InvokeAsync<IJSObjectReference>("import", "./_content/CCTC_Components/Components/RangeSelector/RangeSelector.razor.js"));
                
                if (_jsModule != null)
                {
                    await SafeInvokeJsAsync(() => _jsModule.InvokeVoidAsync("initialize", _trackElement, _dotNetRef, Id));
                }
            }
            catch (Exception)
            {
                // JavaScript initialization failed silently
            }
        }
    }

    private async Task OnFocusAsync(bool isLowerThumb)
    {
        // Ensure the thumb element can receive keyboard events
        // Store the focused thumb state for proper keyboard handling
        _dragIsLowerThumb = isLowerThumb;
        await Task.CompletedTask;
    }

    private async Task OnBlurAsync(bool isLowerThumb)
    {
        await Task.CompletedTask;
    }

    async Task CCTCRangeSelectorKeyDown(KeyboardEventArgs args, bool isLowerThumb)
    {
        if (Disabled) return;

        var stepSize = GetStepSize();
        var currentPercentage = isLowerThumb ? _lowerPercentage : _upperPercentage;

        // For vertical orientation, arrow keys should behave intuitively:
        // Up arrow = increase value, Down arrow = decrease value
        // For horizontal: Left arrow = decrease, Right arrow = increase
        bool shouldDecrease = false;
        bool shouldIncrease = false;

        switch (args.Key)
        {
            case "ArrowLeft":
            case "Left":
                shouldDecrease = Orientation == RangeSelectorOrientation.Horizontal;
                break;
            case "ArrowRight":
            case "Right":
                shouldIncrease = Orientation == RangeSelectorOrientation.Horizontal;
                break;
            case "ArrowDown":
            case "Down":
                shouldDecrease = Orientation == RangeSelectorOrientation.Vertical;
                break;
            case "ArrowUp":
            case "Up":
                shouldIncrease = Orientation == RangeSelectorOrientation.Vertical;
                break;
            case "Home":
                await UpdateValueAsync(0, isLowerThumb);
                return;
            case "End":
                await UpdateValueAsync(100, isLowerThumb);
                return;
            case "PageDown":
                var newPercentagePageDown = Math.Max(0, currentPercentage - (stepSize * 10));
                await UpdateValueAsync(newPercentagePageDown, isLowerThumb);
                return;
            case "PageUp":
                var newPercentagePageUp = Math.Min(100, currentPercentage + (stepSize * 10));
                await UpdateValueAsync(newPercentagePageUp, isLowerThumb);
                return;
        }

        if (shouldDecrease)
        {
            var newPercentage = Math.Max(0, currentPercentage - stepSize);
            await UpdateValueAsync(newPercentage, isLowerThumb);
        }
        else if (shouldIncrease)
        {
            var newPercentage = Math.Min(100, currentPercentage + stepSize);
            await UpdateValueAsync(newPercentage, isLowerThumb);
        }
    }


    
    private async Task OnMouseDownAsync(MouseEventArgs e, bool isLowerThumb)
    {
        if (Disabled) return;

        _dragIsLowerThumb = isLowerThumb;
        
        if (_jsModule != null)
        {
            await SafeInvokeJsAsync(() => _jsModule.InvokeVoidAsync("startDrag", isLowerThumb));
        }

        // Ensure the interacted thumb receives focus so keyboard arrows work
        try
        {
            // Add a small delay to ensure DOM is ready for focus
            await Task.Delay(10);
            
            if (isLowerThumb)
            {
                await _lowerThumbEl.FocusAsync();
            }
            else
            {
                await _upperThumbEl.FocusAsync();
            }
            
            // Force update to ensure focus state is properly maintained
            StateHasChanged();
        }
        catch (Exception)
        {
            // Focus call failed silently - element may not be ready
        }
    }

    
    private async Task OnTouchStartAsync(TouchEventArgs e, bool isLowerThumb)
    {
        if (Disabled) return;

        _dragIsLowerThumb = isLowerThumb;
        
        if (_jsModule != null)
        {
            await SafeInvokeJsAsync(() => _jsModule.InvokeVoidAsync("startDrag", isLowerThumb));
        }

        // Ensure the interacted thumb receives focus so keyboard arrows work (for devices with keyboards)
        try
        {
            // Add a small delay to ensure DOM is ready for focus
            await Task.Delay(10);
            
            if (isLowerThumb)
            {
                await _lowerThumbEl.FocusAsync();
            }
            else
            {
                await _upperThumbEl.FocusAsync();
            }
            
            // Force update to ensure focus state is properly maintained
            StateHasChanged();
        }
        catch (Exception)
        {
            // Focus call failed silently - element may not be ready
        }
    }

    [JSInvokable]
    public async Task UpdateValueAsync(double percentage, bool isLowerThumb)
    {
        var newValue = GetValueFromPercentage(percentage);
        
        if (isLowerThumb)
        {
            if (newValue.CompareTo(_currentUpper) <= 0 && newValue.CompareTo(MinValue) >= 0)
            {
                _currentLower = newValue;
                await LowerValueChanged.InvokeAsync(_currentLower);
            }
        }
        else
        {
            if (newValue.CompareTo(_currentLower) >= 0 && newValue.CompareTo(MaxValue) <= 0)
            {
                _currentUpper = newValue;
                await UpperValueChanged.InvokeAsync(_currentUpper);
            }
        }
        
        await InvokeAsync(StateHasChanged);
    }

    [JSInvokable]
    public async Task StopDragAsync()
    {
        await InvokeAsync(StateHasChanged);
    }

    private TValue GetValueFromPercentage(double percentage)
    {
        if (ValueFromPercentage != null)
        {
            return ValueFromPercentage(MinValue, MaxValue, percentage);
        }

        // Default implementation for numeric types
        if (typeof(TValue) == typeof(int))
        {
            var lower = Convert.ToDouble(MinValue);
            var upper = Convert.ToDouble(MaxValue);
            var value = lower + (percentage / 100.0) * (upper - lower);
            return (TValue)(object)(int)Math.Round(value);
        }
        
        if (typeof(TValue) == typeof(double))
        {
            var lower = Convert.ToDouble(MinValue);
            var upper = Convert.ToDouble(MaxValue);
            var value = lower + (percentage / 100.0) * (upper - lower);
            return (TValue)(object)value;
        }
        
        if (typeof(TValue) == typeof(float))
        {
            var lower = Convert.ToDouble(MinValue);
            var upper = Convert.ToDouble(MaxValue);
            var value = lower + (percentage / 100.0) * (upper - lower);
            return (TValue)(object)(float)value;
        }
        
        if (typeof(TValue) == typeof(decimal))
        {
            var lower = Convert.ToDecimal(MinValue);
            var upper = Convert.ToDecimal(MaxValue);
            var value = lower + (decimal)(percentage / 100.0) * (upper - lower);
            return (TValue)(object)value;
        }

        throw new NotSupportedException($"Type {typeof(TValue).Name} is not supported without custom conversion functions.");
    }

    private double GetPercentageFromValue(TValue value)
    {
        if (PercentageFromValue != null)
        {
            return PercentageFromValue(MinValue, MaxValue, value);
        }

        // Default implementation for numeric types
        if (typeof(TValue) == typeof(int) || typeof(TValue) == typeof(double) || 
            typeof(TValue) == typeof(float) || typeof(TValue) == typeof(decimal))
        {
            var lower = Convert.ToDouble(MinValue);
            var upper = Convert.ToDouble(MaxValue);
            var current = Convert.ToDouble(value);
            
            if (upper == lower) return 0;
            return ((current - lower) / (upper - lower)) * 100.0;
        }

        throw new NotSupportedException($"Type {typeof(TValue).Name} is not supported without custom conversion functions.");
    }

    private double GetStepSize()
    {
        if (StepFunction != null)
        {
            return StepFunction(MinValue, MaxValue);
        }

        // Default step size for keyboard navigation - 1% for smooth control
        return 1.0;
    }

    private string GetDisplayValue(TValue value)
    {
        if (DisplayFormatter != null)
        {
            return DisplayFormatter(value);
        }

        return value?.ToString() ?? string.Empty;
    }
    
    private string GetTabIndex(bool isLowerThumb)
    {
        if (Disabled) return "-1";
        
        // Generate unique tabindex based on component ID hash to avoid conflicts
        // This ensures multiple range selectors can coexist without tabindex conflicts
        var baseTabIndex = Math.Abs(Id?.GetHashCode() ?? 0) % 1000;
        var thumbOffset = isLowerThumb ? 0 : 1;
        return (baseTabIndex + thumbOffset).ToString();
    }

    private string GetFillStyle()
    {
        if (Orientation == RangeSelectorOrientation.Vertical)
        {
            return $"bottom: {_lowerPercentage}%; height: {_upperPercentage - _lowerPercentage}%;";
        }
        else
        {
            return $"left: {_lowerPercentage}%; width: {_upperPercentage - _lowerPercentage}%;";
        }
    }

    private string GetThumbStyle(bool isLower)
    {
        var percentage = isLower ? _lowerPercentage : _upperPercentage;
        
        if (Orientation == RangeSelectorOrientation.Vertical)
        {
            return $"bottom: {percentage}%;";
        }
        else
        {
            return $"left: {percentage}%;";
        }
    }

    protected override void OnParametersSet()
    {
        base.OnParametersSet();
        
        _currentLower = LowerValue ?? MinValue;
        _currentUpper = UpperValue ?? MaxValue;
        
        _componentWrapperClass = 
            new CssBuilder("component-wrapper")
                .AddClass("disabled", Disabled)
                .AddClass("vertical", Orientation == RangeSelectorOrientation.Vertical)
                .AddClass("horizontal", Orientation == RangeSelectorOrientation.Horizontal)
                .AddClass("width-small", Width == RangeSelectorWidth.Small)
                .AddClass("width-medium", Width == RangeSelectorWidth.Medium)
                .AddClass("width-large", Width == RangeSelectorWidth.Large)
                .AddClass("width-full", Width == RangeSelectorWidth.Full)
                .Build();
        
    }

    public override async ValueTask DisposeAsync()
    {
        _dotNetRef?.Dispose();
        if (_jsModule != null)
        {
            try
            {
                await _jsModule.InvokeVoidAsync("dispose", Id);
                await _jsModule.DisposeAsync();
            }
            catch (Exception)
            {
                // Silently handle any disposal errors
            }
        }
        await base.DisposeAsync();
    }
}