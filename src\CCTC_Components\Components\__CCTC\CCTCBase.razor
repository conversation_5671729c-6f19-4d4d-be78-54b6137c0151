﻿@using Microsoft.JSInterop
@using CCTC_Lib.Models.UI
@using CCTC_Components.Components.__CCTC.Models
@using Microsoft.Extensions.Logging
@inject ILogger<CCTCBase> Logger
@inject IJSRuntime JSRuntime
@implements IAsyncDisposable

@code {

    /// <summary>
    /// The global font size assumed for the components
    /// </summary>
    /// <remarks>This is set in the cctc-components.css with $font-size-base: .875rem;
    /// If you change the value here, you MUST change the $font-size-base too</remarks>
    public int FontSizePixels => 14;

    /// <summary>
    /// The id must be unique. This is the key identifier of the component and will be applied to the outer wrapper.
    /// </summary>
    [Parameter, EditorRequired]
    public virtual required string Id { get; set; }

    /// <summary>
    /// The css applied to the component. This is applied to the outer wrapper.
    /// </summary>
    [Parameter]
    public string? CssClass { get; set; }

    /// <summary>
    /// The style applied to the component. This is applied to the outer wrapper.
    /// </summary>
    [Parameter]
    public string? Style { get; set; }

    /// <summary>
    /// Can be used by an element to notify a parent that it has rendered.
    /// This is usually invoked in the OnAfterRender or OnAfterRenderAsync method
    /// </summary>
    [Parameter]
    public Action? HasRendered { get; set; }

    /// <summary>
    /// Can be used by an element to notify a parent that it has rendered.
    /// This is usually invoked in the OnAfterRender or OnAfterRenderAsync method.
    /// Use this if the function should invoke an async method
    /// </summary>
    [Parameter]
    public Func<Task>? HasRenderedAsync { get; set; }

    /// <summary>
    /// Sets the tab index of the main component
    /// </summary>
    [Parameter]
    public int TabIndex { get; set; } = -1;

    /// <summary>
    /// A callback which fires on focus
    /// </summary>
    [Parameter]
    public EventCallback<FocusEventArgs> Focus { get; set; }

    /// <summary>
    /// A callback which fires on blur
    /// </summary>
    [Parameter]
    public EventCallback<FocusEventArgs> Blur { get; set; }

    /// <summary>
    /// A callback which fires on focusin
    /// </summary>
    /// <remarks>focusin events bubble</remarks>
    [Parameter]
    public EventCallback<FocusEventArgs> FocusIn { get; set; }

    /// <summary>
    /// A callback which fires on focusout
    /// </summary>
    /// <remarks>focusout events bubble</remarks>
    [Parameter]
    public EventCallback<FocusEventArgs> FocusOut { get; set; }

    bool _checksRun;

    void DoChecks()
    {
        if (_checksRun)
            return;

        //if a focus callback(s) are given, they will never fire when tabindex is -1
        if (TabIndex == -1
            && (Focus.HasDelegate
                || Blur.HasDelegate
                || FocusIn.HasDelegate || FocusOut.HasDelegate))
        {
            Logger.LogWarning("Focus events will not fire whilst tabindex = -1");
        }

        _checksRun = true;
    }

    /// <inheritdoc />
    protected override void OnInitialized()
    {
        DoChecks();

        base.OnInitialized();
    }

    /// <inheritdoc />
    protected override Task OnInitializedAsync()
    {
        DoChecks();

        return base.OnInitializedAsync();
    }


    IJSObjectReference? _jsUtils;

    ValueTask<IJSObjectReference> SetUtils()
    {
        return JSRuntime.InvokeAsync<IJSObjectReference>("import", "./_content/CCTC_Components/scripts/utils.js");
    }

    /// <summary>
    /// Checks for the existence of an element using the given selector
    /// </summary>
    /// <param name="selector">The selector to use</param>
    /// <returns>True if an element is found</returns>
    /// <remarks>Returns the first selector matching the query</remarks>
    protected async Task<bool> ElementExists(string selector)
    {
        _jsUtils ??= await SetUtils();
        return await _jsUtils.InvokeAsync<bool>("elementExists", selector);
    }

    /// <summary>
    /// Scrolls the element from the given selector into view
    /// </summary>
    /// <param name="selector">The css selector to use</param>
    /// <param name="args">Args used by javascript to change the scrolling behaviour</param>
    protected async Task ScrollIntoViewFromSelector(string selector, object? args)
    {
        _jsUtils ??= _jsUtils ??= await SetUtils();
        await _jsUtils.InvokeVoidAsync("scrollIntoViewFromQuery", selector, args);
    }

    /// <summary>
    /// Scrolls the element from the given selector into view
    /// </summary>
    /// <param name="selector">The css selector to use</param>
    /// <remarks>Uses the default scrolling arguments</remarks>
    protected async Task ScrollIntoViewFromSelector(string selector)
    {
        await ScrollIntoViewFromSelector(selector, null);
    }

    /// <summary>
    /// Returns the <see cref="BoundingRect"/> for an element
    /// </summary>
    /// <param name="id">The id of the element to get the bounding rect for</param>
    /// <returns>A <see cref="BoundingRect"/></returns>
    protected async Task<BoundingRect> GetElementBoundRectFromId(string id)
    {
        _jsUtils ??= _jsUtils ??= await SetUtils();
        return await _jsUtils.InvokeAsync<BoundingRect>("getElementBoundRect", id);
    }

    /// <summary>
    /// Returns the <see cref="BoundingRect"/> for an element
    /// </summary>
    /// <param name="selector">The selector for the element to get the bounding rect for</param>
    /// <returns>A <see cref="BoundingRect"/></returns>
    protected async Task<BoundingRect?> GetElementBoundRectFromQuery(string selector)
    {
        _jsUtils ??= await SetUtils();
        return await _jsUtils.InvokeAsync<BoundingRect>("getElementBoundRectFromQuery", selector);
    }

    /// <summary>
    /// Returns the <see cref="CursorSelection"/> for an element
    /// </summary>
    /// <param name="selector">The selector for the element to get the cursor selection for</param>
    /// <returns>A <see cref="CursorSelection"/></returns>
    protected async Task<CursorSelection> GetCursorSelection(string selector)
    {
        _jsUtils ??= await SetUtils();
        return await _jsUtils.InvokeAsync<CursorSelection>("getCursorSelection", selector);
    }

    /// <summary>
    /// Sets the cursor to the start of the element found by the selector
    /// </summary>
    /// <param name="selector">The selector for the element to set the cursor on</param>
    /// <remarks>Fails silently if an element is not found</remarks>
    protected async Task SetCursorToStart(string selector)
    {
        _jsUtils ??= await SetUtils();
        await _jsUtils.InvokeVoidAsync("cursorToStart", selector);
    }

    /// <summary>
    /// Sets the cursor to the end of the element found by the selector
    /// </summary>
    /// <param name="selector">The selector for the element to set the cursor on</param>
    /// <remarks>Fails silently if an element is not found</remarks>
    protected async Task SetCursorToEnd(string selector)
    {
        _jsUtils ??= await SetUtils();
        await _jsUtils.InvokeVoidAsync("cursorToEnd", selector);
    }

    /// <summary>
    /// Returns true if the referenced element is the active element
    /// </summary>
    /// <param name="reference">The element to check</param>
    /// <returns>True if the element is the active element</returns>
    protected async Task<bool> HasElementGotFocus(ElementReference reference)
    {
        _jsUtils ??= await SetUtils();
        return await _jsUtils.InvokeAsync<bool>("hasElementGotFocus", reference);
    }

    /// <summary>
    /// Returns true if the selected element is the active element
    /// </summary>
    /// <param name="selector">The selector for the element check</param>
    /// <returns>True if the element found by the selector is the active element</returns>
    /// <remarks>Returns false if the selector doesn't find an element</remarks>
    protected async Task<bool> HasSelectedElementGotFocus(string selector)
    {
        _jsUtils ??= await SetUtils();
        return await _jsUtils.InvokeAsync<bool>("hasSelectedElementGotFocus", selector);
    }

    /// <summary>
    /// Returns true if any of the elements found by the selector is the active element
    /// </summary>
    /// <param name="selector">The selector for the elements check</param>
    /// <returns>True if an element found by the selector is the active element</returns>
    /// <remarks>Returns false if the selector doesn't find any elements</remarks>
    protected async Task<bool> HasAnySelectedElementGotFocus(string selector)
    {
        _jsUtils ??= await SetUtils();
        return await _jsUtils.InvokeAsync<bool>("hasAnySelectedElementGotFocus", selector);
    }

    /// <summary>
    /// Returns the current active element
    /// </summary>
    /// <returns>The document.activeElement</returns>
    protected async Task<string> GetActiveElement()
    {
        _jsUtils ??= await SetUtils();
        return await _jsUtils.InvokeAsync<string>("getActiveElement");
    }


    /// <summary>
    /// Sets the focus on the element given by the selector
    /// </summary>
    /// <param name="selector">The selector to find the element</param>
    protected async Task SetFocusOnSelector(string selector)
    {
        _jsUtils ??= await SetUtils();
        await _jsUtils.InvokeVoidAsync("setFocusOnSelector", selector);
    }

    /// <summary>
    /// Scrolls the targeted element up by the number of given pixels
    /// </summary>
    /// <param name="selector">Selects the element to scroll</param>
    /// <param name="moveBy">Number of pixels to scroll up by</param>
    /// <remarks>If the selector does not find an element the request silently fails</remarks>
    protected async Task ScrollUp(string selector, int moveBy)
    {
        _jsUtils ??= await SetUtils();
        await _jsUtils.InvokeVoidAsync("scrollUp", selector, moveBy);
    }

    /// <summary>
    /// Scrolls the targeted element down by the number of given pixels
    /// </summary>
    /// <param name="selector">Selects the element to scroll</param>
    /// <param name="moveBy">Number of pixels to scroll down by</param>
    /// <remarks>If the selector does not find an element the request silently fails</remarks>
    protected async Task ScrollDown(string selector, int moveBy)
    {
        _jsUtils ??= await SetUtils();
        await _jsUtils.InvokeVoidAsync("scrollDown", selector, moveBy);
    }

    /// <summary>
    /// Gets the scroll position of an element
    /// </summary>
    /// <param name="selector">The selector for the element</param>
    /// <returns>The scroll top position or -1 if the selector did not find an element</returns>
    protected async Task<int> GetScrollTop(string selector)
    {
        _jsUtils ??= await SetUtils();
        return await _jsUtils.InvokeAsync<int>("getScrollTop", selector);
    }

    /// <summary>
    /// A utility to convert a rem size to pixels
    /// </summary>
    /// <param name="rem">The size in rem to convert</param>
    /// <returns>An int of pixels</returns>
    protected int RemToPixels(double rem) => (int)Math.Round(FontSizePixels * rem);
    
    /// <summary>
    /// Returns user selected text
    /// </summary>
    protected async Task<string> GetSelectedText()
    {
        _jsUtils ??= await SetUtils();
        return await _jsUtils.InvokeAsync<string>("getSelectedText");
    }

    /// <summary>
    /// Show a JS alert
    /// </summary>
    /// <param name="alertMessage"></param>
    /// <returns></returns>
    protected async Task ShowAlert(string alertMessage)
    {
        await JSRuntime.InvokeVoidAsync("alert", alertMessage);
    }
    
    /// <summary>
    /// Invokes a js invoke void interop call in a safe way by checking whether the object is disposed before executing
    /// </summary>
    /// <param name="jsCall">The js call to make. This is a Func returning a ValueTask that can be awaited</param>
    /// <remarks>Ensure that all component disposals call the base.DisposeAsync() from this base class
    /// as the first call in the DisposeAsync method</remarks>
    /// <remarks>Calling this method will silently handle ObjectDisposedExceptions and JSDisconnectedExceptions</remarks>
    protected ValueTask SafeInvokeJsAsync(Func<ValueTask> jsCall)
    {
        if (IsDisposed) return ValueTask.CompletedTask;
    
        try
        {
            jsCall();
        }
        catch (ObjectDisposedException)
        {
            // Component disposed - safe to ignore
        }
        catch (JSDisconnectedException)
        {
            // Circuit disconnected - safe to ignore
        }
        
        return ValueTask.CompletedTask;
    }
    
    /// <summary>
    /// Invokes a js invoke interop call in a safe way by checking whether the object is disposed before executing
    /// and returning the content of T
    /// </summary>
    /// <param name="jsCall">The js call to make. This is a Func returning a ValueTask that can be awaited</param>
    /// <typeparam name="T">The return type</typeparam>
    /// <returns>T</returns>
    /// <remarks>Ensure that all component disposals call the base.DisposeAsync() from this base class
    /// as the first call in the DisposeAsync method</remarks>
    /// <remarks>Calling this method will silently handle ObjectDisposedExceptions and JSDisconnectedExceptions</remarks>
    protected ValueTask<T> SafeInvokeJsAsync<T>(Func<ValueTask<T>> jsCall)
    {
        if (IsDisposed) return ValueTask.FromResult<T>(default!);
    
        try
        {
            return jsCall();
        }
        catch (ObjectDisposedException)
        {
            // Component disposed - safe to ignore
        }
        catch (JSDisconnectedException)
        {
            // Circuit disconnected - safe to ignore
        }
        
        return ValueTask.FromResult<T>(default!);
    }
    
    /// <summary>
    /// Flags whether the component has been disposed
    /// </summary>
    /// <remarks>Flags whether the component has been disposed</remarks>
    /// <remarks>Must remain public</remarks>
    public bool IsDisposed { get; set; }

    /// <inheritdoc />
    public virtual async ValueTask DisposeAsync()
    {
        if(IsDisposed) return;
        
        if (_jsUtils != null)
        {
            await _jsUtils.DisposeAsync();
        }
        
        IsDisposed = true;
    }
}