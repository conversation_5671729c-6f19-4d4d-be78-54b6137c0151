using CCTC_Components.Components.RangeSelector;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Web;

namespace CCTC_Components.bUnit.test
{
    public class RangeSelectorTests : TestContext
    {
        [Fact]
        public void RangeSelectorRendersCorrectly()
        {
            var cut = RenderComponent<RangeSelector<int>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.MinValue, 0)
                .Add(p => p.MaxValue, 100)
                .Add(p => p.LowerValue, 20)
                .Add(p => p.UpperValue, 80)
            );

            var componentWrapper = cut.Find(".component-wrapper");
            var track = cut.Find(".cctc-rangeselector-track");
            var fill = cut.Find(".cctc-rangeselector-fill");
            var lowerThumb = cut.Find(".cctc-rangeselector-thumb-lower");
            var upperThumb = cut.Find(".cctc-rangeselector-thumb-upper");

            Assert.NotNull(componentWrapper);
            Assert.NotNull(track);
            Assert.NotNull(fill);
            Assert.NotNull(lowerThumb);
            Assert.NotNull(upperThumb);
        }

        [Fact]
        public void RangeSelectorValueCallbacksInvokedOnUpdate()
        {
            var mockDummyService = new Mock<IDummyService>();
            int lowerValueChangedNewValue = 0;
            int upperValueChangedNewValue = 0;

            var cut = RenderComponent<RangeSelector<int>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.MinValue, 0)
                .Add(p => p.MaxValue, 100)
                .Add(p => p.LowerValue, 20)
                .Add(p => p.UpperValue, 80)
                .Add(p => p.LowerValueChanged, async value => { lowerValueChangedNewValue = value; await mockDummyService.Object.MethodOneAsync(); })
                .Add(p => p.UpperValueChanged, async value => { upperValueChangedNewValue = value; await mockDummyService.Object.MethodTwoAsync(); })
            );

            cut.InvokeAsync(async () => await cut.Instance.UpdateValueAsync(30, true));
            cut.InvokeAsync(async () => await cut.Instance.UpdateValueAsync(70, false));

            Assert.Equal(30, lowerValueChangedNewValue);
            Assert.Equal(70, upperValueChangedNewValue);

            mockDummyService.Verify(m => m.MethodOneAsync(), Times.Once);
            mockDummyService.Verify(m => m.MethodTwoAsync(), Times.Once);
        }

        [Fact]
        public void RangeSelectorWhenDisabledDoesNotAllowInteraction()
        {
            var mockDummyService = new Mock<IDummyService>();
            int lowerValueChangedNewValue = 20;
            int upperValueChangedNewValue = 80;

            var cut = RenderComponent<RangeSelector<int>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.MinValue, 0)
                .Add(p => p.MaxValue, 100)
                .Add(p => p.LowerValue, 20)
                .Add(p => p.UpperValue, 80)
                .Add(p => p.Disabled, true)
                .Add(p => p.LowerValueChanged, async value => { lowerValueChangedNewValue = value; await mockDummyService.Object.MethodOneAsync(); })
                .Add(p => p.UpperValueChanged, async value => { upperValueChangedNewValue = value; await mockDummyService.Object.MethodTwoAsync(); })
            );

            var lowerThumb = cut.Find(".cctc-rangeselector-thumb-lower");
            var upperThumb = cut.Find(".cctc-rangeselector-thumb-upper");

            Assert.Equal("-1", lowerThumb.GetAttribute("tabindex"));
            Assert.Equal("-1", upperThumb.GetAttribute("tabindex"));
            Assert.Equal("true", lowerThumb.GetAttribute("aria-disabled"));
            Assert.Equal("true", upperThumb.GetAttribute("aria-disabled"));
        }

        [Fact]
        public void DisabledClassConfiguredCorrectly()
        {
            var cut = RenderComponent<RangeSelector<int>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.MinValue, 0)
                .Add(p => p.MaxValue, 100)
            );

            var componentWrapper = cut.Find(".component-wrapper");
            string disabledClass = "disabled";

            TestHelpers.AssertDoesNotHaveClass(componentWrapper, disabledClass);

            cut.SetParametersAndRender(parameters => parameters
                .Add(p => p.Disabled, true)
            );

            TestHelpers.AssertHasClass(componentWrapper, disabledClass);
        }

        [Fact]
        public void OrientationClassesConfiguredCorrectly()
        {
            var cut = RenderComponent<RangeSelector<int>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.MinValue, 0)
                .Add(p => p.MaxValue, 100)
            );

            var componentWrapper = cut.Find(".component-wrapper");
            string horizontalClass = "horizontal";
            string verticalClass = "vertical";

            TestHelpers.AssertHasClass(componentWrapper, horizontalClass);
            TestHelpers.AssertDoesNotHaveClass(componentWrapper, verticalClass);

            cut.SetParametersAndRender(parameters => parameters
                .Add(p => p.Orientation, RangeSelectorOrientation.Vertical)
            );

            TestHelpers.AssertDoesNotHaveClass(componentWrapper, horizontalClass);
            TestHelpers.AssertHasClass(componentWrapper, verticalClass);
        }

        [Fact]
        public void WidthClassesConfiguredCorrectly()
        {
            var cut = RenderComponent<RangeSelector<int>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.MinValue, 0)
                .Add(p => p.MaxValue, 100)
            );

            var componentWrapper = cut.Find(".component-wrapper");

            TestHelpers.AssertDoesNotHaveClass(componentWrapper, "width-small");
            TestHelpers.AssertDoesNotHaveClass(componentWrapper, "width-medium");
            TestHelpers.AssertDoesNotHaveClass(componentWrapper, "width-large");
            TestHelpers.AssertDoesNotHaveClass(componentWrapper, "width-full");

            cut.SetParametersAndRender(parameters => parameters
                .Add(p => p.Width, RangeSelectorWidth.Small)
            );
            TestHelpers.AssertHasClass(componentWrapper, "width-small");

            cut.SetParametersAndRender(parameters => parameters
                .Add(p => p.Width, RangeSelectorWidth.Medium)
            );
            TestHelpers.AssertDoesNotHaveClass(componentWrapper, "width-small");
            TestHelpers.AssertHasClass(componentWrapper, "width-medium");

            cut.SetParametersAndRender(parameters => parameters
                .Add(p => p.Width, RangeSelectorWidth.Large)
            );
            TestHelpers.AssertDoesNotHaveClass(componentWrapper, "width-medium");
            TestHelpers.AssertHasClass(componentWrapper, "width-large");

            cut.SetParametersAndRender(parameters => parameters
                .Add(p => p.Width, RangeSelectorWidth.Full)
            );
            TestHelpers.AssertDoesNotHaveClass(componentWrapper, "width-large");
            TestHelpers.AssertHasClass(componentWrapper, "width-full");
        }

        [Fact]
        public void ShowLabelsConfiguredCorrectly()
        {
            var cut = RenderComponent<RangeSelector<int>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.MinValue, 0)
                .Add(p => p.MaxValue, 100)
                .Add(p => p.ShowLabels, true)
            );

            TestHelpers.AssertFound(cut, ".cctc-rangeselector-labels");

            cut.SetParametersAndRender(parameters => parameters
                .Add(p => p.ShowLabels, false)
            );

            TestHelpers.AssertNotFound(cut, ".cctc-rangeselector-labels");
        }

        [Fact]
        public void RangeBoundariesAreRespected()
        {
            var cut = RenderComponent<RangeSelector<int>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.MinValue, 10)
                .Add(p => p.MaxValue, 90)
                .Add(p => p.LowerValue, 30)
                .Add(p => p.UpperValue, 70)
            );

            cut.InvokeAsync(async () => await cut.Instance.UpdateValueAsync(0, true));
            cut.InvokeAsync(async () => await cut.Instance.UpdateValueAsync(100, false));

            var lowerThumb = cut.Find(".cctc-rangeselector-thumb-lower");
            var upperThumb = cut.Find(".cctc-rangeselector-thumb-upper");

            Assert.Equal("10", lowerThumb.GetAttribute("aria-valuenow"));
            Assert.Equal("90", upperThumb.GetAttribute("aria-valuenow"));
        }

        [Fact]
        public void ThumbOrderIsRespected()
        {
            var cut = RenderComponent<RangeSelector<int>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.MinValue, 0)
                .Add(p => p.MaxValue, 100)
                .Add(p => p.LowerValue, 30)
                .Add(p => p.UpperValue, 70)
            );

            cut.InvokeAsync(async () => await cut.Instance.UpdateValueAsync(80, true));
            cut.InvokeAsync(async () => await cut.Instance.UpdateValueAsync(20, false));

            var lowerThumb = cut.Find(".cctc-rangeselector-thumb-lower");
            var upperThumb = cut.Find(".cctc-rangeselector-thumb-upper");

            Assert.Equal("30", lowerThumb.GetAttribute("aria-valuenow"));
            Assert.Equal("70", upperThumb.GetAttribute("aria-valuenow"));
        }

        [Fact]
        public void InvalidTypeParameterThrowsException()
        {
            var cut = () => RenderComponent<RangeSelector<string>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.MinValue, "A")
                .Add(p => p.MaxValue, "Z")
            );

            var actual = Assert.Throws<InvalidOperationException>(cut);
            string expectedMessage = "ValueFromPercentage and PercentageFromValue parameters are required for type String";
            Assert.Contains(expectedMessage, actual.Message);
        }

        [Fact]
        public void ValidNumericTypeParametersDoNotThrowException()
        {
            RenderComponent<RangeSelector<int>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.MinValue, 0)
                .Add(p => p.MaxValue, 100)
            );

            RenderComponent<RangeSelector<double>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.MinValue, 0.0)
                .Add(p => p.MaxValue, 100.0)
            );

            RenderComponent<RangeSelector<float>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.MinValue, 0.0f)
                .Add(p => p.MaxValue, 100.0f)
            );

            RenderComponent<RangeSelector<decimal>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.MinValue, 0.0m)
                .Add(p => p.MaxValue, 100.0m)
            );
        }

        [Fact]
        public void HorizontalRangeSelectorRespondsToArrowKeys()
        {
            int lowerValueChanged = 25;
            int upperValueChanged = 75;
            bool lowerCallbackInvoked = false;
            bool upperCallbackInvoked = false;

            var cut = RenderComponent<RangeSelector<int>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.MinValue, 0)
                .Add(p => p.MaxValue, 100)
                .Add(p => p.LowerValue, 25)
                .Add(p => p.UpperValue, 75)
                .Add(p => p.Orientation, RangeSelectorOrientation.Horizontal)
                .Add(p => p.LowerValueChanged, value => { lowerValueChanged = value; lowerCallbackInvoked = true; })
                .Add(p => p.UpperValueChanged, value => { upperValueChanged = value; upperCallbackInvoked = true; })
            );

            var lowerThumb = cut.Find(".cctc-rangeselector-thumb-lower");
            var upperThumb = cut.Find(".cctc-rangeselector-thumb-upper");

            // Test right arrow on lower thumb (should increase value)
            lowerThumb.KeyDown(key: "ArrowRight");
            Assert.True(lowerCallbackInvoked, "Lower callback should be invoked for ArrowRight");
            Assert.Equal(26, lowerValueChanged);

            // Reset flag
            lowerCallbackInvoked = false;

            // Test left arrow on lower thumb (should decrease value)
            lowerThumb.KeyDown(key: "ArrowLeft");
            Assert.True(lowerCallbackInvoked, "Lower callback should be invoked for ArrowLeft");
            Assert.Equal(25, lowerValueChanged);

            // Test right arrow on upper thumb (should increase value)
            upperThumb.KeyDown(key: "ArrowRight");
            Assert.True(upperCallbackInvoked, "Upper callback should be invoked for ArrowRight");
            Assert.Equal(76, upperValueChanged);

            // Reset flag
            upperCallbackInvoked = false;

            // Test left arrow on upper thumb (should decrease value)  
            upperThumb.KeyDown(key: "ArrowLeft");
            Assert.True(upperCallbackInvoked, "Upper callback should be invoked for ArrowLeft");
            Assert.Equal(75, upperValueChanged);
        }

        [Fact]
        public void VerticalRangeSelectorRespondsToArrowKeys()
        {
            int lowerValueChanged = 25;
            int upperValueChanged = 75;

            var cut = RenderComponent<RangeSelector<int>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.MinValue, 0)
                .Add(p => p.MaxValue, 100)
                .Add(p => p.LowerValue, 25)
                .Add(p => p.UpperValue, 75)
                .Add(p => p.Orientation, RangeSelectorOrientation.Vertical)
                .Add(p => p.LowerValueChanged, value => lowerValueChanged = value)
                .Add(p => p.UpperValueChanged, value => upperValueChanged = value)
            );

            var lowerThumb = cut.Find(".cctc-rangeselector-thumb-lower");
            var upperThumb = cut.Find(".cctc-rangeselector-thumb-upper");

            // Test up arrow on lower thumb (should increase value)
            lowerThumb.KeyDown(key: "ArrowUp");
            Assert.Equal(26, lowerValueChanged);

            // Test down arrow on lower thumb (should decrease value)
            lowerThumb.KeyDown(key: "ArrowDown");
            Assert.Equal(25, lowerValueChanged);

            // Test up arrow on upper thumb (should increase value)
            upperThumb.KeyDown(key: "ArrowUp");
            Assert.Equal(76, upperValueChanged);

            // Test down arrow on upper thumb (should decrease value)
            upperThumb.KeyDown(key: "ArrowDown");
            Assert.Equal(75, upperValueChanged);
        }

        [Fact]
        public void RangeSelectorRespondsToHomeAndEndKeys()
        {
            int lowerValueChanged = 25;
            int upperValueChanged = 75;

            var cut = RenderComponent<RangeSelector<int>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.MinValue, 0)
                .Add(p => p.MaxValue, 100)
                .Add(p => p.LowerValue, 25)
                .Add(p => p.UpperValue, 75)
                .Add(p => p.LowerValueChanged, value => lowerValueChanged = value)
                .Add(p => p.UpperValueChanged, value => upperValueChanged = value)
            );

            var lowerThumb = cut.Find(".cctc-rangeselector-thumb-lower");
            var upperThumb = cut.Find(".cctc-rangeselector-thumb-upper");

            // Test Home key on lower thumb (should set to minimum)
            lowerThumb.KeyDown(key: "Home");
            Assert.Equal(0, lowerValueChanged);

            // Test End key on upper thumb (should set to maximum)
            upperThumb.KeyDown(key: "End");
            Assert.Equal(100, upperValueChanged);
        }

        [Fact]
        public void KeyboardNavigationRespectsRangeBoundaries()
        {
            int lowerValueChanged = 10;
            int upperValueChanged = 90;

            var cut = RenderComponent<RangeSelector<int>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.MinValue, 10)
                .Add(p => p.MaxValue, 90)
                .Add(p => p.LowerValue, 10)
                .Add(p => p.UpperValue, 90)
                .Add(p => p.LowerValueChanged, value => lowerValueChanged = value)
                .Add(p => p.UpperValueChanged, value => upperValueChanged = value)
            );

            var lowerThumb = cut.Find(".cctc-rangeselector-thumb-lower");
            var upperThumb = cut.Find(".cctc-rangeselector-thumb-upper");

            // Test left arrow at minimum boundary (should stay at minimum)
            lowerThumb.KeyDown(key: "ArrowLeft");
            Assert.Equal(10, lowerValueChanged);

            // Test right arrow at maximum boundary (should stay at maximum)
            upperThumb.KeyDown(key: "ArrowRight");
            Assert.Equal(90, upperValueChanged);
        }

        [Fact]
        public void KeyboardNavigationPreventsInvalidRanges()
        {
            int lowerValueChanged = 40;
            int upperValueChanged = 60;

            var cut = RenderComponent<RangeSelector<int>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.MinValue, 0)
                .Add(p => p.MaxValue, 100)
                .Add(p => p.LowerValue, 40)
                .Add(p => p.UpperValue, 60)
                .Add(p => p.LowerValueChanged, value => lowerValueChanged = value)
                .Add(p => p.UpperValueChanged, value => upperValueChanged = value)
            );

            var lowerThumb = cut.Find(".cctc-rangeselector-thumb-lower");
            var upperThumb = cut.Find(".cctc-rangeselector-thumb-upper");

            // Move lower thumb to almost equal upper thumb
            for (int i = 0; i < 19; i++)
            {
                lowerThumb.KeyDown(key: "ArrowRight");
            }
            Assert.Equal(59, lowerValueChanged);

            // Try to move lower thumb beyond upper thumb (should be prevented)
            lowerThumb.KeyDown(key: "ArrowRight");
            Assert.Equal(60, lowerValueChanged); // Should equal upper, not exceed

            lowerThumb.KeyDown(key: "ArrowRight");
            Assert.Equal(60, lowerValueChanged); // Should stay equal, not exceed

            // Try to move upper thumb below lower thumb (should be prevented)
            upperThumb.KeyDown(key: "ArrowLeft");
            Assert.Equal(60, upperValueChanged); // Should stay equal to lower
        }

        [Fact]
        public void DisabledRangeSelectorIgnoresKeyboardInput()
        {
            int lowerValueChanged = 25;
            int upperValueChanged = 75;

            var cut = RenderComponent<RangeSelector<int>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.MinValue, 0)
                .Add(p => p.MaxValue, 100)
                .Add(p => p.LowerValue, 25)
                .Add(p => p.UpperValue, 75)
                .Add(p => p.Disabled, true)
                .Add(p => p.LowerValueChanged, value => lowerValueChanged = value)
                .Add(p => p.UpperValueChanged, value => upperValueChanged = value)
            );

            var lowerThumb = cut.Find(".cctc-rangeselector-thumb-lower");
            var upperThumb = cut.Find(".cctc-rangeselector-thumb-upper");

            // Test arrow keys on disabled component (values should not change)
            lowerThumb.KeyDown(key: "ArrowRight");
            Assert.Equal(25, lowerValueChanged);

            upperThumb.KeyDown(key: "ArrowLeft");
            Assert.Equal(75, upperValueChanged);

            // Test Home and End keys on disabled component (values should not change)
            lowerThumb.KeyDown(key: "Home");
            Assert.Equal(25, lowerValueChanged);

            upperThumb.KeyDown(key: "End");
            Assert.Equal(75, upperValueChanged);
        }
    }
}