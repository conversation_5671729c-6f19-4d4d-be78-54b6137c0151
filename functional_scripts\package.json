{"name": "cctc_components_functional_tests", "version": "0.0.1", "description": "A project for creating the functional specs and tests for CCTC_Components", "main": "index.js", "type": "module", "scripts": {"cucumber": "tsx ./node_modules/@cucumber/cucumber/bin/cucumber.js", "api": "set DEBUG=pw:api && npm run cucumber", "build": "rimraf build && npm run format && npm run lint && npm run cucumber-check", "cucumber-check": "npm run cucumber -- --dry-run --require env/set-environment-variables.ts --require world/custom-world.ts --require step-definitions/**/*.ts --require hooks/**/*.ts  --require-module ts-node/register --format-options \"{\\\"snippetInterface\\\": \\\"async-await\\\"}\" --format summary --format progress --format progress-bar", "debug": "set PWDEBUG=1 && set DEBUG=pw:api && npm run cucumber", "video": "set PWVIDEO=1 && npm run cucumber", "format": "prettier --write \"**/*.{ts,tsx,css,html}\" ", "lint": "eslint", "lint-fix": "eslint --fix", "only": "npm run cucumber -- --tags", "only-debug": "set PWDEBUG=1 && set DEBUG=pw:api && npm run cucumber -- --tags", "report": "playwright open reports/report.html", "snippets": "npm run cucumber -- features/**/*.feature --dry-run --format snippets", "steps-usage": "npm run cucumber -- features/**/*.feature --dry-run", "all": "npm run cucumber -- features/**/*.feature", "test": "npm run cucumber", "test:parallel": "npm run cucumber -- --parallel 3", "test:parallel-retry": "npm run cucumber -- --parallel 3 --retry 5", "docker": "docker run --rm --network host -v $(pwd):/work/ -w /work/ -it mcr.microsoft.com/playwright:latest /bin/bash", "allure": "allure serve reports/allure-results", "tryit": "tsc", "imagediff": "node ./src/utils/imageDiff.mjs", "imagediff-bin": "pixelmatch"}, "scriptsComments": {"imagediff-bin": "use the pixelmatch binary. Arg1: File path of first png file, Arg2: File path of second png file, Arg3: threshold (0-1)"}, "engines": {"node": ">=14"}, "author": "CCTC", "bugs": {"url": "https://github.com/CCTC-team/CCTC_Components_functional_tests/issues"}, "homepage": "https://github.com/CCTC-team/CCTC_Components_functional_tests/blob/master/README.md", "devDependencies": {"@cucumber/cucumber": "11.1.1", "@cucumber/html-formatter": "21.7.0", "@cucumber/messages": "27.0.2", "@cucumber/pretty-formatter": "1.0.1", "@playwright/test": "^1.49.1", "@stylistic/eslint-plugin": "2.12.1", "@types/fs-extra": "11.0.4", "@types/node": "22.10.2", "@types/pixelmatch": "5.2.6", "@types/pngjs": "6.0.5", "@typescript-eslint/eslint-plugin": "8.23.0", "@typescript-eslint/parser": "8.23.0", "allure-cucumberjs": "3.0.7", "cucumber-html-reporter": "7.2.0", "eslint": "9.17.0", "eslint-config-prettier": "9.1.0", "eslint-plugin-playwright": "2.1.0", "eslint-plugin-prettier": "5.2.3", "fs-extra": "11.2.0", "multiple-cucumber-html-reporter": "3.8.0", "open": "10.1.0", "pixelmatch": "6.0.0", "pngjs": "7.0.0", "prettier": "3.4.2", "rimraf": "6.0.1", "ts-node": "10.9.2", "tsx": "4.19.2", "typescript": "5.7.2", "typescript-eslint": "8.23.0"}}