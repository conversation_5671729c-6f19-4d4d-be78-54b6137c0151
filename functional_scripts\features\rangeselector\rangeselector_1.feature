@component @rangeselector @rangeselector_1
Feature: the range selector component sample page navigation
    Scenario: the range selector sample page is available
        Given the user is at the home page
        When the user selects the "Range selector" component in the container "Input"
        Then the url ending is "rangeselectorsample"
        And the range selector label displays "Selected range: 25 - 75"
        