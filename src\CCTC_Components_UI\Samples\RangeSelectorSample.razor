@page "/rangeselectorsample"
@using CCTC_Components.Components.RangeSelector

@{
    var description = new List<string>
    {
        "The RangeSelector component provides a dual-handle range slider that supports generic types. " +
        "It allows users to select a range between two values using intuitive drag-and-drop interaction " +
        "or keyboard navigation. The component supports numeric types out of the box and can be extended " +
        "to work with custom types through conversion functions."
    };

    var features = new List<(string, string)>
    {
        ("generic type support", "supports integers, doubles, floats, decimals, and custom types with conversion functions"),
        ("keyboard navigation", "full keyboard support with arrow keys, Home/End keys for precise control"),
        ("accessibility", "complete ARIA support with proper roles and labels for screen readers"),
        ("touch friendly", "optimized for touch devices with proper event handling"),
        ("customizable display", "custom formatting and styling options"),
        ("disabled state", "can be disabled to prevent user interaction"),
        ("vertical orientation", "supports both horizontal and vertical orientations for different layout needs"),
        ("flexible sizing", "multiple width/height options: auto, small, medium, large, and full")
    };

    var gotchas = new List<(string, string)>
    {
        ("custom types", "non-numeric types require ValueFromPercentage and PercentageFromValue conversion functions"),
        ("range validation", "ensure MinValue is less than MaxValue for proper operation"),
        ("JavaScript dependency", "requires rangeselector.js for enhanced interaction"),
        ("vertical orientation", "vertical range selectors require adequate container height for proper display"),
        ("width settings", "width parameter affects horizontal sliders; for vertical sliders it controls height")
    };

    var usage = new List<string>
    {
    };

    var usageCode = new List<(string title, string description, string code, RenderFragment fragment)>
    {
        ("Integer Range", "Basic usage with integer values for selecting a numeric range.",
@"<RangeSelector TValue=""int""
    Id=""integer-range""
    MinValue=""0""
    MaxValue=""100""
    LowerValue=""intLower""
    UpperValue=""intUpper""
    LowerValueChanged=""@((int val) => intLower = val)""
    UpperValueChanged=""@((int val) => intUpper = val)"" />

<p>Selected range: @intLower - @intUpper</p>

@code {
    private int intLower = 20;
    private int intUpper = 80;
}",
        @<div>
            <RangeSelector TValue="int"
                Id="integer-range-usage"
                MinValue="0"
                MaxValue="100"
                LowerValue="intLowerUsage"
                UpperValue="intUpperUsage"
                LowerValueChanged="@((int val) => intLowerUsage = val)"
                UpperValueChanged="@((int val) => intUpperUsage = val)" />
            <p class="mt-3">Selected range: @intLowerUsage - @intUpperUsage</p>
        </div>
        ),

        ("Double Range", "Double precision values for selecting a numeric range with decimal values.",
@"<RangeSelector TValue=""double""
    Id=""double-range""
    MinValue=""0.0""
    MaxValue=""10.0""
    LowerValue=""doubleLower""
    UpperValue=""doubleUpper""
    LowerValueChanged=""@((double val) => doubleLower = val)""
    UpperValueChanged=""@((double val) => doubleUpper = val)""
    DisplayFormatter=""@(val => $""{val:F2}"")""
    ShowLabels=""true"" />

<p>Selected range: @doubleLower.ToString(""F2"") - @doubleUpper.ToString(""F2"")</p>

@code {
    private double doubleLower = 2.5;
    private double doubleUpper = 7.8;
}",
        @<div>
            <RangeSelector TValue="double"
                Id="double-range-usage"
                MinValue="0.0"
                MaxValue="10.0"
                LowerValue="doubleLowerUsage"
                UpperValue="doubleUpperUsage"
                LowerValueChanged="@((double val) => doubleLowerUsage = val)"
                UpperValueChanged="@((double val) => doubleUpperUsage = val)"
                DisplayFormatter="@(val => $"{val:F2}")"
                ShowLabels="true" />
            <p class="mt-3">Selected range: @doubleLowerUsage.ToString("F2") - @doubleUpperUsage.ToString("F2")</p>
        </div>
        ),

        ("Date Range", "Date selection using custom conversion functions (date only, no time).",
@"<RangeSelector TValue=""DateTime""
    Id=""date-range""
    MinValue=""dateMin""
    MaxValue=""dateMax""
    LowerValue=""dateLower""
    UpperValue=""dateUpper""
    LowerValueChanged=""@((DateTime val) => dateLower = val)""
    UpperValueChanged=""@((DateTime val) => dateUpper = val)""
    ValueFromPercentage=""@DateFromPercentage""
    PercentageFromValue=""@DateToPercentage""
    DisplayFormatter=""@(val => val.ToString(""yyyy-MM-dd""))"" />

<p>Selected date range: @dateLower.ToString(""yyyy-MM-dd"") - @dateUpper.ToString(""yyyy-MM-dd"")</p>

@code {
    private DateTime dateMin = new DateTime(2024, 1, 1);
    private DateTime dateMax = new DateTime(2024, 12, 31);
    private DateTime dateLower = new DateTime(2024, 3, 15);
    private DateTime dateUpper = new DateTime(2024, 9, 15);

    private DateTime DateFromPercentage(DateTime min, DateTime max, double percentage)
    {
        var minDate = min.Date;
        var maxDate = max.Date;
        var totalDays = (maxDate - minDate).TotalDays;
        var percentageDays = totalDays * (percentage / 100.0);
        return minDate.AddDays(percentageDays);
    }

    private double DateToPercentage(DateTime min, DateTime max, DateTime value)
    {
        var minDate = min.Date;
        var maxDate = max.Date;
        var valueDate = value.Date;
        var totalDays = (maxDate - minDate).TotalDays;
        var currentDays = (valueDate - minDate).TotalDays;
        return totalDays == 0 ? 0 : (currentDays / totalDays) * 100.0;
    }
}",
        @<div>
            <RangeSelector TValue="DateTime"
                Id="date-range-usage"
                MinValue="dateMinUsage"
                MaxValue="dateMaxUsage"
                LowerValue="dateLowerUsage"
                UpperValue="dateUpperUsage"
                LowerValueChanged="@((DateTime val) => dateLowerUsage = val)"
                UpperValueChanged="@((DateTime val) => dateUpperUsage = val)"
                ValueFromPercentage="@DateFromPercentage"
                PercentageFromValue="@DateToPercentage"
                DisplayFormatter="@(val => val.ToString("yyyy-MM-dd"))" />
            <p>Selected date range: @dateLowerUsage.ToString("yyyy-MM-dd") - @dateUpperUsage.ToString("yyyy-MM-dd")</p>
        </div>
        ),

        ("DateTime Range", "Date and time selection with both date and time components.",
@"<RangeSelector TValue=""DateTime""
    Id=""datetime-range""
    MinValue=""dateTimeMin""
    MaxValue=""dateTimeMax""
    LowerValue=""dateTimeLower""
    UpperValue=""dateTimeUpper""
    LowerValueChanged=""@((DateTime val) => dateTimeLower = val)""
    UpperValueChanged=""@((DateTime val) => dateTimeUpper = val)""
    ValueFromPercentage=""@DateTimeFromPercentage""
    PercentageFromValue=""@DateTimeToPercentage""
    DisplayFormatter=""@(val => val.ToString(""yyyy-MM-dd HH:mm""))"" />

<p>Selected datetime range: @dateTimeLower.ToString(""yyyy-MM-dd HH:mm"") - @dateTimeUpper.ToString(""yyyy-MM-dd HH:mm"")</p>

@code {
    private DateTime dateTimeMin = new DateTime(2024, 1, 1, 0, 0, 0);
    private DateTime dateTimeMax = new DateTime(2024, 12, 31, 23, 59, 59);
    private DateTime dateTimeLower = new DateTime(2024, 3, 15, 9, 30, 0);
    private DateTime dateTimeUpper = new DateTime(2024, 9, 15, 17, 45, 0);

    private DateTime DateTimeFromPercentage(DateTime min, DateTime max, double percentage)
    {
        var totalTicks = max.Ticks - min.Ticks;
        var percentageTicks = (long)(totalTicks * (percentage / 100.0));
        return new DateTime(min.Ticks + percentageTicks);
    }

    private double DateTimeToPercentage(DateTime min, DateTime max, DateTime value)
    {
        var totalTicks = max.Ticks - min.Ticks;
        var currentTicks = value.Ticks - min.Ticks;
        return totalTicks == 0 ? 0 : (currentTicks / (double)totalTicks) * 100.0;
    }
}",
        @<div>
            <RangeSelector TValue="DateTime"
                Id="datetime-range-usage"
                MinValue="dateTimeMinUsage"
                MaxValue="dateTimeMaxUsage"
                LowerValue="dateTimeLowerUsage"
                UpperValue="dateTimeUpperUsage"
                LowerValueChanged="@((DateTime val) => dateTimeLowerUsage = val)"
                UpperValueChanged="@((DateTime val) => dateTimeUpperUsage = val)"
                ValueFromPercentage="@DateTimeFromPercentage"
                PercentageFromValue="@DateTimeToPercentage"
                DisplayFormatter="@(val => val.ToString("yyyy-MM-dd HH:mm"))" />
            <p>Selected datetime range: @dateTimeLowerUsage.ToString("yyyy-MM-dd HH:mm") - @dateTimeUpperUsage.ToString("yyyy-MM-dd HH:mm")</p>
        </div>
        ),

        ("Time Range (TimeSpan)", "Time duration selection with hour and minute precision.",
@"<RangeSelector TValue=""TimeSpan""
    Id=""time-range""
    MinValue=""TimeSpan.Zero""
    MaxValue=""TimeSpan.FromHours(24)""
    LowerValue=""timeLower""
    UpperValue=""timeUpper""
    LowerValueChanged=""@((TimeSpan val) => timeLower = val)""
    UpperValueChanged=""@((TimeSpan val) => timeUpper = val)""
    ValueFromPercentage=""@TimeFromPercentage""
    PercentageFromValue=""@TimeToPercentage""
    DisplayFormatter=""@(val => FormatTimeSpan(val))"" />

<p>Selected time range: @timeLower.ToString(@""hh\:mm"") - @timeUpper.ToString(@""hh\:mm"")</p>

@code {
    private TimeSpan timeLower = TimeSpan.FromHours(9);
    private TimeSpan timeUpper = TimeSpan.FromHours(17);

    private TimeSpan TimeFromPercentage(TimeSpan min, TimeSpan max, double percentage)
    {
        var totalTicks = max.Ticks - min.Ticks;
        var percentageTicks = (long)(totalTicks * (percentage / 100.0));
        return new TimeSpan(min.Ticks + percentageTicks);
    }

    private double TimeToPercentage(TimeSpan min, TimeSpan max, TimeSpan value)
    {
        var totalTicks = max.Ticks - min.Ticks;
        var currentTicks = value.Ticks - min.Ticks;
        return totalTicks == 0 ? 0 : (currentTicks / (double)totalTicks) * 100.0;
    }

    private string FormatTimeSpan(TimeSpan timeSpan)
    {
        // Handle the special case of 24 hours (1 day)
        if (timeSpan.Days >= 1 && timeSpan.Hours == 0 && timeSpan.Minutes == 0)
        {
            return ""24:00"";
        }
        
        // For all other cases, use standard hours and minutes formatting
        var totalHours = (int)timeSpan.TotalHours;
        return $""{totalHours:D2}:{timeSpan.Minutes:D2}"";
    }
}",
        @<div>
            <RangeSelector TValue="TimeSpan"
                Id="time-range-usage"
                MinValue="TimeSpan.Zero"
                MaxValue="TimeSpan.FromHours(24)"
                LowerValue="timeLowerUsage"
                UpperValue="timeUpperUsage"
                LowerValueChanged="@((TimeSpan val) => timeLowerUsage = val)"
                UpperValueChanged="@((TimeSpan val) => timeUpperUsage = val)"
                ValueFromPercentage="@TimeFromPercentage"
                PercentageFromValue="@TimeToPercentage"
                DisplayFormatter="@(val => FormatTimeSpan(val))" />
            <p>Selected time range: @timeLowerUsage.ToString(@"hh\:mm") - @timeUpperUsage.ToString(@"hh\:mm")</p>
        </div>
        ),

        ("Multiple Sliders", "Multiple range selectors for different value types in one interface.",
@"<div class=""range-group"">
    <h5>Price Range (£)</h5>
    <RangeSelector TValue=""double""
        Id=""price-range""
        MinValue=""0.00""
        MaxValue=""1000.00""
        LowerValue=""priceLower""
        UpperValue=""priceUpper""
        LowerValueChanged=""@((double val) => priceLower = val)""
        UpperValueChanged=""@((double val) => priceUpper = val)""
        DisplayFormatter=""@(val => $""£{val:F2}"")"" 
        StepFunction=""@PriceStepFunction""
        ShowLabels=""true"" />
    
    <h5>Quantity Range</h5>
    <RangeSelector TValue=""int""
        Id=""quantity-range""
        MinValue=""1""
        MaxValue=""50""
        LowerValue=""quantityLower""
        UpperValue=""quantityUpper""
        LowerValueChanged=""@((int val) => quantityLower = val)""
        UpperValueChanged=""@((int val) => quantityUpper = val)"" />
</div>

<p>Price: £@priceLower.ToString(""F2"") - £@priceUpper.ToString(""F2"")</p>
<p>Quantity: @quantityLower - @quantityUpper</p>

@code {
    private double priceLower = 100.50;
    private double priceUpper = 500.75;
    private int quantityLower = 5;
    private int quantityUpper = 25;

    // Step function for price range to allow decimal precision
    private double PriceStepFunction(double min, double max)
    {
        // Return 0.1% step size to allow for decimal increments (1000 range = 1.0 increment = 0.1% step)
        // This allows for finer control and decimal values like £100.50
        return 0.1;
    }
}",
        @<div>
            <div class="range-group">
                <h5>Price Range (£)</h5>
                <RangeSelector TValue="double"
                    Id="price-range-usage"
                    MinValue="0.00"
                    MaxValue="1000.00"
                    LowerValue="priceLowerUsage"
                    UpperValue="priceUpperUsage"
                    LowerValueChanged="@((double val) => priceLowerUsage = val)"
                    UpperValueChanged="@((double val) => priceUpperUsage = val)"
                    DisplayFormatter="@(val => $"£{val:F2}")"
                    StepFunction="@PriceStepFunction"
                    ShowLabels="true" />
                
                <h5>Quantity Range</h5>
                <RangeSelector TValue="int"
                    Id="quantity-range-usage"
                    MinValue="1"
                    MaxValue="50"
                    LowerValue="quantityLowerUsage"
                    UpperValue="quantityUpperUsage"
                    LowerValueChanged="@((int val) => quantityLowerUsage = val)"
                    UpperValueChanged="@((int val) => quantityUpperUsage = val)" />
            </div>
            <p>Price: £@priceLowerUsage.ToString("F2") - £@priceUpperUsage.ToString("F2")</p>
            <p>Quantity: @quantityLowerUsage - @quantityUpperUsage</p>
        </div>
        ),

        ("Disabled State", "Range selector in disabled state to prevent user interaction.",
@"<RangeSelector TValue=""int""
    Id=""disabled-range""
    MinValue=""0""
    MaxValue=""100""
    LowerValue=""disabledLower""
    UpperValue=""disabledUpper""
    Disabled=""true""
    ShowLabels=""true"" />

<p>This range selector is disabled and cannot be modified.</p>

@code {
    private int disabledLower = 25;
    private int disabledUpper = 75;
}",
        @<div>
            <RangeSelector TValue="int"
                Id="disabled-range-usage"
                MinValue="0"
                MaxValue="100"
                LowerValue="25"
                UpperValue="75"
                Disabled="true"
                ShowLabels="true" />
            <p>This range selector is disabled and cannot be modified.</p>
        </div>
        ),

        ("Vertical Orientation", "Range selector in vertical orientation for space-constrained layouts.",
@"<RangeSelector TValue=""int""
    Id=""vertical-range""
    MinValue=""0""
    MaxValue=""100""
    LowerValue=""verticalLower""
    UpperValue=""verticalUpper""
    LowerValueChanged=""@((int val) => verticalLower = val)""
    UpperValueChanged=""@((int val) => verticalUpper = val)""
    Orientation=""RangeSelectorOrientation.Vertical""
    ShowLabels=""true"" />

<p>Selected range: @verticalLower - @verticalUpper</p>

@code {
    private int verticalLower = 30;
    private int verticalUpper = 70;
}",
        @<div>
            <RangeSelector TValue="int"
                Id="vertical-range-usage"
                MinValue="0"
                MaxValue="100"
                LowerValue="verticalLowerUsage"
                UpperValue="verticalUpperUsage"
                LowerValueChanged="@((int val) => verticalLowerUsage = val)"
                UpperValueChanged="@((int val) => verticalUpperUsage = val)"
                Orientation="RangeSelectorOrientation.Vertical"
                ShowLabels="true" />
            <p>Selected range: @verticalUpperUsage - @verticalLowerUsage (top to bottom)</p>
        </div>
        ),

        ("Small Width", "Range selector with small width for compact layouts.",
@"<RangeSelector TValue=""int""
    Id=""small-width-range""
    MinValue=""0""
    MaxValue=""100""
    LowerValue=""smallLower""
    UpperValue=""smallUpper""
    LowerValueChanged=""@((int val) => smallLower = val)""
    UpperValueChanged=""@((int val) => smallUpper = val)""
    Width=""RangeSelectorWidth.Small""
    ShowLabels=""true"" />

<p>Selected range: @smallLower - @smallUpper</p>

@code {
    private int smallLower = 15;
    private int smallUpper = 85;
}",
        @<div>
            <RangeSelector TValue="int"
                Id="small-width-range-usage"
                MinValue="0"
                MaxValue="100"
                LowerValue="smallLowerUsage"
                UpperValue="smallUpperUsage"
                LowerValueChanged="@((int val) => smallLowerUsage = val)"
                UpperValueChanged="@((int val) => smallUpperUsage = val)"
                Width="RangeSelectorWidth.Small"
                ShowLabels="true" />
            <p>Selected range: @smallLowerUsage - @smallUpperUsage</p>
        </div>
        ),

        ("Vertical Small", "Vertical range selector with small height for compact layouts.",
@"<RangeSelector TValue=""int""
    Id=""vertical-small-range""
    MinValue=""0""
    MaxValue=""100""
    LowerValue=""verticalSmallLower""
    UpperValue=""verticalSmallUpper""
    LowerValueChanged=""@((int val) => verticalSmallLower = val)""
    UpperValueChanged=""@((int val) => verticalSmallUpper = val)""
    Orientation=""RangeSelectorOrientation.Vertical""
    Width=""RangeSelectorWidth.Small""
    ShowLabels=""true"" />

<p>Selected range: @verticalSmallLower - @verticalSmallUpper</p>

@code {
    private int verticalSmallLower = 40;
    private int verticalSmallUpper = 60;
}",
        @<div>
            <RangeSelector TValue="int"
                Id="vertical-small-range-usage"
                MinValue="0"
                MaxValue="100"
                LowerValue="verticalSmallLowerUsage"
                UpperValue="verticalSmallUpperUsage"
                LowerValueChanged="@((int val) => verticalSmallLowerUsage = val)"
                UpperValueChanged="@((int val) => verticalSmallUpperUsage = val)"
                Orientation="RangeSelectorOrientation.Vertical"
                Width="RangeSelectorWidth.Small"
                ShowLabels="true" />
            <p>Selected range: @verticalSmallUpperUsage - @verticalSmallLowerUsage (top to bottom)</p>
        </div>
        ),

        ("Vertical Side Display", "Vertical range selector with result displayed on the side for better space utilization.",
@"<div style=""display: flex; align-items: center; gap: 15px;"">
    <RangeSelector TValue=""int""
        Id=""vertical-side-range""
        MinValue=""0""
        MaxValue=""100""
        LowerValue=""verticalSideLower""
        UpperValue=""verticalSideUpper""
        LowerValueChanged=""@((int val) => verticalSideLower = val)""
        UpperValueChanged=""@((int val) => verticalSideUpper = val)""
        Orientation=""RangeSelectorOrientation.Vertical""
        Width=""RangeSelectorWidth.Medium""
        ShowLabels=""true"" />
    <div>
        <strong>Selected Range:</strong><br/>
        Lower: @verticalSideLower<br/>
        Upper: @verticalSideUpper<br/>
        <small>Span: @(verticalSideUpper - verticalSideLower)</small>
    </div>
</div>

@code {
    private int verticalSideLower = 35;
    private int verticalSideUpper = 75;
}",
        @<div>
            <div style="display: flex; align-items: center; gap: 15px;">
                <RangeSelector TValue="int"
                    Id="vertical-side-range-usage"
                    MinValue="0"
                    MaxValue="100"
                    LowerValue="verticalSideLowerUsage"
                    UpperValue="verticalSideUpperUsage"
                    LowerValueChanged="@((int val) => verticalSideLowerUsage = val)"
                    UpperValueChanged="@((int val) => verticalSideUpperUsage = val)"
                    Orientation="RangeSelectorOrientation.Vertical"
                    Width="RangeSelectorWidth.Medium"
                    ShowLabels="true" />
                <div>
                    <strong>Selected Range:</strong><br/>
                    Top: @verticalSideUpperUsage<br/>
                    Bottom: @verticalSideLowerUsage<br/>
                    <small>Span: @(verticalSideUpperUsage - verticalSideLowerUsage)</small>
                </div>
            </div>
        </div>
        ),

        ("Multiple Vertical Controls", "Multiple vertical range selectors arranged side-by-side, each with individual side displays.",
@"<div style=""display: flex; gap: 30px; justify-content: center;"">
    <div style=""display: flex; align-items: center; gap: 15px;"">
        <div style=""text-align: center; min-width: 80px;"">
            <strong>Bass</strong><br/>
            <RangeSelector TValue=""int""
                Id=""bass-range""
                MinValue=""0""
                MaxValue=""100""
                LowerValue=""bassLower""
                UpperValue=""bassUpper""
                LowerValueChanged=""@((int val) => bassLower = val)""
                UpperValueChanged=""@((int val) => bassUpper = val)""
                Orientation=""RangeSelectorOrientation.Vertical""
                Width=""RangeSelectorWidth.Small""
                ShowLabels=""false"" />
        </div>
        <div>
            <small>High: @bassUpper<br/>
            Low: @bassLower</small>
        </div>
    </div>
    <div style=""display: flex; align-items: center; gap: 15px;"">
        <div style=""text-align: center; min-width: 80px;"">
            <strong>Mid</strong><br/>
            <RangeSelector TValue=""int""
                Id=""mid-range""
                MinValue=""0""
                MaxValue=""100""
                LowerValue=""midLower""
                UpperValue=""midUpper""
                LowerValueChanged=""@((int val) => midLower = val)""
                UpperValueChanged=""@((int val) => midUpper = val)""
                Orientation=""RangeSelectorOrientation.Vertical""
                Width=""RangeSelectorWidth.Small""
                ShowLabels=""false"" />
        </div>
        <div>
            <small>High: @midUpper<br/>
            Low: @midLower</small>
        </div>
    </div>
    <div style=""display: flex; align-items: center; gap: 15px;"">
        <div style=""text-align: center; min-width: 80px;"">
            <strong>Treble</strong><br/>
            <RangeSelector TValue=""int""
                Id=""treble-range""
                MinValue=""0""
                MaxValue=""100""
                LowerValue=""trebleLower""
                UpperValue=""trebleUpper""
                LowerValueChanged=""@((int val) => trebleLower = val)""
                UpperValueChanged=""@((int val) => trebleUpper = val)""
                Orientation=""RangeSelectorOrientation.Vertical""
                Width=""RangeSelectorWidth.Small""
                ShowLabels=""false"" />
        </div>
        <div>
            <small>High: @trebleUpper<br/>
            Low: @trebleLower</small>
        </div>
    </div>
</div>

@code {
    private int bassLower = 20;
    private int bassUpper = 60;
    private int midLower = 30;
    private int midUpper = 80;
    private int trebleLower = 40;
    private int trebleUpper = 90;
}",
        @<div>
            <div style="display: flex; gap: 30px; justify-content: center;">
                <div style="display: flex; align-items: center; gap: 15px;">
                    <div style="text-align: center; min-width: 80px;">
                        <strong>Bass</strong><br/>
                        <RangeSelector TValue="int"
                            Id="bass-range-usage"
                            MinValue="0"
                            MaxValue="100"
                            LowerValue="bassLowerUsage"
                            UpperValue="bassUpperUsage"
                            LowerValueChanged="@((int val) => bassLowerUsage = val)"
                            UpperValueChanged="@((int val) => bassUpperUsage = val)"
                            Orientation="RangeSelectorOrientation.Vertical"
                            Width="RangeSelectorWidth.Small"
                            ShowLabels="false" />
                    </div>
                    <div>
                        <small>High: @bassUpperUsage<br/>
                        Low: @bassLowerUsage</small>
                    </div>
                </div>
                <div style="display: flex; align-items: center; gap: 15px;">
                    <div style="text-align: center; min-width: 80px;">
                        <strong>Mid</strong><br/>
                        <RangeSelector TValue="int"
                            Id="mid-range-usage"
                            MinValue="0"
                            MaxValue="100"
                            LowerValue="midLowerUsage"
                            UpperValue="midUpperUsage"
                            LowerValueChanged="@((int val) => midLowerUsage = val)"
                            UpperValueChanged="@((int val) => midUpperUsage = val)"
                            Orientation="RangeSelectorOrientation.Vertical"
                            Width="RangeSelectorWidth.Small"
                            ShowLabels="false" />
                    </div>
                    <div>
                        <small>High: @midUpperUsage<br/>
                        Low: @midLowerUsage</small>
                    </div>
                </div>
                <div style="display: flex; align-items: center; gap: 15px;">
                    <div style="text-align: center; min-width: 80px;">
                        <strong>Treble</strong><br/>
                        <RangeSelector TValue="int"
                            Id="treble-range-usage"
                            MinValue="0"
                            MaxValue="100"
                            LowerValue="trebleLowerUsage"
                            UpperValue="trebleUpperUsage"
                            LowerValueChanged="@((int val) => trebleLowerUsage = val)"
                            UpperValueChanged="@((int val) => trebleUpperUsage = val)"
                            Orientation="RangeSelectorOrientation.Vertical"
                            Width="RangeSelectorWidth.Small"
                            ShowLabels="false" />
                    </div>
                    <div>
                        <small>High: @trebleUpperUsage<br/>
                        Low: @trebleLowerUsage</small>
                    </div>
                </div>
            </div>
        </div>
        )
    };

    var relatedComponents = new List<(Relation relation, string display, string url, string description)>();
}

<Sampler
    ComponentName="RangeSelector"
    ComponentCssName="rangeselector"
    Description="@description"
    RelatedComponents="@relatedComponents"
    UsageText="Typical usages of the <code>RangeSelector</code> component are shown below"
    UsageCodeList="@usageCode"
    ContentHeightPixels="300"
    Features="@features"
    Gotchas="@gotchas"
    >
    <ExampleTemplate>
        <div>
            <RangeSelector TValue="int"
                Id="range-selector-example"
                MinValue="0"
                MaxValue="100"
                LowerValue="intLowerExample"
                UpperValue="intUpperExample"
                LowerValueChanged="@((int val) => intLowerExample = val)"
                UpperValueChanged="@((int val) => intUpperExample = val)"
                ShowLabels="true" />

            <div class="mt-3">
                Selected range: @intLowerExample - @intUpperExample
            </div>
        </div>
    </ExampleTemplate>
</Sampler>

@code {
    // Example values
    private int intLowerExample = 25;
    private int intUpperExample = 75;

    // Usage example values
    private int intLowerUsage = 20;
    private int intUpperUsage = 80;

    private double doubleLowerUsage = 2.5;
    private double doubleUpperUsage = 7.8;

    // Date range (date only)
    private DateTime dateMinUsage = new DateTime(2024, 1, 1);
    private DateTime dateMaxUsage = new DateTime(2024, 12, 31);
    private DateTime dateLowerUsage = new DateTime(2024, 3, 15);
    private DateTime dateUpperUsage = new DateTime(2024, 9, 15);

    // DateTime range (date and time)
    private DateTime dateTimeMinUsage = new DateTime(2024, 1, 1, 0, 0, 0);
    private DateTime dateTimeMaxUsage = new DateTime(2024, 12, 31, 23, 59, 59);
    private DateTime dateTimeLowerUsage = new DateTime(2024, 3, 15, 9, 30, 0);
    private DateTime dateTimeUpperUsage = new DateTime(2024, 9, 15, 17, 45, 0);

    private TimeSpan timeLowerUsage = TimeSpan.FromHours(9);
    private TimeSpan timeUpperUsage = TimeSpan.FromHours(17);

    private double priceLowerUsage = 100.50;
    private double priceUpperUsage = 500.75;

    private int quantityLowerUsage = 5;
    private int quantityUpperUsage = 25;

    // Vertical orientation usage
    private int verticalLowerUsage = 30;
    private int verticalUpperUsage = 70;

    // Small width usage
    private int smallLowerUsage = 15;
    private int smallUpperUsage = 85;

    // Vertical small usage
    private int verticalSmallLowerUsage = 40;
    private int verticalSmallUpperUsage = 60;

    // Vertical side display usage
    private int verticalSideLowerUsage = 35;
    private int verticalSideUpperUsage = 75;

    // Multiple vertical controls (equalizer-style)
    private int bassLowerUsage = 20;
    private int bassUpperUsage = 60;
    private int midLowerUsage = 30;
    private int midUpperUsage = 80;
    private int trebleLowerUsage = 40;
    private int trebleUpperUsage = 90;

    // Date conversion functions (date only, no time)
    private DateTime DateFromPercentage(DateTime min, DateTime max, double percentage)
    {
        var minDate = min.Date;
        var maxDate = max.Date;
        var totalDays = (maxDate - minDate).TotalDays;
        var percentageDays = totalDays * (percentage / 100.0);
        return minDate.AddDays(percentageDays);
    }

    private double DateToPercentage(DateTime min, DateTime max, DateTime value)
    {
        var minDate = min.Date;
        var maxDate = max.Date;
        var valueDate = value.Date;
        var totalDays = (maxDate - minDate).TotalDays;
        var currentDays = (valueDate - minDate).TotalDays;
        return totalDays == 0 ? 0 : (currentDays / totalDays) * 100.0;
    }

    // DateTime conversion functions (date and time)
    private DateTime DateTimeFromPercentage(DateTime min, DateTime max, double percentage)
    {
        var totalTicks = max.Ticks - min.Ticks;
        var percentageTicks = (long)(totalTicks * (percentage / 100.0));
        return new DateTime(min.Ticks + percentageTicks);
    }

    private double DateTimeToPercentage(DateTime min, DateTime max, DateTime value)
    {
        var totalTicks = max.Ticks - min.Ticks;
        var currentTicks = value.Ticks - min.Ticks;
        return totalTicks == 0 ? 0 : (currentTicks / (double)totalTicks) * 100.0;
    }

    // TimeSpan conversion functions
    private TimeSpan TimeFromPercentage(TimeSpan min, TimeSpan max, double percentage)
    {
        var totalTicks = max.Ticks - min.Ticks;
        var percentageTicks = (long)(totalTicks * (percentage / 100.0));
        return new TimeSpan(min.Ticks + percentageTicks);
    }

    private double TimeToPercentage(TimeSpan min, TimeSpan max, TimeSpan value)
    {
        var totalTicks = max.Ticks - min.Ticks;
        var currentTicks = value.Ticks - min.Ticks;
        return totalTicks == 0 ? 0 : (currentTicks / (double)totalTicks) * 100.0;
    }

    // Custom formatter to display 24:00 instead of 00:00 for 24 hours
    private string FormatTimeSpan(TimeSpan timeSpan)
    {
        // Handle the special case of 24 hours (1 day)
        if (timeSpan.Days >= 1 && timeSpan.Hours == 0 && timeSpan.Minutes == 0)
        {
            return "24:00";
        }
        
        // For all other cases, use standard hours and minutes formatting
        var totalHours = (int)timeSpan.TotalHours;
        return $"{totalHours:D2}:{timeSpan.Minutes:D2}";
    }

    // Step function for price range to allow decimal precision
    private double PriceStepFunction(double min, double max)
    {
        // Return 0.1% step size to allow for decimal increments (1000 range = 1.0 increment = 0.1% step)
        // This allows for finer control and decimal values like £100.50
        return 0.1;
    }
}