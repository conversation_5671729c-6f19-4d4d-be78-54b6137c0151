1 - the range selector component sample page navigation
2 - all range selector data types are displayed correctly
3 - the range selector functions correctly with blue middle section and value constraints
4 - the range selector supports different orientations and sizing options
5 - multiple range selectors work independently and simultaneously
6 - the range selector supports accessibility and keyboard navigation
7 - the range selector disabled state prevents interaction