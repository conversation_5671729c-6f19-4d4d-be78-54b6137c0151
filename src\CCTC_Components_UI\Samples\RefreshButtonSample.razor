﻿@page "/refreshbuttonsample"

@{
    var description = new List<string>
    {
        "The RefreshButton component. This builds on the base button to include confirmation feedback via the UI"
    };

    var features = new List<(string, string)>
    {
        ("Configuration", "Customise the button icon and text. The button icon can be placed on the left or right. The button can be disabled"),
        ("Interactivity", "A callback can be associated with user button click. Optionally include confirmation feedback of user button click via the UI")
    };

    var usageCode = new List<(string title, string description, string code, RenderFragment fragment)>
    {
        ("ButtonIcon: refresh, IconPosition: Left, styled with border", "",
@"<RefreshButton
    Id=""usage1""
    ButtonIcon=""refresh""
    ButtonText=""@ShortRefreshButtonText""
    IconPosition=""PositionX.Left""
    StyledWithBorder=""true""
    ShowConfirmation=""true""
    OnClick=""@(() => Console.WriteLine(""click""))"">
</RefreshButton>

@code {

    string ShortRefreshButtonText { get; set; } = ""some text"";
}",
    @<RefreshButton
        Id="usage1"
        ButtonIcon="refresh"
        ButtonText="@ShortRefreshButtonText"
        IconPosition="PositionX.Left"
        StyledWithBorder="true"
        ShowConfirmation="true"
        OnClick="@(() => Console.WriteLine("click"))">
    </RefreshButton>),
        ("ButtonIcon: light_mode, IconPosition: Right, overridden icon color via Style", "",
@"<RefreshButton
    Id=""usage2""
    Style=""--cctc-button-icon-color: var(--cctc-warning-color);""
    ButtonIcon=""light_mode""
    ButtonText=""@ShortRefreshButtonText""
    IconPosition=""PositionX.Right""
    OnClick=""@(() => Console.WriteLine(""click""))"">
</RefreshButton>

@code {

    string ShortRefreshButtonText { get; set; } = ""some text"";
}",
    @<RefreshButton
        Id="usage2"
        Style="--cctc-button-icon-color: var(--cctc-warning-color);"
        ButtonIcon="light_mode"
        ButtonText="@ShortRefreshButtonText"
        IconPosition="PositionX.Right"
        OnClick="@(() => Console.WriteLine("click"))">
    </RefreshButton>),
        ("ButtonIcon: refresh, IconPosition: Left, ButtonText: long text to demonstrate wrapping, CssClass: restricted-width", "",
@"<RefreshButton
    Id=""usage3""
    CssClass=""restricted-width""
    ButtonIcon=""refresh""
    ButtonText=""@LongRefreshButtonText""
    IconPosition=""PositionX.Left""
    OnClick=""@(() => Console.WriteLine(""click""))"">
</RefreshButton>

@code {

    string LongRefreshButtonText { get; set; } = ""Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat."";
}",
    @<RefreshButton
        Id="usage3"
        CssClass="restricted-width"
        ButtonIcon="refresh"
        ButtonText="@LongRefreshButtonText"
        IconPosition="PositionX.Left"
        OnClick="@(() => Console.WriteLine("click"))">
    </RefreshButton>),
        ("ButtonIcon: refresh (default)", "",
@"<RefreshButton
    Id=""usage4""
    ButtonText=""@ShortRefreshButtonText""
    OnClick=""@(() => Console.WriteLine(""click""))"">
</RefreshButton>

@code {

    string ShortRefreshButtonText { get; set; } = ""some text"";
}",
    @<RefreshButton
        Id="usage4"
        ButtonText="@ShortRefreshButtonText"
        OnClick="@(() => Console.WriteLine("click"))">
    </RefreshButton>),
        ("ButtonIcon: refresh, ButtonText: no text", "",
@"<RefreshButton
    Id=""usage5""
    ButtonIcon=""refresh""
    OnClick=""@(() => Console.WriteLine(""click""))"">
</RefreshButton>

@code {

}",
    @<RefreshButton
        Id="usage5"
        ButtonIcon="refresh"
        OnClick="@(() => Console.WriteLine("click"))">
    </RefreshButton>),
        ("ButtonIcon: refresh, IconPosition: Left, Disabled", "",
@"<RefreshButton
    Id=""usage6""
    ButtonIcon=""refresh""
    ButtonText=""@ShortRefreshButtonText""
    IconPosition=""PositionX.Left""
    OnClick=""@(() => Console.WriteLine(""click""))""
    Disabled=""true"">
</RefreshButton>

@code {

    string ShortRefreshButtonText { get; set; } = ""some text"";
}",
    @<RefreshButton
        Id="usage6"
        ButtonIcon="refresh"
        ButtonText="@ShortRefreshButtonText"
        IconPosition="PositionX.Left"
        OnClick="@(() => Console.WriteLine("click"))"
        Disabled="true"
        ShowConfirmation="true">
    </RefreshButton>)
    };
}

@* The wrapper div is necessary here for ::deep isolated css rules to be applied to the RefreshButton component *@
<div>
    <Sampler
        ComponentName="RefreshButton"
        QualifiedComponentName="Buttons.RefreshButton"
        ComponentCssName="button"
        ComponentTypeName="refreshbutton"
        Description="@description"
        Features="@features"
        UsageText="Typical usages of the <code>RefreshButton</code> component are shown below"
        UsageCodeList="@usageCode"
        ContentHeightPixels="425">
        <ExampleTemplate>
            <RefreshButton
                Id="example1"
                ButtonIcon="refresh"
                ButtonText="some text"
                IconPosition="PositionX.Left"
                StyledWithBorder="true"
                ShowConfirmation="true"
                OnClick="@(() => _counter++)">
            </RefreshButton>
            <p class="mt-3">Counter: @_counter</p>
        </ExampleTemplate>
    </Sampler>
</div>

@code {

    int _counter;

    string ShortRefreshButtonText { get; set; } = "some text";

    string LongRefreshButtonText { get; set; } = "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.";
}