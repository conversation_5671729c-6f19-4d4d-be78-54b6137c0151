RESTRICTION: == net9.0
NUGET
  remote: https://api.nuget.org/v3/index.json
    AngleSharp (1.3)
    AngleSharp.Css (1.0.0-beta.154)
      AngleSharp (>= 1.0 < 2.0)
    AngleSharp.Diffing (1.0)
      AngleSharp (>= 1.1.2)
      AngleSharp.Css (>= 1.0.0-beta.144)
    Azure.Core (1.47)
      Microsoft.Bcl.AsyncInterfaces (>= 8.0)
      System.ClientModel (>= 1.5)
      System.Memory.Data (>= 8.0.1)
    Azure.Identity (1.14.2)
      Azure.Core (>= 1.46.1)
      Microsoft.Identity.Client (>= 4.73.1)
      Microsoft.Identity.Client.Extensions.Msal (>= 4.73.1)
      System.Memory (>= 4.5.5)
    BlazorComponentUtilities (1.8)
    Blazored.Modal (7.3.1)
      Microsoft.AspNetCore.Components (>= 8.0.2)
      Microsoft.AspNetCore.Components.Web (>= 8.0.2)
      Microsoft.Extensions.DependencyInjection.Abstractions (>= 8.0)
      Microsoft.JSInterop.WebAssembly (>= 8.0.2)
    BlazorPro.Spinkit (1.2)
      BlazorComponentUtilities (>= 1.6)
      Microsoft.AspNetCore.Components (>= 3.1.2)
      Microsoft.AspNetCore.Components.Web (>= 3.1.2)
    BouncyCastle.Cryptography (2.6.1)
    bunit (1.40)
      bunit.core (>= 1.40)
      bunit.web (>= 1.40)
    bunit.core (1.40)
      Microsoft.AspNetCore.Components (>= 9.0.5)
      Microsoft.Extensions.Logging (>= 9.0.5)
      Microsoft.Extensions.Logging.Abstractions (>= 9.0.5)
    bunit.web (1.40)
      AngleSharp (>= 1.2)
      AngleSharp.Css (>= 1.0.0-beta.154)
      AngleSharp.Diffing (>= 1.0)
      bunit.core (>= 1.40)
      Microsoft.AspNetCore.Components (>= 9.0.5)
      Microsoft.AspNetCore.Components.Authorization (>= 9.0.5)
      Microsoft.AspNetCore.Components.Web (>= 9.0.5)
      Microsoft.AspNetCore.Components.WebAssembly (>= 9.0.5)
      Microsoft.AspNetCore.Components.WebAssembly.Authentication (>= 9.0.5)
      Microsoft.Extensions.Caching.Memory (>= 9.0.5)
      Microsoft.Extensions.Localization.Abstractions (>= 9.0.5)
      Microsoft.Extensions.Logging (>= 9.0.5)
      Microsoft.Extensions.Logging.Abstractions (>= 9.0.5)
      System.Text.Json (>= 9.0.5)
    Castle.Core (5.2.1)
      System.Diagnostics.EventLog (>= 6.0)
    Dapper (2.1.66)
    Expecto (10.2.3)
      FSharp.Core (>= 7.0.200)
      Mono.Cecil (>= 0.11.4 < 1.0)
    FSharp.Core (9.0.300)
    FSharp.Data (6.6)
      FSharp.Core (>= 6.0.1)
      FSharp.Data.Csv.Core (>= 6.6)
      FSharp.Data.Html.Core (>= 6.6)
      FSharp.Data.Http (>= 6.6)
      FSharp.Data.Json.Core (>= 6.6)
      FSharp.Data.Runtime.Utilities (>= 6.6)
      FSharp.Data.WorldBank.Core (>= 6.6)
      FSharp.Data.Xml.Core (>= 6.6)
    FSharp.Data.Csv.Core (6.6)
      FSharp.Core (>= 6.0.1)
      FSharp.Data.Runtime.Utilities (>= 6.6)
    FSharp.Data.Html.Core (6.6)
      FSharp.Core (>= 6.0.1)
      FSharp.Data.Csv.Core (>= 6.6)
      FSharp.Data.Runtime.Utilities (>= 6.6)
    FSharp.Data.Http (6.6)
      FSharp.Core (>= 6.0.1)
    FSharp.Data.Json.Core (6.6)
      FSharp.Core (>= 6.0.1)
      FSharp.Data.Http (>= 6.6)
      FSharp.Data.Runtime.Utilities (>= 6.6)
    FSharp.Data.Runtime.Utilities (6.6)
      FSharp.Core (>= 6.0.1)
      FSharp.Data.Http (>= 6.6)
    FSharp.Data.WorldBank.Core (6.6)
      FSharp.Core (>= 6.0.1)
      FSharp.Data.Http (>= 6.6)
      FSharp.Data.Json.Core (>= 6.6)
      FSharp.Data.Runtime.Utilities (>= 6.6)
    FSharp.Data.Xml.Core (6.6)
      FSharp.Core (>= 6.0.1)
      FSharp.Data.Http (>= 6.6)
      FSharp.Data.Json.Core (>= 6.6)
      FSharp.Data.Runtime.Utilities (>= 6.6)
    FSharp.SystemTextJson (1.4.36)
      FSharp.Core (>= 4.7)
      System.Text.Json (>= 6.0.10)
    FsToolkit.ErrorHandling (5.0.1)
      FSharp.Core (>= 9.0.300)
    FsToolkit.ErrorHandling.TaskResult (4.18)
      FsToolkit.ErrorHandling (>= 4.18)
    MailKit (4.13)
      MimeKit (>= 4.13)
      System.Formats.Asn1 (>= 8.0.1)
    Microsoft.AspNetCore.Authorization (9.0.7)
      Microsoft.AspNetCore.Metadata (>= 9.0.7)
      Microsoft.Extensions.Logging.Abstractions (>= 9.0.7)
      Microsoft.Extensions.Options (>= 9.0.7)
    Microsoft.AspNetCore.Components (9.0.7)
      Microsoft.AspNetCore.Authorization (>= 9.0.7)
      Microsoft.AspNetCore.Components.Analyzers (>= 9.0.7)
    Microsoft.AspNetCore.Components.Analyzers (9.0.7)
    Microsoft.AspNetCore.Components.Authorization (9.0.7)
      Microsoft.AspNetCore.Authorization (>= 9.0.7)
      Microsoft.AspNetCore.Components (>= 9.0.7)
    Microsoft.AspNetCore.Components.Forms (9.0.7)
      Microsoft.AspNetCore.Components (>= 9.0.7)
    Microsoft.AspNetCore.Components.Web (9.0.7)
      Microsoft.AspNetCore.Components (>= 9.0.7)
      Microsoft.AspNetCore.Components.Forms (>= 9.0.7)
      Microsoft.Extensions.DependencyInjection (>= 9.0.7)
      Microsoft.Extensions.Primitives (>= 9.0.7)
      Microsoft.JSInterop (>= 9.0.7)
    Microsoft.AspNetCore.Components.WebAssembly (9.0.7)
      Microsoft.AspNetCore.Components.Web (>= 9.0.7)
      Microsoft.Extensions.Configuration.Binder (>= 9.0.7)
      Microsoft.Extensions.Configuration.Json (>= 9.0.7)
      Microsoft.Extensions.Logging (>= 9.0.7)
      Microsoft.JSInterop.WebAssembly (>= 9.0.7)
    Microsoft.AspNetCore.Components.WebAssembly.Authentication (9.0.7)
      Microsoft.AspNetCore.Components.Authorization (>= 9.0.7)
      Microsoft.AspNetCore.Components.Web (>= 9.0.7)
    Microsoft.AspNetCore.Components.WebAssembly.DevServer (9.0.7)
    Microsoft.AspNetCore.Metadata (9.0.7)
    Microsoft.Bcl.AsyncInterfaces (9.0.7)
    Microsoft.Bcl.Cryptography (9.0.7)
    Microsoft.CodeCoverage (17.14.1)
    Microsoft.Data.SqlClient (6.0.2)
      Azure.Identity (>= 1.11.4)
      Microsoft.Bcl.Cryptography (>= 9.0.4)
      Microsoft.Data.SqlClient.SNI.runtime (>= 6.0.2)
      Microsoft.Extensions.Caching.Memory (>= 9.0.4)
      Microsoft.IdentityModel.JsonWebTokens (>= 7.5)
      Microsoft.IdentityModel.Protocols.OpenIdConnect (>= 7.5)
      Microsoft.SqlServer.Server (>= 1.0)
      System.Configuration.ConfigurationManager (>= 9.0.4)
      System.Security.Cryptography.Pkcs (>= 9.0.4)
    Microsoft.Data.SqlClient.SNI.runtime (6.0.2)
    Microsoft.Extensions.Caching.Abstractions (9.0.7)
      Microsoft.Extensions.Primitives (>= 9.0.7)
    Microsoft.Extensions.Caching.Memory (9.0.7)
      Microsoft.Extensions.Caching.Abstractions (>= 9.0.7)
      Microsoft.Extensions.DependencyInjection.Abstractions (>= 9.0.7)
      Microsoft.Extensions.Logging.Abstractions (>= 9.0.7)
      Microsoft.Extensions.Options (>= 9.0.7)
      Microsoft.Extensions.Primitives (>= 9.0.7)
    Microsoft.Extensions.Configuration (9.0.7)
      Microsoft.Extensions.Configuration.Abstractions (>= 9.0.7)
      Microsoft.Extensions.Primitives (>= 9.0.7)
    Microsoft.Extensions.Configuration.Abstractions (9.0.7)
      Microsoft.Extensions.Primitives (>= 9.0.7)
    Microsoft.Extensions.Configuration.Binder (9.0.7)
      Microsoft.Extensions.Configuration.Abstractions (>= 9.0.7)
    Microsoft.Extensions.Configuration.FileExtensions (9.0.7)
      Microsoft.Extensions.Configuration (>= 9.0.7)
      Microsoft.Extensions.Configuration.Abstractions (>= 9.0.7)
      Microsoft.Extensions.FileProviders.Abstractions (>= 9.0.7)
      Microsoft.Extensions.FileProviders.Physical (>= 9.0.7)
      Microsoft.Extensions.Primitives (>= 9.0.7)
    Microsoft.Extensions.Configuration.Json (9.0.7)
      Microsoft.Extensions.Configuration (>= 9.0.7)
      Microsoft.Extensions.Configuration.Abstractions (>= 9.0.7)
      Microsoft.Extensions.Configuration.FileExtensions (>= 9.0.7)
      Microsoft.Extensions.FileProviders.Abstractions (>= 9.0.7)
    Microsoft.Extensions.DependencyInjection (9.0.7)
      Microsoft.Extensions.DependencyInjection.Abstractions (>= 9.0.7)
    Microsoft.Extensions.DependencyInjection.Abstractions (9.0.7)
    Microsoft.Extensions.FileProviders.Abstractions (9.0.7)
      Microsoft.Extensions.Primitives (>= 9.0.7)
    Microsoft.Extensions.FileProviders.Physical (9.0.7)
      Microsoft.Extensions.FileProviders.Abstractions (>= 9.0.7)
      Microsoft.Extensions.FileSystemGlobbing (>= 9.0.7)
      Microsoft.Extensions.Primitives (>= 9.0.7)
    Microsoft.Extensions.FileSystemGlobbing (9.0.7)
    Microsoft.Extensions.Localization.Abstractions (9.0.7)
    Microsoft.Extensions.Logging (9.0.7)
      Microsoft.Extensions.DependencyInjection (>= 9.0.7)
      Microsoft.Extensions.Logging.Abstractions (>= 9.0.7)
      Microsoft.Extensions.Options (>= 9.0.7)
    Microsoft.Extensions.Logging.Abstractions (9.0.7)
      Microsoft.Extensions.DependencyInjection.Abstractions (>= 9.0.7)
    Microsoft.Extensions.Options (9.0.7)
      Microsoft.Extensions.DependencyInjection.Abstractions (>= 9.0.7)
      Microsoft.Extensions.Primitives (>= 9.0.7)
    Microsoft.Extensions.Primitives (9.0.7)
    Microsoft.Identity.Client (4.74)
      Microsoft.IdentityModel.Abstractions (>= 6.35)
      System.Diagnostics.DiagnosticSource (>= 6.0.1)
    Microsoft.Identity.Client.Extensions.Msal (4.74)
      Microsoft.Identity.Client (>= 4.74)
      System.Security.Cryptography.ProtectedData (>= 4.5)
    Microsoft.IdentityModel.Abstractions (8.12.1)
    Microsoft.IdentityModel.JsonWebTokens (8.12.1)
      Microsoft.IdentityModel.Tokens (>= 8.12.1)
    Microsoft.IdentityModel.Logging (8.12.1)
      Microsoft.IdentityModel.Abstractions (>= 8.12.1)
    Microsoft.IdentityModel.Protocols (8.12.1)
      Microsoft.IdentityModel.Tokens (>= 8.12.1)
    Microsoft.IdentityModel.Protocols.OpenIdConnect (8.12.1)
      Microsoft.IdentityModel.Protocols (>= 8.12.1)
      System.IdentityModel.Tokens.Jwt (>= 8.12.1)
    Microsoft.IdentityModel.Tokens (8.12.1)
      Microsoft.Extensions.Logging.Abstractions (>= 8.0)
      Microsoft.IdentityModel.Logging (>= 8.12.1)
    Microsoft.JSInterop (9.0.7)
    Microsoft.JSInterop.WebAssembly (9.0.7)
      Microsoft.JSInterop (>= 9.0.7)
    Microsoft.NET.Test.Sdk (17.14.1)
      Microsoft.CodeCoverage (>= 17.14.1)
      Microsoft.TestPlatform.TestHost (>= 17.14.1)
    Microsoft.Reactive.Testing (6.0.1)
      System.Reactive (>= 6.0.1)
    Microsoft.SqlServer.Server (1.0)
    Microsoft.TestPlatform.ObjectModel (17.14.1)
      System.Reflection.Metadata (>= 8.0)
    Microsoft.TestPlatform.TestHost (17.14.1)
      Microsoft.TestPlatform.ObjectModel (>= 17.14.1)
      Newtonsoft.Json (>= 13.0.3)
    MimeKit (4.13)
      BouncyCastle.Cryptography (>= 2.5.1)
      System.Security.Cryptography.Pkcs (>= 8.0.1)
    Mono.Cecil (0.11.6)
    Moq (4.20.72)
      Castle.Core (>= 5.1.1)
    Neo4j.Driver (5.28.2)
      System.IO.Pipelines (>= 8.0)
    Newtonsoft.Json (13.0.3)
    NodaTime (3.2.2)
    NodaTime.Serialization.SystemTextJson (1.3)
      NodaTime (>= 3.0 < 4.0)
    System.ClientModel (1.5.1)
      Microsoft.Extensions.Logging.Abstractions (>= 8.0.3)
      System.Memory.Data (>= 8.0.1)
    System.Configuration.ConfigurationManager (9.0.7)
      System.Diagnostics.EventLog (>= 9.0.7)
      System.Security.Cryptography.ProtectedData (>= 9.0.7)
    System.Diagnostics.DiagnosticSource (9.0.7)
    System.Diagnostics.EventLog (9.0.7)
    System.Formats.Asn1 (9.0.7)
    System.IdentityModel.Tokens.Jwt (8.12.1)
      Microsoft.IdentityModel.JsonWebTokens (>= 8.12.1)
      Microsoft.IdentityModel.Tokens (>= 8.12.1)
    System.IO.Pipelines (9.0.7)
    System.Memory (4.6.3)
    System.Memory.Data (9.0.7)
    System.Reactive (6.0.1)
    System.Reflection.Metadata (9.0.7)
    System.Security.Cryptography.Pkcs (9.0.7)
    System.Security.Cryptography.ProtectedData (9.0.7)
    System.Text.Json (9.0.7)
    TextCopy (6.2.1)
      Microsoft.Extensions.DependencyInjection.Abstractions (>= 7.0)
    xunit (2.9.3)
      xunit.analyzers (>= 1.18)
      xunit.assert (>= 2.9.3)
      xunit.core (2.9.3)
    xunit.abstractions (2.0.3)
    xunit.analyzers (1.23)
    xunit.assert (2.9.3)
    xunit.core (2.9.3)
      xunit.extensibility.core (2.9.3)
      xunit.extensibility.execution (2.9.3)
    xunit.extensibility.core (2.9.3)
      xunit.abstractions (>= 2.0.3)
    xunit.extensibility.execution (2.9.3)
      xunit.extensibility.core (2.9.3)
    xunit.runner.visualstudio (3.1.2)
  remote: https://nuget.pkg.github.com/CCTC-team/index.json
    CCTC_Lib (1.0.14)
      System.Reactive (>= 6.0.1)
    Lib (1.0.44)
      Dapper (>= 2.1.66)
      Expecto (>= 10.2.3)
      FSharp.Core (>= 9.0.300)
      FSharp.Data (>= 6.6)
      FSharp.SystemTextJson (>= 1.4.36)
      FsToolkit.ErrorHandling (>= 5.0.1)
      FsToolkit.ErrorHandling.TaskResult (>= 4.18)
      MailKit (>= 4.13)
      Microsoft.Data.SqlClient (>= 6.0.2)
      Neo4j.Driver (>= 5.28.2)
      NodaTime (>= 3.2.2)
      NodaTime.Serialization.SystemTextJson (>= 1.3)
