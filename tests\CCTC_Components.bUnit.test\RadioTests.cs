﻿using AngleSharp.Html.Dom;
using CCTC_Components.Components.Radio;
using Microsoft.AspNetCore.Components;

namespace CCTC_Components.bUnit.test
{
    public class RadioTests : CCTCComponentsTestContext
    {
        [Fact]
        public void RadioInvokesEventCallbacksCorrectly()
        {
            AddAddTooltip();
            AddIsOverflowing(false);
            AddUpdateTooltipTitle();

            var mockDummyService = new Mock<IDummyService>();
            string? valueChangedNewValue = null;
            ChangeEventArgs? selectionChangedNewValue = null;
            string? expectedValue = "SecondOption";

            var cut = RenderComponent<Radio<string>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.Data, new List<string> { "FirstOption", "SecondOption", "ThirdOption" })
                .Add(p => p.ValueChanged, async args => { valueChangedNewValue = args; await mockDummyService.Object.MethodOneAsync(); })
                .Add(p => p.SelectionChanged, async args => { selectionChangedNewValue = args; await mockDummyService.Object.MethodTwoAsync(); })
                .Add(p => p.Value, "ThirdOption")
                .Add(p => p.ShowClear, true)
            );

            var inputElements = cut.Find("input");
            inputElements.Change(expectedValue);
            Assert.Equal(expectedValue, valueChangedNewValue);
            Assert.Equal(expectedValue, selectionChangedNewValue?.Value?.ToString());

            var showClearIcon = cut.Find(".icon-wrapper .material-icons.backspace");
            showClearIcon.Click();
            expectedValue = null;
            Assert.Equal(expectedValue, valueChangedNewValue);
            Assert.Equal(expectedValue, selectionChangedNewValue?.Value?.ToString());

            mockDummyService.Verify(m => m.MethodOneAsync(), Times.Exactly(2));
            mockDummyService.Verify(m => m.MethodTwoAsync(), Times.Exactly(2));
        }

        [Fact]
        public void RadioCheckedChangesCorrectly()
        {
            AddAddTooltip();
            AddIsOverflowing(false);
            AddUpdateTooltipTitle();

            var cut = RenderComponent<Radio<string>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.Data, new List<string> { "FirstOption", "SecondOption", "ThirdOption" })
                .Add(p => p.Value, "ThirdOption")
            );

            var inputElements = cut.FindAll("input");

            Assert.False(((IHtmlInputElement)inputElements[0]).IsChecked);
            Assert.False(((IHtmlInputElement)inputElements[1]).IsChecked);
            Assert.True(((IHtmlInputElement)inputElements[2]).IsChecked);

            inputElements[1].Change("SecondOption");
            var inputElementsAfterChange = cut.FindAll("input");

            Assert.False(((IHtmlInputElement)inputElementsAfterChange[0]).IsChecked);
            Assert.True(((IHtmlInputElement)inputElementsAfterChange[1]).IsChecked);
            Assert.False(((IHtmlInputElement)inputElementsAfterChange[2]).IsChecked);
        }

        [Fact]
        public void RadioOptionsConfiguredCorrectly()
        {
            AddAddTooltip();
            AddIsOverflowing(false);

            var cut = RenderComponent<Radio<(string value, string display)>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.Data, new List<(string value, string display)> { ("1", "One"), ("2", "Two"), ("3", "Three") })
                .Add(p => p.Value, ("1", "One"))
                .Add(p => p.ValueSelector, item => item.value)
                .Add(p => p.DisplaySelector, item => item.display)
                .Add(p => p.Visible, item => item.value != "3")
            );

            var inputElements = cut.FindAll("input");
            var firstInputElement = (IHtmlInputElement)inputElements[0];
            var secondInputElement = (IHtmlInputElement)inputElements[1];
            var labelElements = cut.FindAll("label");
            int expectedInputElementCount = 2;

            Assert.Equal(expectedInputElementCount, inputElements.Count);
            Assert.Equal("1", firstInputElement.Value);
            Assert.Equal("2", secondInputElement.Value);
            Assert.Equal("One", ((IHtmlLabelElement)labelElements[0]).InnerHtml);
            Assert.Equal("Two", ((IHtmlLabelElement)labelElements[1]).InnerHtml);
            Assert.True(firstInputElement.IsChecked);
            Assert.False(secondInputElement.IsChecked);
        }

        [Fact]
        public void RadioAttributesConfiguredCorrectly()
        {
            AddAddTooltip();
            AddIsOverflowing(false);

            var cut = RenderComponent<Radio<string>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.Data, new List<string> { "FirstOption", "SecondOption" })
                .Add(p => p.Value, "SecondOption")
                .Add(p => p.CssClass, "test-class")
                .Add(p => p.Style, "color: blue;")
                .Add(p => p.Disabled, item => item == "FirstOption")
                .Add(p => p.RadioOrientation, RadioOrientation.Horizontal)
            );

            var radioWrapperElement = cut.Find("cctc-input[data-cctc-input-type=\"radio\"]");

            var expectedRadioWrapperAttributes = new Dictionary<string, string?>()
            {
                { "id", "test-id" },
                { "class", "test-class" },
                { "style", "color: blue;" },
                { "data-author", "cctc" }
            };

            var actualRadioWrapperAttributes = new Dictionary<string, string?>()
            {
                { "id", radioWrapperElement.Id },
                { "class", radioWrapperElement.ClassName },
                { "style", radioWrapperElement.GetAttribute("style") },
                { "data-author", radioWrapperElement.GetAttribute("data-author") }
            };

            var inputElements = cut.FindAll("input");
            var firstInputElement = (IHtmlInputElement)inputElements[0];
            var secondInputElement = (IHtmlInputElement)inputElements[1];

            var expectedInputAttributes = new List<(string attribute, string? value)>()
            {
                ( "name", "test-id-radio-group" ),
                ( "id", "test-id-0" ),
                ( "value", "FirstOption" ),
                ( "name", "test-id-radio-group" ),
                ( "id", "test-id-1" ),
                ( "value", "SecondOption" )
            };

            var actualInputAttributes = new List<(string attribute, string? value)>()
            {
                ( "name", firstInputElement.Name ),
                ( "id", firstInputElement.Id ),
                ( "value", firstInputElement.Value ),
                ( "name", secondInputElement.Name ),
                ( "id", secondInputElement.Id ),
                ( "value", secondInputElement.Value )
            };

            var labelElement = cut.FindAll("label");
            var firstLabelElement = (IHtmlLabelElement)labelElement[0];
            var secondLabelElement = (IHtmlLabelElement)labelElement[1];

            var expectedLabelAttributes = new List<(string attribute, string? value)>()
            {
                ( "for", "test-id-0" ),
                ( "for", "test-id-1" ),
                ( "value", "FirstOption" ),
                ( "value", "SecondOption" )
            };

            var actualLabelAttributes = new List<(string attribute, string? value)>()
            {
                ( "for", firstLabelElement.HtmlFor ),
                ( "for", secondLabelElement.HtmlFor ),
                ( "value", firstLabelElement.InnerHtml ),
                ( "value", secondLabelElement.InnerHtml ),
            };

            Assert.Equal(expectedRadioWrapperAttributes, actualRadioWrapperAttributes);
            Assert.Equal(expectedInputAttributes, actualInputAttributes);
            Assert.True(firstInputElement.IsDisabled);
            Assert.False(secondInputElement.IsDisabled);
            Assert.Equal(expectedLabelAttributes, actualLabelAttributes);
        }

        [Fact]
        public void RadioOrientationConfiguredCorrectly()
        {
            AddAddTooltip();
            AddIsOverflowing(false);
            AddUpdateTooltipTitle();

            var cut = RenderComponent<Radio<string>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.Data, new List<string> { "FirstOption", "SecondOption" })
                .Add(p => p.Value, "SecondOption")
            );

            cut.GetElementAt("div", 1)?.ClassName?.MarkupMatches("radio-group-wrapper radio-group-column left-justify label-wrap");

            cut.SetParametersAndRender(parameters => parameters
                .Add(p => p.RadioOrientation, RadioOrientation.Horizontal)
            );

            cut.GetElementAt("div", 1)?.ClassName?.MarkupMatches("radio-group-wrapper radio-group-row label-wrap");

            cut.SetParametersAndRender(parameters => parameters
                .Add(p => p.RadioOrientation, RadioOrientation.VerticalLeft)
            );

            cut.GetElementAt("div", 1)?.ClassName?.MarkupMatches("radio-group-wrapper radio-group-column left-justify label-wrap");

            cut.SetParametersAndRender(parameters => parameters
                .Add(p => p.RadioOrientation, RadioOrientation.VerticalRight)
            );

            cut.GetElementAt("div", 1)?.ClassName?.MarkupMatches("radio-group-wrapper radio-group-column right-justify label-wrap");
        }

        [Fact]
        public void RadioLabelOverflowConfiguredCorrectly()
        {
            AddAddTooltip();
            AddIsOverflowing(false);
            AddUpdateTooltipTitle();

            var cut = RenderComponent<Radio<string>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.Data, new List<string> { "FirstOption", "SecondOption" })
                .Add(p => p.Value, "SecondOption")
            );

            cut.GetElementAt("div", 1)?.ClassName?.MarkupMatches("radio-group-wrapper radio-group-column left-justify label-wrap");

            cut.SetParametersAndRender(parameters => parameters
                .Add(p => p.RadioLabelOverflow, RadioLabelOverflow.NoWrap)
            );

            cut.GetElementAt("div", 1)?.ClassName?.MarkupMatches("radio-group-wrapper radio-group-column left-justify label-nowrap");

            cut.SetParametersAndRender(parameters => parameters
                .Add(p => p.RadioLabelOverflow, RadioLabelOverflow.Scroll)
            );

            cut.GetElementAt("div", 1)?.ClassName?.MarkupMatches("radio-group-wrapper radio-group-column left-justify label-scroll");

            cut.SetParametersAndRender(parameters => parameters
                .Add(p => p.RadioLabelOverflow, RadioLabelOverflow.None)
            );

            cut.GetElementAt("div", 1)?.ClassName?.MarkupMatches("radio-group-wrapper radio-group-column left-justify");
        }

        [Fact]
        public void RadioDisabledWhenAllOptionsDisabled()
        {
            AddAddTooltip();
            AddIsOverflowing(false);

            var cut = RenderComponent<Radio<string>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.Data, new List<string> { "FirstOption", "SecondOption" })
                .Add(p => p.Value, "SecondOption")
                .Add(p => p.Disabled, _ => true)
            );

            var radioGroupWrapperElement = cut.FindAll(".radio-group-wrapper.disabled");
            Assert.Equal(1, radioGroupWrapperElement.Count);

            var inputElements = cut.FindAll("input");
            Assert.True(((IHtmlInputElement)inputElements[0]).IsDisabled);
            Assert.True(((IHtmlInputElement)inputElements[1]).IsDisabled);
        }

        [Fact]
        public void RadioNotDisabledWhenAllOptionsNotDisabled()
        {
            AddAddTooltip();
            AddIsOverflowing(false);

            var cut = RenderComponent<Radio<string>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.Data, new List<string> { "FirstOption", "SecondOption" })
                .Add(p => p.Value, "SecondOption")
                .Add(p => p.Disabled, item => item == "FirstOption")
            );

            var radioGroupWrapperElement = cut.FindAll(".radio-group-wrapper.disabled");
            Assert.Equal(0, radioGroupWrapperElement.Count);

            var inputElements = cut.FindAll("input");
            Assert.True(((IHtmlInputElement)inputElements[0]).IsDisabled);
            Assert.False(((IHtmlInputElement)inputElements[1]).IsDisabled);
        }

        [Theory]
        [InlineData(true, true, 0)]
        [InlineData(true, false, 1)]
        [InlineData(false, false, 0)]
        public void RadioReadOnlyIconConfiguredCorrectly(bool readOnly, bool hideReadOnlyIcon, int expectedIconCount)
        {
            AddAddTooltip();
            AddIsOverflowing(false);

            var cut = RenderComponent<Radio<string>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.Data, new List<string> { "FirstOption", "SecondOption" })
                .Add(p => p.ReadOnly, readOnly)
                .Add(p => p.HideReadOnlyIcon, hideReadOnlyIcon)
            );

            var readOnlyIcon = cut.FindAll(".icon-wrapper .material-icons.lock");
            Assert.Equal(expectedIconCount, readOnlyIcon.Count);
        }

        [Fact]
        public void RadioOnClickPreventDefaultEnabled()
        {
            AddAddTooltip();
            AddIsOverflowing(false);

            var cut = RenderComponent<Radio<string>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.Data, new List<string> { "FirstOption", "SecondOption", "ThirdOption" })
                .Add(p => p.Value, "ThirdOption")
                .Add(p => p.ReadOnly, true)
            );

            var radioGroupWrapperElement = cut.Find(".radio-group-wrapper");
            string eventName = "blazor:onclick:preventdefault";
            Assert.Contains(eventName, radioGroupWrapperElement.OuterHtml);
        }

        [Fact]
        public void RadioOnClickPreventDefaultNotEnabled()
        {
            AddAddTooltip();
            AddIsOverflowing(false);

            var cut = RenderComponent<Radio<string>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.Data, new List<string> { "FirstOption", "SecondOption", "ThirdOption" })
                .Add(p => p.Value, "ThirdOption")
            );

            var radioGroupWrapperElement = cut.Find(".radio-group-wrapper");
            string eventName = "blazor:onclick:preventdefault";
            Assert.DoesNotContain(eventName, radioGroupWrapperElement.OuterHtml);
        }

        [Fact]
        public void ShowClearIconClassesCorrectlyConfigured()
        {
            AddAddTooltip();
            AddIsOverflowing(false);
            AddUpdateTooltipTitle();

            var mockDummyService = new Mock<IDummyService>();
            int? value = null;
            var cut = RenderComponent<Radio<int?>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.Data, new List<int?> { 1, 2 })
                .Add(p => p.Value, value)
                .Add(p => p.ValueChanged, args => value = args)
                .Add(p => p.ShowClear, true)
            );

            var clearIcon = cut.Find(".material-icons.backspace");
            clearIcon.ClassName!.MarkupMatches("material-icons backspace hide-clear-icon");

            value = 2;
            cut.SetParametersAndRender(parameters => parameters
                .Add(p => p.Value, value)
            );

            clearIcon.ClassName!.MarkupMatches("material-icons backspace show-clear-icon");

            clearIcon.Click();
            cut.SetParametersAndRender(parameters => parameters
               .Add(p => p.Value, value)
           );

            clearIcon.ClassName!.MarkupMatches("material-icons backspace hide-clear-icon");
        }

        [Fact]
        public void ShowClearWithNonNullableValueTypeThrowsArgumentException()
        {
            var cut = () => RenderComponent<Radio<int>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.Data, new List<int> { 1, 2 })
                .Add(p => p.Value, 2)
                .Add(p => p.ShowClear, true)
            );

            var actual = Assert.Throws<ArgumentException>(cut);
            string expectedParamName = "ShowClear";
            string expectedMessage = $"ShowClear should only be set to true when TData is a reference type or nullable value type (Parameter 'ShowClear')";
            Assert.Equal(expectedParamName, actual.ParamName);
            Assert.Equal(expectedMessage, actual.Message);
        }

        [Fact]
        public void ShowClearWithNullableValueTypeDoesNotThrowArgumentException()
        {
            AddAddTooltip();
            AddIsOverflowing(false);

            RenderComponent<Radio<int?>>(parameters => parameters
                .Add(p => p.Id, "test-id")
                .Add(p => p.Data, new List<int?> { 1, 2 })
                .Add(p => p.Value, 2)
                .Add(p => p.ShowClear, true)
            );
        }
    }
}
