﻿// https://fake.build/guide.html
// https://fake.build/guide/getting-started.html#Run-FAKE-using-a-dedicated-build-project
// https://github.com/TheAngryByrd/MiniScaffold/blob/master/build/build.fs

open System
open System.Text.Json
open System.Text.RegularExpressions
open Fake.Core
open Fake.DotNet
open Fake.Core.TargetOperators
open Fake.IO
open Fake.IO.Globbing.Operators
open Fake.Documentation
open Fake.Tools.Git
open FSharp.Data
open FSharp.Data.JsonExtensions
open Common.Helpers

(*
install az cli: https://learn.microsoft.com/en-us/cli/azure/install-azure-cli-windows?tabs=azure-cli
install swa cli globally: npm install -g @azure/static-web-apps-cli
install github cli: winget install --id GitHub.cli
create Azure resource group: cctc-components
create a github PAT token with repos, admin:org, read:project and write:packages scopes
store the above PAT token in GITHUBPASSWORD system environment variable
swa login
obtain tenant id from Azure portal (All Services > Microsoft Entra ID)
az login --tenant <TenantID>
in Azure portal in the cctc-components resource group create a Static Web App named components-ui. Deployment details source: Other
in Azure portal in the cctc-components resource group create a Static Web App named components-docs. Deployment details source: Other
ensure functional_scripts/src/config.ts has the BASE_URL set to the Static Web App preview URL (e.g. https://gray-tree-00d961c03-preview.5.azurestaticapps.net)
clone https://github.com/CCTC-team/gh-val.git
publish getval project to ../../gh-val/publish using dotnet publish -o <path> command
install docfx as a global tool: dotnet tool install -g docfx
install pandoc: winget install --source winget --exact --id JohnMacFarlane.Pandoc
dotnet run from build.fsproj location
*)

(*
    POTENTIAL ISSUES

    - az client - may be outdated. Run az upgrade to update it else it blocks with a user confirmation request
    - sws client - this may be outdated too


------------------------------------------------------------------------------------------------------------
    -- deployment of UI and docfx will use a single project without versioning so with every build
        it will update single swa for ui and single docfx and therefore only the latest build is
        available for viewing.

    -- to view previous versions, can use git to revert to a tag and then use swa to test UI in dev server by
        going to publish/wwwroot and running

        swa start

        which will run a test deployment locally. Note: must be in the publish app for it to work

    -- static web apps are viewable in preview and production flavours. The main uri is auto-generated (will
        be fixed when we buy a domain), and the versions can be viewed by a uri change e.g.
        production:     https://gray-tree-00d961c03.5.azurestaticapps.net/
        preview:        https://gray-tree-00d961c03-preview.westeurope.5.azurestaticapps.net/

    -- static web apps are given a standard env variable of ENV=production|preview
        this can be used to change the theme to make it clear whether the app is preview or prod

    -- check out playwright
        -- see https://www.google.com/search?q=playwright+testing+youyube&oq=playwright+testing+youyube&gs_lcrp=EgZjaHJvbWUyBggAEEUYOTIICAEQABgWGB4yBggCEEUYQNIBCTEyODcxajBqNKgCALACAA&sourceid=chrome&ie=UTF-8#fpstate=ive&vld=cid:48f7474d,vid:FvyYC2pxL_8,st:0
            for details about running in azure browser - quicker, and gives consistent experience = much better!
        -- does NOT run on webkit for mobile or normal i.e. safari https://github.com/microsoft/playwright/issues/2876

    can use
    build.cmd to run

    ----
    TODO:
        - workflow? - document process - use a wiki

------------------------------------------------------------------------------------------------------------
*)

//assumes a valid login to azure (i.e. az login)
//assumes there are valid github environment variables for user and password

let netVersion = "net9.0"
let dotnetConfig = DotNet.BuildConfiguration.Release
let resourceGroup = "cctc-components"
let uiWebAppName = "components-ui"
let docsWebAppName = "components-docs"
let checkBranch = "main"
let packLocation = "../pack"
let readmePath = "../README.md"
let getStartedPath = "../src/CCTC_Components_UI/Pages/GetStarted.razor"
let mainLayoutPath = "../src/CCTC_Components_UI/Shared/MainLayout.razor"
let indexHtmlPath = "../src/CCTC_Components_UI/wwwroot/index.html"
let packageJsonPath = "../src/CCTC_Components_UI/wwwroot/css/package.json"
let cctcTeamPackageSource = "https://nuget.pkg.github.com/CCTC-team/index.json"
//let baseUrl = "https://calm-pond-09746c803-preview.westeurope.5.azurestaticapps.net"
let baseUrl = "https://gray-tree-00d961c03-preview.5.azurestaticapps.net"

/// Defines a dependency - y is dependent on x. Finishes the chain.
let (==>!) x y =
    x ==> y
    |> ignore

/// Defines a soft dependency. x must run before y, if it is present, but y does not require x to be run. Finishes the chain.
let (?=>!) x y =
    x ?=> y
    |> ignore

type FindReplace =
    {
        pattern: string
        replacement: string
    }

let getArgValue tarParameter arg =
    tarParameter.Context.Arguments
    |> Seq.find(String.startsWith arg)
    |> String.replaceFirst arg ""
    |> String.trim

let replaceFileContent filePath findReplace =
    let newStringList =
        File.readAsString filePath
        |> String.split('\n')
        |> List.map(fun line ->         
            let replacement =
                findReplace
                |> List.tryPick(fun x ->
                    if line.Contains(x.pattern) then
                        Some x.replacement
                    else
                        None
                )
            match replacement with
            | Some x -> x
            | None -> line
        )
   
    let newString = String.Join("\n", newStringList)
    File.replaceContent filePath newString  

//runs functional tests in parallel and with retry on specified browser
let runFunctionalTests browserName =
    //Blazor wasm not running on webkit https://github.com/microsoft/playwright/issues/2876
    let procRes =
        CreateProcess.fromRawCommand
            "powershell.exe"
                [
                    "-ExecutionPolicy"; "Bypass";
                    "-Command";
                    $"cd ../functional_scripts; $env:BROWSER='{browserName}'; npm run test:parallel-retry"
                ]
                |> CreateProcess.redirectOutput
                |> Proc.run

    if procRes.ExitCode <> 0 then
        failwithf "Functional scripts using Gherkin and Playwright failed - check the functional_scripts/reports/report.html file for details"

//retrieves the static app deployment token using the name of the app
let getStaticAppKey appName =
    let procResult =
        CreateProcess.fromRawCommand
            "powershell.exe"
                [
                    "az staticwebapp"
                    "secrets"
                    "list"
                    $"--name {appName}"
                    "--query \"properties.apiKey\""
                ]
        |> CreateProcess.redirectOutput
        |> CreateProcess.ensureExitCode
        |> Proc.run

    procResult.Result.Output

//deploys the swa to the given location using the given key
let deploySite sitePath deployEnv token =
    CreateProcess.fromRawCommand
        "powershell.exe"
            [
                "-ExecutionPolicy"; "Bypass";
                "swa deploy"
                $"--env {deployEnv}"
                sitePath
                $"--deployment-token {token}"
            ]        
            |> CreateProcess.redirectOutput
            |> CreateProcess.ensureExitCode
            |> Proc.run
            |> ignore

//update the environment variables for the static app
let setSiteEnvVars appName deployEnv (keyValues : (string * string) list) =

    let checkKey (key : string) =
        key.ToCharArray()
        |> Array.iter(fun c -> if Char.IsLetterOrDigit(c) |> not then failwithf "Only letters and digits are valid keys - you used [%s]" key)

    let keyVals =
        keyValues
        |> List.map(fun (k,v) ->
            checkKey k
            $"{k}=\"{v}\"")
        |> String.joinStr " "

    CreateProcess.fromRawCommand
        "powershell.exe"
            [
                "az staticwebapp appsettings set"
                $"--name {appName}"
                $"--environment-name {deployEnv}"
                $"--setting-names {keyVals}"
            ]
            |> CreateProcess.redirectOutput
            |> CreateProcess.ensureExitCode
            |> Proc.run
            |> ignore

let mutable nextPackageVersion = ""

//gets the next version of the package using gh client
let getNextPackageVersion() =
    let procRes =
        CreateProcess.fromRawCommand
            "powershell.exe"
                [
                    "(Get-ChildItem -Path Env:GITHUBPASSWORD).Value | gh auth login --with-token;"
                    "gh api -H \"Accept: application/vnd.github+json\" -H \"X-GitHub-Api-Version: 2022-11-28\"  /orgs/CCTC-team/packages/nuget/CCTC_Components/versions"
                ]
                |> CreateProcess.redirectOutput
                |> CreateProcess.ensureExitCode
                |> Proc.run

    if procRes.ExitCode <> 0 then
        failwithf "The github command to get the versions of the CCTC_Components package failed with error %s" procRes.Result.Error

    //find the latest created version and use that name as the last version e.g. 1.0.9
    let latest =
        let data = procRes.Result.Output |> JsonValue.Parse
        data.AsArray()
        |> Array.maxBy(fun j -> j?("created_at").AsString())

    let lastVer = latest?("name").AsString()
    let parts = lastVer |> String.splitStr "."
    nextPackageVersion <- $"{parts[0]}.{parts[1]}.{(parts[2] |> Int32.Parse) + 1}"


let initTargets() =

    Target.create "Init" (fun _ ->
        Trace.trace "Starting the build with FAKE"
    )

    Target.create "Check user setup" (fun _ ->
        let responseText = "Press y|Y to confirm or any other key to cancel --> "
        let setupQuestions =
            [
                $"Functional scripts require 100%% scaling on your main monitor. Have you got this?\n{responseText}"
                $"Have you got the latest browser versions (see https://github.com/CCTC-team/CCTC_Components/wiki/Browsers)?\n{responseText}"
            ]

        for setupQuestion in setupQuestions do
            let response =
                UserInput.getUserInput setupQuestion

            if response.ToLower() <> "y" then
                failwith "Process was cancelled"
    )

    Target.create "Check az client is up to date" (fun _ ->
        let res =
            CreateProcess.fromRawCommand
                "powershell.exe" [ "az --version" ]
                |> CreateProcess.redirectOutput
                |> Proc.run
        if res.ExitCode <> 0 then failwith "couldn't get the az version"
        if res.Result.Output |> string |> String.contains "Your CLI is up-to-date" |> not then
            failwith "The 'az --version' command did not report the az client was up to date. Run 'az upgrade' before continuing."
    )

    Target.create "Get swa client version" (fun _ ->
        let res =
            CreateProcess.fromRawCommand
                //"powershell.exe" [ "swa --version" ]
                "cmd.exe" [ "/c"; "swa --version" ]
                |> CreateProcess.redirectOutput
                |> Proc.run
        if res.ExitCode <> 0 then failwith "couldn't get the swa version"

        let version = res.Result.Output |> string

        if Regex.IsMatch(version, @"^\d\.\d+\.\d+$") |> not then
            failwith "The 'swa --version' command did not return a valid version number. Make sure it is installed before continuing."
        else printfn "swa version is %s" version
    )

    Target.create "Pause 30 seconds" (fun _ ->
        CreateProcess.fromRawCommand
            "powershell.exe"
                [
                    "-ExecutionPolicy"; "Bypass";
                    "-Command";
                    "Start-Sleep 30"
                ]
                |> Proc.run
                |> ignore
    )

    Target.create "Check current branch" (fun _ ->

        let checkBranch repoPath =
            let branch = Information.getBranchName repoPath

            if branch <> checkBranch then
                failwith $"expecting the {checkBranch} branch to be current for repo {repoPath} but was {branch}"

        checkBranch ".."
        //TODO: reinstate this
        //checkBranch "../../CCTC_Components_functional_tests"
    )

    Target.create "Check functional test config" (fun _ ->
        let lines = File.read "../functional_scripts/src/support/config.ts"
        let uncommentedBaseUrlLines =
            lines
            |> Seq.filter(fun x -> x.Contains(baseUrl))
            |> Seq.filter(fun x -> x.Trim().StartsWith("//") |> not)

        if uncommentedBaseUrlLines |> Seq.length <> 1 then
            failwith $"expecting the base url to be {baseUrl}"
    )

    Target.create "Build CCTC_Components" (fun _ ->
        "../src/CCTC_Components/CCTC_Components.csproj"
        |> DotNet.build (fun opt -> { opt with Configuration = dotnetConfig })
    )

    Target.create "Test CCTC_Components" (fun _ ->
        !! "../tests/**/*.*proj"
        |> Seq.iter (DotNet.test (fun opt ->
            { opt with
                NoRestore = true
                }))
    )

    Target.create "Functional tests with Gherkin and Playwright (Chromium)" (fun _ ->
        runFunctionalTests "chromium"
    )

    Target.create "Functional tests with Gherkin and Playwright (Chrome)" (fun _ ->
        runFunctionalTests "chrome"
    )

    Target.create "Functional tests with Gherkin and Playwright (Edge)" (fun _ ->
        runFunctionalTests "msedge"
    )

    Target.create "Functional tests with Gherkin and Playwright (Firefox)" (fun _ ->
        runFunctionalTests "firefox"
    )

    Target.create "Run metadata docfx" (fun _ ->
        DocFx.exec
            (id)
            "metadata ../docs/docfx/docfx.json" " --log logs/docfx_metadatalog.json" |> ignore
    )

    Target.create "Run build docfx" (fun _ ->
        DocFx.exec
            (id)
            "build ../docs/docfx/docfx.json" " --log logs/docfx_buildlog.json" |> ignore
    )

    Target.create "Update CCTC_Components package version references" (fun p ->
        let nextVer = getArgValue p "--nextVer"
        let packageVersionRefPattern = "dotnet add package CCTC_Components --version "
        let fourSpaces = "    "
        replaceFileContent readmePath
            [
                {
                    pattern = packageVersionRefPattern
                    replacement = $"{fourSpaces}`dotnet add package CCTC_Components --version {nextVer}`"
                }
            ]
        replaceFileContent getStartedPath
            [
                {   
                    pattern = packageVersionRefPattern
                    replacement = $"{fourSpaces}{fourSpaces}{fourSpaces}<code>dotnet add package CCTC_Components --version {nextVer}</code>"
                }
            ]
        replaceFileContent mainLayoutPath
            [
                {   pattern = """<small class="component-package-version">"""
                    replacement = $"""{fourSpaces}{fourSpaces}{fourSpaces}{fourSpaces}<small class="component-package-version">v.{nextVer}</small>"""
                }
            ]
    )

    Target.create "Update dependency version references" (fun _ ->
        let indexHtmlText = File.readAsString indexHtmlPath
        let packageJsonText = File.readAsString packageJsonPath
        let jQueryVersionPattern = "jquery-(.+)\.min.js"
        let chartJsVersionPattern = "chart\.js@(.+)\/dist\/chart\.umd\.min\.js"
        let bootstrapVersionNumber =
            JsonDocument.Parse(packageJsonText).RootElement.GetProperty("dependencies").GetProperty("bootstrap").ToString().Trim('^')
        let jQueryVersionNumber = (Regex.Match(indexHtmlText, jQueryVersionPattern)).Groups[1]
        let chartJsVersionNumber = (Regex.Match(indexHtmlText, chartJsVersionPattern)).Groups[1]

        let fourSpaces = "    "
        let twelveSpaces = $"{fourSpaces}{fourSpaces}{fourSpaces}"
        let bootstrapCssPattern = "/dist/css/bootstrap.min.css"
        let bootstrapJsPattern = "/dist/js/bootstrap.bundle.min.js"
        let jQueryPattern = "https://code.jquery.com/jquery-"
        let chartJsPattern = "/dist/chart.umd.min.js"

        replaceFileContent readmePath
            [
                {
                    pattern = bootstrapCssPattern
                    replacement = $"{fourSpaces}`https://cdn.jsdelivr.net/npm/bootstrap@{bootstrapVersionNumber}/dist/css/bootstrap.min.css`"
                }
                {
                    pattern = bootstrapJsPattern
                    replacement = $"{fourSpaces}`https://cdn.jsdelivr.net/npm/bootstrap@{bootstrapVersionNumber}/dist/js/bootstrap.bundle.min.js`"
                }
                {
                    pattern = jQueryPattern
                    replacement = $"{fourSpaces}`https://code.jquery.com/jquery-{jQueryVersionNumber}.min.js`"
                }
                {
                    pattern = chartJsPattern
                    replacement = $"{fourSpaces}`https://cdn.jsdelivr.net/npm/chart.js@{chartJsVersionNumber}/dist/chart.umd.min.js`"
                }
            ]
        replaceFileContent getStartedPath
            [
                {
                    pattern = bootstrapCssPattern
                    replacement = $"{twelveSpaces}<code>https://cdn.jsdelivr.net/npm/bootstrap@@{bootstrapVersionNumber}/dist/css/bootstrap.min.css</code>"
                }
                {
                    pattern = bootstrapJsPattern
                    replacement = $"{twelveSpaces}<code>https://cdn.jsdelivr.net/npm/bootstrap@@{bootstrapVersionNumber}/dist/js/bootstrap.bundle.min.js</code>"
                }
                {
                    pattern = jQueryPattern
                    replacement = $"{twelveSpaces}<code>https://code.jquery.com/jquery-{jQueryVersionNumber}.min.js</code>"
                }
                {
                    pattern = chartJsPattern
                    replacement = $"{twelveSpaces}<code>https://cdn.jsdelivr.net/npm/chart.js@@{chartJsVersionNumber}/dist/chart.umd.min.js</code>"
                }
            ]
        
        ()
    )

    //note: the UI has a project reference to the component project and does not use the package
    Target.create "Clean CCTC_Components_UI" (fun _ ->
        !! "../src/CCTC_Components_UI/bin"
        ++ "../src/CCTC_Components_UI/obj"
        |> Shell.cleanDirs
    )
    
    Target.create "Build CCTC_Components_UI" (fun _ ->
        "../src/CCTC_Components_UI/CCTC_Components_UI.csproj"
        |> DotNet.build (fun opt -> { opt with Configuration = dotnetConfig })
    )

    Target.create "Publish CCTC_Components_UI" (fun _ ->
        "../src/CCTC_Components_UI/CCTC_Components_UI.csproj"
        |> DotNet.publish (fun opt -> { opt with Configuration = dotnetConfig })
    )

    Target.create "Deploy UI as static web app - preview" (fun _ ->
        getStaticAppKey uiWebAppName
        |> deploySite
            $"../src/CCTC_Components_UI/bin/Release/{netVersion}/publish/wwwroot"
            "preview"

        setSiteEnvVars uiWebAppName "preview" [ "ENV", "preview" ]
    )

    Target.create "Deploy Docs as static web app - preview" (fun _ ->
        getStaticAppKey docsWebAppName
        |> deploySite
            "../docs/docfx/_site"
            "preview"

        setSiteEnvVars docsWebAppName "preview" [ "ENV", "preview" ]
    )

    Target.create "Deploy UI as static web app - production" (fun _ ->
        getStaticAppKey uiWebAppName
        |> deploySite
            $"../src/CCTC_Components_UI/bin/Release/{netVersion}/publish/wwwroot"
            "Production"

        //this fails with Not Found error. Seems to be a bug
        //setSiteEnvVars uiWebAppName "Production" [ "ENV", "production" ]
    )

    Target.create "Deploy Docs as static web app - production" (fun _ ->
        getStaticAppKey docsWebAppName
        |> deploySite
            "../docs/docfx/_site"
            "production"

        //this fails with Not Found error. Seems to be a bug
        //setSiteEnvVars docsWebAppName "production" [ "ENV", "production" ]
    )

    Target.create "Pack CCTC_Components" (fun p ->

        let nextVer = getArgValue p "--nextVer"

        //use the nextVer to pack the assembly
        "../src/CCTC_Components/CCTC_Components.csproj"
            |> DotNet.pack (fun opt ->
                { opt with
                    Configuration = dotnetConfig
                    NoBuild = true
                    OutputPath = Some packLocation
                    MSBuildParams =
                        { MSBuild.CliArguments.Create() with
                            Properties = [ "PackageVersion", nextVer ]
                        }

                }
            )
    )

    Target.create "Push CCTC_Components package" (fun p ->

        let nextVer = getArgValue p "--nextVer"

        CreateProcess.fromRawCommand
            "powershell.exe"
                [
                    "-ExecutionPolicy"; "Bypass";
                    $"dotnet nuget push {packLocation}/CCTC_Components.{nextVer}.nupkg  --source {cctcTeamPackageSource} --api-key (Get-ChildItem -Path Env:GITHUBPASSWORD).Value"
                ]
                |> CreateProcess.redirectOutput
                |> CreateProcess.ensureExitCode
                |> Proc.run
                |> ignore
    )

    Target.create "Tag version in git" (fun p ->

        let nextVer = getArgValue p "--nextVer"
        CommandHelper.directRunGitCommandAndFail ".." $"tag v{nextVer}"
        CommandHelper.directRunGitCommandAndFail ".." $"push --tags"
    )

    Target.create "Build validation docs" (fun _ ->

        CreateProcess.fromRawCommand
            "powershell.exe"
                [
                    "-ExecutionPolicy"; "Bypass";
                    "-Command";
                    $"../../gh-val/publish/getval.exe --config config/cctc_components_config.json --version {nextPackageVersion}"
                ]
                |> CreateProcess.redirectOutput
                |> CreateProcess.ensureExitCode
                |> Proc.run
                |> ignore
    )

    Target.create "Replace docfx net version with current" (fun _ ->
        let docfxFile = IO.File.ReadAllText("../docs/docfx/docfx.json")
        let updateFile = Regex.Replace(docfxFile, "net\d\d?.0", netVersion)
        IO.File.WriteAllText("../docs/docfx/docfx.json", updateFile)
    )

    "Init"
        ==> "Check user setup"
        ==> "Check az client is up to date"
        ==> "Get swa client version"
        ==> "Replace docfx net version with current"
        ==> "Check current branch"
        ==> "Check functional test config"
        ==> "Build CCTC_Components"
        ==> "Test CCTC_Components"
        ==> "Run metadata docfx"
        ==> "Run build docfx"
        ==> "Update CCTC_Components package version references"
        ==> "Update dependency version references"
        ==> "Clean CCTC_Components_UI"
        ==> "Build CCTC_Components_UI"
        ==> "Publish CCTC_Components_UI"
        ==> "Deploy UI as static web app - preview"
        
        //TODO: penetration testing
        
        ==> "Pause 30 seconds"
        ==> "Functional tests with Gherkin and Playwright (Chromium)"
        ==> "Functional tests with Gherkin and Playwright (Chrome)"
        ==> "Functional tests with Gherkin and Playwright (Edge)"
        ==> "Functional tests with Gherkin and Playwright (Firefox)"
        ==> "Deploy Docs as static web app - preview"
        ==> "Pack CCTC_Components"
        ==> "Push CCTC_Components package"
        ==> "Tag version in git"
        ==> "Build validation docs"
        ==> "Deploy UI as static web app - production"
        ==>! "Deploy Docs as static web app - production"

        //TODO: add milestone to every project card
        //Not sure if there is really any advantage to creating milestones

//-----------------------------------------------------------------------------
// Target Start
//-----------------------------------------------------------------------------

[<EntryPoint>]
let main argv =

    getNextPackageVersion()

    //add any arguments needed for targets that are created automatically
    let args =
        $"--nextVer {nextPackageVersion}"::
            (argv |> Array.toList)

    args
    |> Context.FakeExecutionContext.Create false "build.fsx"
    |> Context.RuntimeContext.Fake
    |> Context.setExecutionContext

    initTargets()

    if checkBranch <> "main" then
        printfn "WARNING - expecting the main branch to be used"

    try
        Target.runOrDefaultWithArguments "Deploy Docs as static web app - production"
        0
    with
    | _ ->
        printfn "***** The build FAILED *****"

        -1