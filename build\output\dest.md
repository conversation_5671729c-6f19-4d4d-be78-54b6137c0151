<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>
<body>

<style>

    * {
        font-family: monospace, Courier;
    }

    table {
        border-collapse: collapse;
    }

    th, td {
        /* border: dashed grey 1px; */
        padding: 0.5rem 1rem;
    }

    .feature-body > *, .js-file-line {
        font-size: 12px;
    }

    .spec-script > * {
        font-size: 14px;        
        font-family: Arial, Helvetica, sans-serif;
    }

    .comments {
        font-size: 12px;
    }

    .comment {
        padding-bottom: 0.2rem;
    }

    .comment-edit {
        font-size: 11px;        
    }

    .timeline {
        font-size: 11px;
    }

    .index-good {
        font-size: 12px;
    }

    .index-errors {
        font-size: 11px;
    }

    .sub-header {
        font-size: 10px;
    }

</style>

## CCTC_Components validation for 1.0.66

<div class="sub-header">
Generated on 15/07/2025 20:02:02 from github project id 15 by rmh54
</div>

<div class="sub-header">
* Note that currently the Project related events are not being picked up see
    See https://github.com/orgs/community/discussions/57326
    The audit trail related to moving columns within a project are not being pulled by graphql
</div>

### Index

<div class="index-good">
<a id=index-CCTC_Components-33 href=#CCTC_Components-33>CCTC_Components-33 | Date and time component</a>

<a id=index-CCTC_Components-27 href=#CCTC_Components-27>CCTC_Components-27 | Text component</a>

<a id=index-CCTC_Components-28 href=#CCTC_Components-28>CCTC_Components-28 | Text area component</a>

<a id=index-CCTC_Components-25 href=#CCTC_Components-25>CCTC_Components-25 | Dropdown component</a>

<a id=index-CCTC_Components-37 href=#CCTC_Components-37>CCTC_Components-37 | Numeric component</a>

<a id=index-CCTC_Components-36 href=#CCTC_Components-36>CCTC_Components-36 | Radio component</a>

<a id=index-CCTC_Components-26 href=#CCTC_Components-26>CCTC_Components-26 | Date component</a>

<a id=index-CCTC_Components-32 href=#CCTC_Components-32>CCTC_Components-32 | Time component</a>
</div>


<div class="index-errors">
#### The following features cannot be retrieved (uid | title -> error)

CCTC_Components-43 | Button

error -> Could not get Ok as Result contains an error: expecting all the urls for user specs to contain branch dp_dev

CCTC_Components-44 | Refresh Button

error -> Could not get Ok as Result contains an error: expecting all the urls for user specs to contain branch dp_dev

CCTC_Components-30 | Switch Component

error -> Could not get Ok as Result contains an error: expecting all the urls for user specs to contain branch dp_dev

CCTC_Components-66 | Tabs

error -> Could not get Ok as Result contains an error: expecting all the urls for user specs to contain branch dp_dev

CCTC_Components-70 | [VAL FEATURE]

error -> Could not get Ok as Result contains an error: expecting all the urls for user specs to contain branch dp_dev

CCTC_Components-71 | Lister

error -> Could not get Ok as Result contains an error: expecting all the urls for user specs to contain branch dp_dev

CCTC_Components-75 | Panelmenu

error -> Could not get Ok as Result contains an error: expecting all the urls for user specs to contain branch dp_dev

CCTC_Components-53 | Steps

error -> Could not get Ok as Result contains an error: expecting all the urls for user specs to contain branch dp_dev

CCTC_Components-42 | Pill

error -> Could not get Ok as Result contains an error: expecting all the urls for user specs to contain branch dp_dev

CCTC_Components-40 | Confirm_Modal

error -> Could not get Ok as Result contains an error: expecting all the urls for user specs to contain branch dp_dev

CCTC_Components-35 | Checkbox Component

error -> Could not get Ok as Result contains an error: expecting all the urls for user specs to contain branch dp_dev

CCTC_Components-22 | Concertina component

error -> Could not get Ok as Result contains an error: expecting all the urls for user specs to contain branch dp_dev
</div>

<br/>

### Features


***

<div id="CCTC_Components-33">**Feature:** Date and time component</div>

***

----------------------------------------------------------------------------------------------------------------------- -------------------------------------------------------------------------------
**Id:** I_kwDOKq1Kuc6OPDCf                                                                                              **Uid:** CCTC_Components-33                                                    
                                                                                                                                                                                                        
**Author:** phillidgithub                                                                                               **Created:** 02/07/2024 13:44:39                                               
                                                                                                                                                                                                        
**Assignees:** phillidgithub                                                                                            **Resource path:** <a href=https://github.com//CCTC-team/CCTC_Components/issues/33 target=_blank>/CCTC-team/CCTC_Components/issues/33</a>
                                                                                                                                                                                                        
**Milestone:** |none|                                                                                                   **Labels:** Validation and Verification || v1.0.0                              
                                                                                                                                                                                                        
----------------------------------------------------------------------------------------------------------------------- -------------------------------------------------------------------------------

***

----------------------------------------------------------------------------------------------------------------------- -------------------------------------------------------------------------------
**State:** OPEN                                                                                                         **State reason**: |not applicable|                                             
                                                                                                                                                                                                        
**Includes created edit:** true                                                                                         
                                                                                                                                                                                                        
**Closed:** false                                                                                                       **Closed on:** |none|                                                          
                                                                                                                                                                                                        
**Editor:** phillidgithub                                                                                               **Updated on:** 11/07/2024 07:44:12                                            
                                                                                                                                                                                                        
**Locked:** false                                                                                                       **Participants:** phillidgithub                                                
                                                                                                                                                                                                        
----------------------------------------------------------------------------------------------------------------------- -------------------------------------------------------------------------------

***

**Project item body:** \
<div class="feature-body">
<p dir="auto">Brief description:<br>
The date and time component is used to input a date and time<br>
NOTE: the branch of dp_dev is used but should be main</p>
<p dir="auto">User specs:<br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/user_specs/temporal/dateandtime.spec">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/user_specs/temporal/dateandtime.spec</a></p>
<p dir="auto">Functional scripts:<br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/dateandtime/dateandtime_1.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/dateandtime/dateandtime_1.feature</a><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/dateandtime/dateandtime_2.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/dateandtime/dateandtime_2.feature</a><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/dateandtime/dateandtime_3.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/dateandtime/dateandtime_3.feature</a><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/dateandtime/dateandtime_4.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/dateandtime/dateandtime_4.feature</a><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/dateandtime/dateandtime_5.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/dateandtime/dateandtime_5.feature</a></p>
<p dir="auto">Test files:<br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/tests/CCTC_Components.bUnit.test/DateAndTimeTests.cs">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/tests/CCTC_Components.bUnit.test/DateAndTimeTests.cs</a></p>
<p dir="auto"><strong>Pre Review:</strong></p>
<ul class="contains-task-list">
<li class="task-list-item"><input type="checkbox" id="" disabled="" class="task-list-item-checkbox" aria-label="Incomplete task"> Script(s) target the correct feature sufficiently</li>
<li class="task-list-item"><input type="checkbox" id="" disabled="" class="task-list-item-checkbox" aria-label="Incomplete task"> The user requirements are met</li>
</ul>
<p dir="auto"><strong>Post Review:</strong></p>
<ul class="contains-task-list">
<li class="task-list-item"><input type="checkbox" id="" disabled="" class="task-list-item-checkbox" aria-label="Incomplete task"> Script(s) target the correct feature sufficiently</li>
<li class="task-list-item"><input type="checkbox" id="" disabled="" class="task-list-item-checkbox" aria-label="Incomplete task"> Script(s) appropriately test the feature</li>
</ul>
</div>

***

**User specification:** \

<div class="spec-script">
| 
| _https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/user_specs/temporal/dateandtime.spec_
| 
| 1 - the date and time component date can be typed in as text or can be entered via a date picker. Time can be typed in as text
| 2 - the date and time component can allow an empty value and an entered value can be optionally cleared
| 3 - the date and time component date and time are validated (date / time format, min and / or max date / time) and there is feedback provided to the user via an icon
| 4 - the date and time component can be made read-only and / or disabled. The read-only icon is optional
| 5 - the date and time component has placeholders which match the date and time formats
</div>

**Functional scripts:** \

<div class="spec-script">

_https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/dateandtime/dateandtime_1.feature_

@component @temporal @dateandtime @dateandtime_1
Feature: the date and time component date can be typed in as text or can be entered via a date picker. Time can be typed in as text
    Scenario: the date and time component sample page is available
        Given the user is at the home page
        When the user selects the "Date and time" component in the container "Input"
        Then the url ending is "dateandtimesample"

    Scenario: the date and time component date and time can be typed in as text
        Given the user is at the home page
        And the user selects the "Date and time" component in the container "Input"
        When the user enters "06November2023" into the Date part of the Date and Time component
        And the user enters "1214am" into the Time part of the Date and Time component
        Then the Date part of the Date and Time component has the value "06 November 2023"
        And the Time part of the Date and Time component has the value "12:14 am"

    Scenario: the date and time component date can be entered via a date picker
        Given the user is at the home page
        And the user selects the "Date and time" component in the container "Input"
        When the user enters "2023-11-06" into the Date part of the Date and Time component via the date picker
        Then the Date part of the Date and Time component has the value "06 November 2023"


_https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/dateandtime/dateandtime_2.feature_

@component @temporal @dateandtime @dateandtime_2
Feature: the date and time component can allow an empty value and an entered value can be optionally cleared
    Scenario: the date and time component can allow an empty date and time value when allow clear is set to true
        Given the user is at the home page
        And the user selects the "Date and time" component in the container "Input"
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "Format: default, allow clear, null initial value, FeedbackIcon.Error"
        Then the Date part of the Date and Time component has the value ""
        And the Time part of the Date and Time component has the value ""

    Scenario: the date and time component date and time value can be cleared when allow clear is set to true
        Given the user is at the home page
        And the user selects the "Date and time" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: default, allow clear, FeedbackIcon.Error"
        When the user enters "" into the Date part of the Date and Time component
        And the user enters "" into the Time part of the Date and Time component
        Then the Date part of the Date and Time component has the value ""
        And the Time part of the Date and Time component has the value ""

    Scenario: the date and time component date value can be cleared via the date picker when allow clear is set to true
        Given the user is at the home page
        And the user selects the "Date and time" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: default, allow clear, FeedbackIcon.Error"
        When the user enters "" into the Date part of the Date and Time component via the date picker
        Then the Date part of the Date and Time component has the value ""

    Scenario: the date and time component can allow an empty date and time value when allow clear is set to false
        Given the user is at the home page
        And the user selects the "Date and time" component in the container "Input"
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "Format: default, null initial value, min (2022-01-05 14:33:20) and max (2023-01-05 14:33:22), FeedbackIcon.Both"
        Then the Date part of the Date and Time component has the value ""
        And the Time part of the Date and Time component has the value ""

    Scenario: the date and time component date and time value can not be cleared when allow clear is set to false
        Given the user is at the home page
        And the user selects the "Date and time" component in the container "Input"
        When the user enters "" into the Date part of the Date and Time component
        And the user enters "" into the Time part of the Date and Time component
        Then the Date part of the Date and Time component has the value "04 October 2022"
        And the Time part of the Date and Time component has the value "12:13 pm"


_https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/dateandtime/dateandtime_3.feature_

@component @temporal @dateandtime @dateandtime_3
Feature: the date and time component date and time are validated (date / time format, min and / or max date / time) and there is feedback provided to the user via an icon
    Scenario: an incomplete date and time is shown as an error
        Given the user is at the home page
        And the user selects the "Date and time" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: default, allow clear, FeedbackIcon.Error"
        When the user enters "2021010" into the Date part of the Date and Time component
        And the user enters "11121" into the Time part of the Date and Time component
        Then the Date part of the Date and Time component has the value "2021-01-0"
        And the Time part of the Date and Time component has the value "11:12:1"
        And the Date part of the Date and Time component displays a red exclamation mark feedback icon
        And the Time part of the Date and Time component displays a red exclamation mark feedback icon
        And the Date and Time component image matches the base image "dateandtime-error"

    Scenario: a complete date and time is shown as valid
        Given the user is at the home page
        And the user selects the "Date and time" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: default, FeedbackIcon.Valid"
        When the user enters "20210105" into the Date part of the Date and Time component
        And the user enters "111215" into the Time part of the Date and Time component
        Then the Date part of the Date and Time component has the value "2021-01-05"
        And the Time part of the Date and Time component has the value "11:12:15"
        And the Date part of the Date and Time component displays a green tick feedback icon
        And the Time part of the Date and Time component displays a green tick feedback icon
        And the Date and Time component image matches the base image "dateandtime-valid"

    Scenario: a date earlier than the minimum date is shown as an error
        Given the user is at the home page
        And the user selects the "Date and time" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: default, null initial value, min (2022-01-05 14:33:20) and max (2023-01-05 14:33:22), FeedbackIcon.Both"
        When the user enters "143320" into the Time part of the Date and Time component
        And the user enters "20220104" into the Date part of the Date and Time component
        Then the Time part of the Date and Time component has the value "14:33:20"
        And the Date part of the Date and Time component has the value "2022-01-04"
        And the Time part of the Date and Time component displays a green tick feedback icon
        And the Date part of the Date and Time component displays a red exclamation mark feedback icon

    Scenario: a date later than the maximum date is shown as an error
        Given the user is at the home page
        And the user selects the "Date and time" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: default, null initial value, min (2022-01-05 14:33:20) and max (2023-01-05 14:33:22), FeedbackIcon.Both"
        When the user enters "143320" into the Time part of the Date and Time component
        And the user enters "20230106" into the Date part of the Date and Time component
        Then the Time part of the Date and Time component has the value "14:33:20"
        And the Date part of the Date and Time component has the value "2023-01-06"
        And the Time part of the Date and Time component displays a green tick feedback icon
        And the Date part of the Date and Time component displays a red exclamation mark feedback icon

    Scenario: a time earlier than the minimum time is shown as an error
        Given the user is at the home page
        And the user selects the "Date and time" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: default, null initial value, min (2022-01-05 14:33:20) and max (2023-01-05 14:33:22), FeedbackIcon.Both"
        When the user enters "20220105" into the Date part of the Date and Time component
        And the user enters "143319" into the Time part of the Date and Time component
        Then the Date part of the Date and Time component has the value "2022-01-05"
        And the Time part of the Date and Time component has the value "14:33:19"
        And the Date part of the Date and Time component displays a green tick feedback icon
        And the Time part of the Date and Time component displays a red exclamation mark feedback icon

    Scenario: a time later than the maximum time is shown as an error
        Given the user is at the home page
        And the user selects the "Date and time" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: default, null initial value, min (2022-01-05 14:33:20) and max (2023-01-05 14:33:22), FeedbackIcon.Both"
        When the user enters "20220105" into the Date part of the Date and Time component
        And the user enters "143323" into the Time part of the Date and Time component
        Then the Date part of the Date and Time component has the value "2022-01-05"
        And the Time part of the Date and Time component has the value "14:33:23"
        And the Date part of the Date and Time component displays a green tick feedback icon
        And the Time part of the Date and Time component displays a red exclamation mark feedback icon


_https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/dateandtime/dateandtime_4.feature_

@component @temporal @dateandtime @dateandtime_4
Feature: the date and time component can be made read-only and / or disabled. The read-only icon is optional
    Scenario: the date and time component can be made read-only
        Given the user is at the home page
        And the user selects the "Date and time" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: dd MMMM yyyy h:mm tt, FeedbackIcon: default, read-only"
        Then the Date part of the Date and Time component is not editable
        And the Time part of the Date and Time component is not editable
        And the Date and Time component image matches the base image "dateandtime-readonly"

    Scenario: the date and time component can be disabled
        Given the user is at the home page
        And the user selects the "Date and time" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: dd MMMM yyyy h:mm tt, FeedbackIcon: default, disabled"
        Then the Date part of the Date and Time component is disabled
        And the Time part of the Date and Time component is disabled
        And the Date and Time component image matches the base image "dateandtime-disabled"

    Scenario: the date and time component can be made read-only and disabled
        Given the user is at the home page
        And the user selects the "Date and time" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: dd MMMM yyyy h:mm tt, FeedbackIcon: default, read-only and disabled"
        Then the Date part of the Date and Time component is not editable
        And the Time part of the Date and Time component is not editable
        And the Date part of the Date and Time component is disabled
        And the Time part of the Date and Time component is disabled
        And the Date and Time component image matches the base image "dateandtime-readonly-disabled"

    Scenario: the date and time component can be made read-only without a read-only icon
        Given the user is at the home page
        And the user selects the "Date and time" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: dd MMMM yyyy h:mm tt, FeedbackIcon: default, read-only, read-only icon hidden"
        Then the Date part of the Date and Time component is not editable
        And the Time part of the Date and Time component is not editable
        And the Date and Time component image matches the base image "dateandtime-readonly-no-icon"

    Scenario: the date and time component can be made read-only and disabled without a read-only icon
        Given the user is at the home page
        And the user selects the "Date and time" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: dd MMMM yyyy h:mm tt, FeedbackIcon: default, read-only, disabled and read-only icon hidden"
        Then the Date part of the Date and Time component is not editable
        And the Time part of the Date and Time component is not editable
        And the Date part of the Date and Time component is disabled
        And the Time part of the Date and Time component is disabled
        And the Date and Time component image matches the base image "dateandtime-readonly-disabled-no-icon"


_https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/dateandtime/dateandtime_5.feature_

@component @temporal @dateandtime @dateandtime_5
Feature: the date and time component has placeholders which match the date and time formats
    Scenario: the date and time component has placeholders which match the date and time formats
        Given the user is at the home page
        And the user selects the "Date and time" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: default, allow clear, null initial value, FeedbackIcon.Error"
        Then the Date part of the Date and Time component has the placeholder "yyyy-MM-dd"
        And the Time part of the Date and Time component has the placeholder "HH:mm:ss"
        And the Date and Time component image matches the base image "dateandtime-placeholder"
</div>

***

**Comments:** \

<div class="comments">

</div>

***

**Timeline events:** \

<div class="timeline">
<div class="timeline">
LABELLED | *User:* phillidgithub *Created on:* 02/07/2024 13:44:39 *Label name:* Validation and Verification *Label description:* Use this label to identify the issue as relating to the validation and verification of a feature

LABELLED | *User:* phillidgithub *Created on:* 02/07/2024 13:44:39 *Label name:* v1.0.0 *Label description:* Version 1.0.0

ASSIGNED | *User:* phillidgithub *Created on:* 11/07/2024 07:44:12 *Assignee:* phillidgithub
</div>
</div>


<br/>

--------------- feature ends ---------------

<br/>

***

<div id="CCTC_Components-27">**Feature:** Text component</div>

***

----------------------------------------------------------------------------------------------------------------------- -------------------------------------------------------------------------------
**Id:** I_kwDOKq1Kuc6NChuI                                                                                              **Uid:** CCTC_Components-27                                                    
                                                                                                                                                                                                        
**Author:** phillidgithub                                                                                               **Created:** 21/06/2024 10:34:51                                               
                                                                                                                                                                                                        
**Assignees:** phillidgithub                                                                                            **Resource path:** <a href=https://github.com//CCTC-team/CCTC_Components/issues/27 target=_blank>/CCTC-team/CCTC_Components/issues/27</a>
                                                                                                                                                                                                        
**Milestone:** |none|                                                                                                   **Labels:** Validation and Verification || v1.0.0                              
                                                                                                                                                                                                        
----------------------------------------------------------------------------------------------------------------------- -------------------------------------------------------------------------------

***

----------------------------------------------------------------------------------------------------------------------- -------------------------------------------------------------------------------
**State:** OPEN                                                                                                         **State reason**: |not applicable|                                             
                                                                                                                                                                                                        
**Includes created edit:** true                                                                                         
                                                                                                                                                                                                        
**Closed:** false                                                                                                       **Closed on:** |none|                                                          
                                                                                                                                                                                                        
**Editor:** phillidgithub                                                                                               **Updated on:** 11/07/2024 07:44:49                                            
                                                                                                                                                                                                        
**Locked:** false                                                                                                       **Participants:** phillidgithub                                                
                                                                                                                                                                                                        
----------------------------------------------------------------------------------------------------------------------- -------------------------------------------------------------------------------

***

**Project item body:** \
<div class="feature-body">
<p dir="auto"><strong>Brief description:</strong><br>
The text component is used to input text<br>
NOTE: the branch of dp_dev is used but should be main</p>
<p dir="auto"><strong>User specs:</strong><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/user_specs/textbox/text.spec">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/user_specs/textbox/text.spec</a></p>
<p dir="auto"><strong>Functional scripts:</strong><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/textbox/text/text_1.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/textbox/text/text_1.feature</a><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/textbox/text/text_2.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/textbox/text/text_2.feature</a><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/textbox/text/text_3.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/textbox/text/text_3.feature</a><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/textbox/text/text_4.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/textbox/text/text_4.feature</a></p>
<p dir="auto"><strong>Test files:</strong><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/tests/CCTC_Components.bUnit.test/TextTests.cs">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/tests/CCTC_Components.bUnit.test/TextTests.cs</a></p>
<p dir="auto"><strong>Pre Review:</strong></p>
<ul class="contains-task-list">
<li class="task-list-item"><input type="checkbox" id="" disabled="" class="task-list-item-checkbox" aria-label="Incomplete task"> Script(s) target the correct feature sufficiently</li>
<li class="task-list-item"><input type="checkbox" id="" disabled="" class="task-list-item-checkbox" aria-label="Incomplete task"> The user requirements are met</li>
</ul>
<p dir="auto"><strong>Post Review:</strong></p>
<ul class="contains-task-list">
<li class="task-list-item"><input type="checkbox" id="" disabled="" class="task-list-item-checkbox" aria-label="Incomplete task"> Script(s) target the correct feature sufficiently</li>
<li class="task-list-item"><input type="checkbox" id="" disabled="" class="task-list-item-checkbox" aria-label="Incomplete task"> Script(s) appropriately test the feature</li>
</ul>
</div>

***

**User specification:** \

<div class="spec-script">
| 
| _https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/user_specs/textbox/text.spec_
| 
| 1 - the text component can receive text input
| 2 - the text component has the facility to redact text and / or add a placeholder
| 3 - the text component input can be constrained by applying an input mask, preventing whitespace (with a configurable response delay) or setting a max length
| 4 - the text component can be made read-only and / or disabled. The read-only icon is optional
</div>

**Functional scripts:** \

<div class="spec-script">

_https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/textbox/text/text_1.feature_

@component @textbox @text @text_1
Feature: the text component can receive text input
    Scenario: the text component sample page is available
        Given the user is at the home page
        When the user selects the "Text" component in the container "Input"
        Then the url ending is "textsample"

    Scenario: the text component can receive text input
        Given the user is at the home page
        And the user selects the "Text" component in the container "Input"
        When the user enters "Test data" into the Text component
        Then the Text component has the value "Test data"


_https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/textbox/text/text_2.feature_

@component @textbox @text @text_2
Feature: the text component has the facility to redact text and / or add a placeholder
    Scenario: the text component can redact text
        Given the user is at the home page
        And the user selects the "Text" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Reactive with prevent whitespace and redacted text"
        Then the Text component image matches the base image "text-redacted"

    Scenario: the text component can add a placeholder
        Given the user is at the home page
        And the user selects the "Text" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "With mask and style applied"
        Then the Text component has the placeholder "(000)000/000/00"
        And the Text component image matches the base image "text-placeholder"


_https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/textbox/text/text_3.feature_

@component @textbox @text @text_3
Feature: the text component input can be constrained by applying an input mask, preventing whitespace (with a configurable response delay) or setting a max length
    Scenario: the text component input can be constrained by applying an input mask
        Given the user is at the home page
        And the user selects the "Text" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "With alternative mask"
        And the Text component has the placeholder ">L<??-00-0"
        When the user enters "lp12456" into the Text component
        Then the Text component has the value "Lp-12-4"

    Scenario: the text component can prevent whitespace
        Given the user is at the home page
        And the user selects the "Text" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Reactive with prevent whitespace and redacted text"
        When the user focusses on the Text component
        And the user presses the backspace key 9 times
        Then the Text component has the value "Demo Text"

    Scenario: the text component can set a max length
        Given the user is at the home page
        And the user selects the "Text" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "With callback and max length of 20"
        When the user enters "Some demo Text greater than max length" into the Text component
        Then the Text component has the value "Some demo Text great"


_https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/textbox/text/text_4.feature_

@component @textbox @text @text_4
Feature: the text component can be made read-only and / or disabled. The read-only icon is optional
    Scenario: the text component can be made read-only
        Given the user is at the home page
        And the user selects the "Text" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Read-only"
        Then the Text component is not editable
        And the Text component image matches the base image "text-readonly"

    Scenario: the text component can be disabled
        Given the user is at the home page
        And the user selects the "Text" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Disabled"
        Then the Text component is disabled
        And the Text component image matches the base image "text-disabled"

    Scenario: the text component can be made read-only and disabled
        Given the user is at the home page
        And the user selects the "Text" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Disabled and read-only"
        Then the Text component is not editable
        And the Text component is disabled
        And the Text component image matches the base image "text-readonly-disabled"

    Scenario: the text component can be made read-only without a read-only icon
        Given the user is at the home page
        And the user selects the "Text" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Read-only long (hide read-only icon)"
        Then the Text component is not editable
        And the Text component image matches the base image "text-readonly-no-icon"

    Scenario: the text component can be made read-only and disabled without a read-only icon
        Given the user is at the home page
        And the user selects the "Text" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Disabled and read-only (hide read-only icon)"
        Then the Text component is not editable
        And the Text component is disabled
        And the Text component image matches the base image "text-readonly-disabled-no-icon"
</div>

***

**Comments:** \

<div class="comments">

</div>

***

**Timeline events:** \

<div class="timeline">
<div class="timeline">
LABELLED | *User:* phillidgithub *Created on:* 21/06/2024 10:34:51 *Label name:* Validation and Verification *Label description:* Use this label to identify the issue as relating to the validation and verification of a feature

LABELLED | *User:* phillidgithub *Created on:* 21/06/2024 10:34:51 *Label name:* v1.0.0 *Label description:* Version 1.0.0

ASSIGNED | *User:* phillidgithub *Created on:* 11/07/2024 07:44:49 *Assignee:* phillidgithub
</div>
</div>


<br/>

--------------- feature ends ---------------

<br/>

***

<div id="CCTC_Components-28">**Feature:** Text area component</div>

***

----------------------------------------------------------------------------------------------------------------------- -------------------------------------------------------------------------------
**Id:** I_kwDOKq1Kuc6NCiSe                                                                                              **Uid:** CCTC_Components-28                                                    
                                                                                                                                                                                                        
**Author:** phillidgithub                                                                                               **Created:** 21/06/2024 10:36:16                                               
                                                                                                                                                                                                        
**Assignees:** phillidgithub                                                                                            **Resource path:** <a href=https://github.com//CCTC-team/CCTC_Components/issues/28 target=_blank>/CCTC-team/CCTC_Components/issues/28</a>
                                                                                                                                                                                                        
**Milestone:** |none|                                                                                                   **Labels:** Validation and Verification || v1.0.0                              
                                                                                                                                                                                                        
----------------------------------------------------------------------------------------------------------------------- -------------------------------------------------------------------------------

***

----------------------------------------------------------------------------------------------------------------------- -------------------------------------------------------------------------------
**State:** OPEN                                                                                                         **State reason**: |not applicable|                                             
                                                                                                                                                                                                        
**Includes created edit:** true                                                                                         
                                                                                                                                                                                                        
**Closed:** false                                                                                                       **Closed on:** |none|                                                          
                                                                                                                                                                                                        
**Editor:** phillidgithub                                                                                               **Updated on:** 11/07/2024 07:44:16                                            
                                                                                                                                                                                                        
**Locked:** false                                                                                                       **Participants:** phillidgithub                                                
                                                                                                                                                                                                        
----------------------------------------------------------------------------------------------------------------------- -------------------------------------------------------------------------------

***

**Project item body:** \
<div class="feature-body">
<p dir="auto"><strong>Brief description:</strong><br>
The text area component is used to input text<br>
NOTE: the branch of dp_dev is used but should be main</p>
<p dir="auto"><strong>User specs:</strong><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/user_specs/textbox/textarea.spec">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/user_specs/textbox/textarea.spec</a></p>
<p dir="auto"><strong>Functional scripts:</strong><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/textbox/textarea/textarea_1.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/textbox/textarea/textarea_1.feature</a><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/textbox/textarea/textarea_2.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/textbox/textarea/textarea_2.feature</a><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/textbox/textarea/textarea_3.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/textbox/textarea/textarea_3.feature</a><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/textbox/textarea/textarea_4.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/textbox/textarea/textarea_4.feature</a></p>
<p dir="auto"><strong>Test files:</strong><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/tests/CCTC_Components.bUnit.test/TextTests.cs">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/tests/CCTC_Components.bUnit.test/TextTests.cs</a></p>
<p dir="auto"><strong>Pre Review:</strong></p>
<ul class="contains-task-list">
<li class="task-list-item"><input type="checkbox" id="" disabled="" class="task-list-item-checkbox" aria-label="Incomplete task"> Script(s) target the correct feature sufficiently</li>
<li class="task-list-item"><input type="checkbox" id="" disabled="" class="task-list-item-checkbox" aria-label="Incomplete task"> The user requirements are met</li>
</ul>
<p dir="auto"><strong>Post Review:</strong></p>
<ul class="contains-task-list">
<li class="task-list-item"><input type="checkbox" id="" disabled="" class="task-list-item-checkbox" aria-label="Incomplete task"> Script(s) target the correct feature sufficiently</li>
<li class="task-list-item"><input type="checkbox" id="" disabled="" class="task-list-item-checkbox" aria-label="Incomplete task"> Script(s) appropriately test the feature</li>
</ul>
</div>

***

**User specification:** \

<div class="spec-script">
| 
| _https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/user_specs/textbox/textarea.spec_
| 
| 1 - the text area component can receive text input
| 2 - the text area component has the facility to add a placeholder and / or set an initial number of display rows
| 3 - the text area component input can be constrained by applying an input mask, preventing whitespace (with a configurable response delay) or setting a max length
| 4 - the text area component can be made read-only and / or disabled. The read-only icon is optional
</div>

**Functional scripts:** \

<div class="spec-script">

_https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/textbox/textarea/textarea_1.feature_

@component @textbox @textarea @textarea_1
Feature: the text area component can receive text input
    Scenario: the text area component sample page is available
        Given the user is at the home page
        When the user selects the "Text area" component in the container "Input"
        Then the url ending is "textareasample"

    Scenario: the text area component can receive text input
        Given the user is at the home page
        And the user selects the "Text area" component in the container "Input"
        When the user enters "Test data" into the Text area component
        Then the Text area component has the value "Test data"


_https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/textbox/textarea/textarea_2.feature_

@component @textbox @textarea @textarea_2
Feature: the text area component has the facility to add a placeholder and / or set an initial number of display rows
    Scenario: the text area component can add a placeholder
        Given the user is at the home page
        And the user selects the "Text area" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "With mask and style applied"
        Then the Text area component has the placeholder "(000)000/000/00"
        And the Text area component image matches the base image "textarea-placeholder"

    Scenario: the text area component can set an initial number of display rows
        Given the user is at the home page
        And the user selects the "Text area" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "With four given rows and CssClass applied"
        Then the Text area component has 4 rows


_https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/textbox/textarea/textarea_3.feature_

@component @textbox @textarea @textarea_3
Feature: the text area component input can be constrained by applying an input mask, preventing whitespace (with a configurable response delay) or setting a max length
    Scenario: the text area component input can be constrained by applying an input mask
        Given the user is at the home page
        And the user selects the "Text area" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "With mask and style applied"
        And the Text area component has the placeholder "(000)000/000/00"
        When the user enters "4857463545867" into the Text area component
        Then the Text area component has the value "(485)746/354/58"

    Scenario: the text area component can prevent whitespace
        Given the user is at the home page
        And the user selects the "Text area" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Reactive with prevent whitespace"
        When the user focusses on the Text area component
        And the user presses the backspace key 13 times
        Then the Text area component has the value "Demo TextArea"

    Scenario: the text area component can set a max length
        Given the user is at the home page
        And the user selects the "Text area" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "With callback and max length of 20"
        When the user enters "Some demo Text greater than max length" into the Text area component
        Then the Text area component has the value "Some demo Text great"


_https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/textbox/textarea/textarea_4.feature_

@component @textbox @textarea @textarea_4
Feature: the text area component can be made read-only and / or disabled. The read-only icon is optional
    Scenario: the text area component can be made read-only
        Given the user is at the home page
        And the user selects the "Text area" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Read-only"
        Then the Text area component is not editable
        And the Text area component image matches the base image "textarea-readonly"

    Scenario: the text area component can be disabled
        Given the user is at the home page
        And the user selects the "Text area" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Disabled"
        Then the Text area component is disabled
        And the Text area component image matches the base image "textarea-disabled"

    Scenario: the text area component can be made read-only and disabled
        Given the user is at the home page
        And the user selects the "Text area" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Disabled and read-only"
        Then the Text area component is not editable
        And the Text area component is disabled
        And the Text area component image matches the base image "textarea-readonly-disabled"

    Scenario: the text area component can be made read-only without a read-only icon
        Given the user is at the home page
        And the user selects the "Text area" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Read-only (hide read-only icon)"
        Then the Text area component is not editable
        And the Text area component image matches the base image "textarea-readonly-no-icon"

    Scenario: the text area component can be made read-only and disabled without a read-only icon
        Given the user is at the home page
        And the user selects the "Text area" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Disabled and read-only (hide read-only icon)"
        Then the Text area component is not editable
        And the Text area component is disabled
        And the Text area component image matches the base image "textarea-readonly-disabled-no-icon"
</div>

***

**Comments:** \

<div class="comments">

</div>

***

**Timeline events:** \

<div class="timeline">
<div class="timeline">
LABELLED | *User:* phillidgithub *Created on:* 21/06/2024 10:36:16 *Label name:* Validation and Verification *Label description:* Use this label to identify the issue as relating to the validation and verification of a feature

LABELLED | *User:* phillidgithub *Created on:* 21/06/2024 10:36:16 *Label name:* v1.0.0 *Label description:* Version 1.0.0

RENAMED TITLE | *User:* phillidgithub *Created on:* 24/06/2024 11:20:01 *Previous title:* Text area *Current title:* Text area component

ASSIGNED | *User:* phillidgithub *Created on:* 11/07/2024 07:44:15 *Assignee:* phillidgithub
</div>
</div>


<br/>

--------------- feature ends ---------------

<br/>

***

<div id="CCTC_Components-25">**Feature:** Dropdown component</div>

***

----------------------------------------------------------------------------------------------------------------------- -------------------------------------------------------------------------------
**Id:** I_kwDOKq1Kuc6NCbJN                                                                                              **Uid:** CCTC_Components-25                                                    
                                                                                                                                                                                                        
**Author:** phillidgithub                                                                                               **Created:** 21/06/2024 10:20:54                                               
                                                                                                                                                                                                        
**Assignees:** phillidgithub                                                                                            **Resource path:** <a href=https://github.com//CCTC-team/CCTC_Components/issues/25 target=_blank>/CCTC-team/CCTC_Components/issues/25</a>
                                                                                                                                                                                                        
**Milestone:** |none|                                                                                                   **Labels:** Validation and Verification || v1.0.0                              
                                                                                                                                                                                                        
----------------------------------------------------------------------------------------------------------------------- -------------------------------------------------------------------------------

***

----------------------------------------------------------------------------------------------------------------------- -------------------------------------------------------------------------------
**State:** OPEN                                                                                                         **State reason**: |not applicable|                                             
                                                                                                                                                                                                        
**Includes created edit:** true                                                                                         
                                                                                                                                                                                                        
**Closed:** false                                                                                                       **Closed on:** |none|                                                          
                                                                                                                                                                                                        
**Editor:** phillidgithub                                                                                               **Updated on:** 11/07/2024 07:43:09                                            
                                                                                                                                                                                                        
**Locked:** false                                                                                                       **Participants:** phillidgithub                                                
                                                                                                                                                                                                        
----------------------------------------------------------------------------------------------------------------------- -------------------------------------------------------------------------------

***

**Project item body:** \
<div class="feature-body">
<p dir="auto"><strong>Brief description:</strong><br>
The dropdown component is used to select an option from a list of options<br>
NOTE: the branch of dp_dev is used but should be main</p>
<p dir="auto"><strong>User specs:</strong><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/user_specs/dropdown.spec">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/user_specs/dropdown.spec</a></p>
<p dir="auto"><strong>Functional scripts:</strong><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/dropdown/dropdown_1.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/dropdown/dropdown_1.feature</a><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/dropdown/dropdown_2.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/dropdown/dropdown_2.feature</a><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/dropdown/dropdown_3.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/dropdown/dropdown_3.feature</a><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/dropdown/dropdown_4.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/dropdown/dropdown_4.feature</a></p>
<p dir="auto"><strong>Test files:</strong><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/tests/CCTC_Components.bUnit.test/DropDownTests.cs">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/tests/CCTC_Components.bUnit.test/DropDownTests.cs</a></p>
<p dir="auto"><strong>Pre Review:</strong></p>
<ul class="contains-task-list">
<li class="task-list-item"><input type="checkbox" id="" disabled="" class="task-list-item-checkbox" aria-label="Incomplete task"> Script(s) target the correct feature sufficiently</li>
<li class="task-list-item"><input type="checkbox" id="" disabled="" class="task-list-item-checkbox" aria-label="Incomplete task"> The user requirements are met</li>
</ul>
<p dir="auto"><strong>Post Review:</strong></p>
<ul class="contains-task-list">
<li class="task-list-item"><input type="checkbox" id="" disabled="" class="task-list-item-checkbox" aria-label="Incomplete task"> Script(s) target the correct feature sufficiently</li>
<li class="task-list-item"><input type="checkbox" id="" disabled="" class="task-list-item-checkbox" aria-label="Incomplete task"> Script(s) appropriately test the feature</li>
</ul>
</div>

***

**User specification:** \

<div class="spec-script">
| 
| _https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/user_specs/dropdown.spec_
| 
| 1 - the dropdown component option can be changed using the mouse or the keyboard
| 2 - the dropdown component can allow an empty value and an entered value can be optionally cleared
| 3 - the dropdown component handles options that have a large amount of text and can display tooltips when required
| 4 - the dropdown component can be made read-only and individual options can be disabled or set as visible. The read-only icon is optional
</div>

**Functional scripts:** \

<div class="spec-script">

_https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/dropdown/dropdown_1.feature_

@component @dropdown @dropdown_1
Feature: the dropdown component option can be changed using the mouse or the keyboard
    Scenario: the dropdown component sample page is available
        Given the user is at the home page
        When the user selects the "Dropdown" component in the container "Input"
        Then the url ending is "dropdownsample"

    Scenario: the dropdown component option can be changed using the mouse
        Given the user is at the home page
        And the user selects the "Dropdown" component in the container "Input"
        And the current selected dropdown option has the text "One"
        When the user clicks on the current selected dropdown option
        And the dropdown is expanded
        And the user clicks on the dropdown option with the text "Two"
        Then the current selected dropdown option has the text "Two"

    Scenario: the dropdown component option can be changed using the keyboard
        Given the user is at the home page
        And the user selects the "Dropdown" component in the container "Input"
        And the current selected dropdown option has the text "One"
        When the user clicks on the current selected dropdown option
        And the dropdown is expanded
        And the user presses the down arrow key 2 times
        And the user presses the up arrow key once
        Then the current selected dropdown option has the text "Two"


_https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/dropdown/dropdown_2.feature_

@component @dropdown @dropdown_2
Feature: the dropdown component can allow an empty value and an entered value can be optionally cleared
    Scenario: the current selected dropdown option can be empty when show clear is set to true
        Given the user is at the home page
        And the user selects the "Dropdown" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Dropdown type: Nullable Tuple data with show clear"
        Then the current selected dropdown option has the text ""
        And the Dropdown component image matches the base image "dropdown-cleared-hide-clear-icon"

    Scenario: the current selected dropdown option can be cleared when show clear is set to true
        Given the user is at the home page
        And the user selects the "Dropdown" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Dropdown type: nullable int, with callback, show clear and disabled options applied"
        And the current selected dropdown option has the text "2"
        And the Dropdown component image matches the base image "dropdown-show-clear-icon"
        When the user clicks on the dropdown clear icon
        Then the current selected dropdown option has the text ""
        And the dropdown clear icon is no longer in view
        And the Dropdown component image matches the base image "dropdown-cleared-hide-clear-icon"

    Scenario: the current selected dropdown option can be empty when show clear is set to false
        Given the user is at the home page
        And the user selects the "Dropdown" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Dropdown type: Person class, null initial value, do not show clear"
        Then the current selected dropdown option has the text ""
        And the Dropdown component image matches the base image "dropdown-cleared-no-clear-icon"


_https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/dropdown/dropdown_3.feature_

@component @dropdown @dropdown_3
Feature: the dropdown component handles options that have a large amount of text and can display tooltips when required
    Scenario: the current selected option can be truncated
        Given the user is at the home page
        And the user selects the "Dropdown" component in the container "Input"
        When the user clicks on the current selected dropdown option
        And the user moves the mouse relative to the main viewport with an x coordinate of 0 and y coordinate of 0
        And the dropdown is expanded
        And the user presses the down arrow key 5 times
        And the user presses the enter key once
        Then the Dropdown component image matches the base image "dropdown-truncated"

    Scenario: long dropdown options in the listbox can wrap after three lines
        Given the user is at the home page
        And the user selects the "Dropdown" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Dropdown type: Person record, DropDownOptionOverflow: Wrap, TooltipPlacement: Right, DropDownTooltipBehaviour: EnabledOnOptionOverflow"
        When the user clicks on the current selected dropdown option
        And the user moves the mouse relative to the main viewport with an x coordinate of 0 and y coordinate of 0
        And the dropdown is expanded
        And the user presses the up arrow key once
        Then the Dropdown component listbox image matches the base image "dropdown-option-wrap"

    Scenario: long dropdown options in the listbox can truncate on one line
        Given the user is at the home page
        And the user selects the "Dropdown" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Dropdown type: Person record, DropDownOptionOverflow: NoWrap, TooltipPlacement: Right, DropDownTooltipBehaviour: EnabledOnOptionOverflow"
        When the user clicks on the current selected dropdown option
        And the user moves the mouse relative to the main viewport with an x coordinate of 0 and y coordinate of 0
        And the dropdown is expanded
        And the user presses the up arrow key once
        Then the Dropdown component listbox image matches the base image "dropdown-option-nowrap"

    Scenario: long dropdown options in the listbox can scroll on one line
        Given the user is at the home page
        And the user selects the "Dropdown" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Dropdown type: Person record, DropDownOptionOverflow: Scroll, DropDownTooltipBehaviour: Disabled"
        When the user clicks on the current selected dropdown option
        And the user moves the mouse relative to the main viewport with an x coordinate of 0 and y coordinate of 0
        And the dropdown is expanded
        And the user presses the up arrow key once
        Then the dropdown options can scroll
        And the Dropdown component listbox image matches the base image "dropdown-option-scroll"

    Scenario: long dropdown options in the listbox be displayed in full
        Given the user is at the home page
        And the user selects the "Dropdown" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Dropdown type: Person record, DropDownOptionOverflow: None, TooltipPlacement: Right, DropDownTooltipBehaviour: EnabledOnOptionOverflow"
        When the user clicks on the current selected dropdown option
        And the user moves the mouse relative to the main viewport with an x coordinate of 0 and y coordinate of 0
        And the dropdown is expanded
        And the user presses the up arrow key once
        Then the Dropdown component listbox image matches the base image "dropdown-option-full"

    Scenario: the current selected option can have a tooltip
        Given the user is at the home page
        And the user selects the "Dropdown" component in the container "Input"
        When the user clicks on the current selected dropdown option
        And the dropdown is expanded
        And the user presses the down arrow key 5 times
        And the user presses the enter key once
        Then the current selected dropdown option has a tooltip enabled containing the full option text

    Scenario: the current selected option can not have a tooltip
        Given the user is at the home page
        And the user selects the "Dropdown" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Dropdown type: Person record, DropDownOptionOverflow: Scroll, DropDownTooltipBehaviour: Disabled"
        Then the current selected dropdown option does not have a tooltip enabled

    Scenario: dropdown options can have a tooltip
        Given the user is at the home page
        When the user selects the "Dropdown" component in the container "Input"
        Then the dropdown options have tooltips enabled containing the full option text

    Scenario: dropdown options can not have a tooltip
        Given the user is at the home page
        And the user selects the "Dropdown" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Dropdown type: Person record, DropDownOptionOverflow: Scroll, DropDownTooltipBehaviour: Disabled"
        Then the dropdown options do not have tooltips enabled


_https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/dropdown/dropdown_4.feature_

@component @dropdown @dropdown_4
Feature: the dropdown component can be made read-only and individual options can be disabled or set as visible. The read-only icon is optional
    Scenario: the dropdown component can be made read-only
        Given the user is at the home page
        And the user selects the "Dropdown" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Dropdown type: Tuple data, read-only, TooltipPlacement.Bottom"
        When the user clicks on the current selected dropdown option
        And the user moves the mouse relative to the main viewport with an x coordinate of 0 and y coordinate of 0
        Then the dropdown is not expanded
        And the Dropdown component image matches the base image "dropdown-readonly"

    Scenario: the dropdown component can be disabled
        Given the user is at the home page
        And the user selects the "Dropdown" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Dropdown type: Tuple data, disabled, TooltipPlacement: Right"
        When the user clicks on the current selected dropdown option
        And the user moves the mouse relative to the main viewport with an x coordinate of 0 and y coordinate of 0
        Then the dropdown is not expanded
        And the Dropdown component image matches the base image "dropdown-disabled"

    Scenario: the dropdown component can be made read-only and disabled
        Given the user is at the home page
        And the user selects the "Dropdown" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Dropdown type: Tuple data, read-only, disabled, TooltipPlacement: Right"
        When the user clicks on the current selected dropdown option
        And the user moves the mouse relative to the main viewport with an x coordinate of 0 and y coordinate of 0
        Then the dropdown is not expanded
        And the Dropdown component image matches the base image "dropdown-readonly-disabled"

    Scenario: the dropdown component can be made read-only without a read-only icon
        Given the user is at the home page
        And the user selects the "Dropdown" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Dropdown type: Tuple data, read-only, hide read-only icon, TooltipPlacement: Top"
        When the user clicks on the current selected dropdown option
        And the user moves the mouse relative to the main viewport with an x coordinate of 0 and y coordinate of 0
        Then the dropdown is not expanded
        And the Dropdown component image matches the base image "dropdown-readonly-no-icon"

    Scenario: the dropdown component can be made read-only and disabled without a read-only icon
        Given the user is at the home page
        And the user selects the "Dropdown" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Dropdown type: Tuple data, read-only, disabled, hide read-only icon, TooltipPlacement: Bottom"
        When the user clicks on the current selected dropdown option
        And the user moves the mouse relative to the main viewport with an x coordinate of 0 and y coordinate of 0
        Then the dropdown is not expanded
        And the Dropdown component image matches the base image "dropdown-readonly-disabled-no-icon"

    Scenario: the dropdown component individual options can be disabled
        Given the user is at the home page
        And the user selects the "Dropdown" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Dropdown type: nullable int, with callback, show clear and disabled options applied"
        And the current selected dropdown option has the text "2"
        When the user clicks on the current selected dropdown option
        And the user moves the mouse relative to the main viewport with an x coordinate of 0 and y coordinate of 0
        And the dropdown is expanded
        And the Dropdown component listbox image matches the base image "dropdown-option-disabled"
        And the user clicks on the dropdown option with the text "4"
        Then the current selected dropdown option has the text "2"
</div>

***

**Comments:** \

<div class="comments">

</div>

***

**Timeline events:** \

<div class="timeline">
<div class="timeline">
LABELLED | *User:* phillidgithub *Created on:* 21/06/2024 10:20:55 *Label name:* Validation and Verification *Label description:* Use this label to identify the issue as relating to the validation and verification of a feature

LABELLED | *User:* phillidgithub *Created on:* 21/06/2024 10:21:20 *Label name:* v1.0.0 *Label description:* Version 1.0.0

ASSIGNED | *User:* phillidgithub *Created on:* 11/07/2024 07:43:09 *Assignee:* phillidgithub
</div>
</div>


<br/>

--------------- feature ends ---------------

<br/>

***

<div id="CCTC_Components-37">**Feature:** Numeric component</div>

***

----------------------------------------------------------------------------------------------------------------------- -------------------------------------------------------------------------------
**Id:** I_kwDOKq1Kuc6PMx_8                                                                                              **Uid:** CCTC_Components-37                                                    
                                                                                                                                                                                                        
**Author:** phillidgithub                                                                                               **Created:** 11/07/2024 07:40:52                                               
                                                                                                                                                                                                        
**Assignees:** phillidgithub                                                                                            **Resource path:** <a href=https://github.com//CCTC-team/CCTC_Components/issues/37 target=_blank>/CCTC-team/CCTC_Components/issues/37</a>
                                                                                                                                                                                                        
**Milestone:** |none|                                                                                                   **Labels:** Validation and Verification || v1.0.0                              
                                                                                                                                                                                                        
----------------------------------------------------------------------------------------------------------------------- -------------------------------------------------------------------------------

***

----------------------------------------------------------------------------------------------------------------------- -------------------------------------------------------------------------------
**State:** OPEN                                                                                                         **State reason**: |not applicable|                                             
                                                                                                                                                                                                        
**Includes created edit:** true                                                                                         
                                                                                                                                                                                                        
**Closed:** false                                                                                                       **Closed on:** |none|                                                          
                                                                                                                                                                                                        
**Editor:** phillidgithub                                                                                               **Updated on:** 11/07/2024 07:43:16                                            
                                                                                                                                                                                                        
**Locked:** false                                                                                                       **Participants:** phillidgithub                                                
                                                                                                                                                                                                        
----------------------------------------------------------------------------------------------------------------------- -------------------------------------------------------------------------------

***

**Project item body:** \
<div class="feature-body">
<p dir="auto">Brief description:<br>
The numeric component is used to input a number<br>
NOTE: the branch of dp_dev is used but should be main</p>
<p dir="auto">User specs:<br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/user_specs/numeric.spec">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/user_specs/numeric.spec</a></p>
<p dir="auto">Functional scripts:<br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/numeric/numeric_1.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/numeric/numeric_1.feature</a><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/numeric/numeric_2.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/numeric/numeric_2.feature</a><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/numeric/numeric_3.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/numeric/numeric_3.feature</a><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/numeric/numeric_4.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/numeric/numeric_4.feature</a><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/numeric/numeric_5.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/numeric/numeric_5.feature</a></p>
<p dir="auto">Test files:<br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/tests/CCTC_Components.bUnit.test/NumericTests.cs">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/tests/CCTC_Components.bUnit.test/NumericTests.cs</a></p>
<p dir="auto"><strong>Pre Review:</strong></p>
<ul class="contains-task-list">
<li class="task-list-item"><input type="checkbox" id="" disabled="" class="task-list-item-checkbox" aria-label="Incomplete task"> Script(s) target the correct feature sufficiently</li>
<li class="task-list-item"><input type="checkbox" id="" disabled="" class="task-list-item-checkbox" aria-label="Incomplete task"> The user requirements are met</li>
</ul>
<p dir="auto"><strong>Post Review:</strong></p>
<ul class="contains-task-list">
<li class="task-list-item"><input type="checkbox" id="" disabled="" class="task-list-item-checkbox" aria-label="Incomplete task"> Script(s) target the correct feature sufficiently</li>
<li class="task-list-item"><input type="checkbox" id="" disabled="" class="task-list-item-checkbox" aria-label="Incomplete task"> Script(s) appropriately test the feature</li>
</ul>
</div>

***

**User specification:** \

<div class="spec-script">
| 
| _https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/user_specs/numeric.spec_
| 
| 1 - the numeric component can receive numeric input
| 2 - the numeric component has the facility to redact text and / or add a placeholder
| 3 - the numeric component input can be constrained by preventing whitespace (with a configurable response delay) or setting a max length
| 4 - the numeric component input can be constrained by applying an optional number format and the mathematical rounding method can be specified
| 5 - the numeric component can be made read-only and / or disabled. The read-only icon is optional
</div>

**Functional scripts:** \

<div class="spec-script">

_https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/numeric/numeric_1.feature_

@component @numeric @numeric_1
Feature: the numeric component can receive numeric input
    Scenario: the numeric component sample page is available
        Given the user is at the home page
        When the user selects the "Numeric" component in the container "Input"
        Then the url ending is "numericsample"

    Scenario: the numeric component can receive numeric input
        Given the user is at the home page
        Given the user selects the "Numeric" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: -##00"
        When the user enters "123456" into the Numeric component
        Then the Numeric component has the value "1234"


_https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/numeric/numeric_2.feature_

@component @numeric @numeric_2
Feature: the numeric component has the facility to redact text and / or add a placeholder
    Scenario: the numeric component has the facility to redact text
        Given the user is at the home page
        Given the user selects the "Numeric" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "No format with a null initial value, redact text, allows whitespace, max length"
        When the user enters "123" into the Numeric component
        Then the Numeric component image matches the base image "numeric-redacted"

    Scenario: the numeric component can add a placeholder
        Given the user is at the home page
        Given the user selects the "Numeric" component in the container "Input"
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "No format with a null initial value, redact text, allows whitespace, max length"
        Then the Numeric component has the placeholder "123"
        And the Numeric component image matches the base image "numeric-placeholder"


_https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/numeric/numeric_3.feature_

@component @numeric @numeric_3
Feature: the numeric component input can be constrained by preventing whitespace (with a configurable response delay) or setting a max length
    Scenario: the numeric component can prevent whitespace
        Given the user is at the home page
        Given the user selects the "Numeric" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: -##00"
        When the user focusses on the Numeric component
        And the user presses the backspace key 3 times
        Then the Numeric component has the value "123"

    Scenario: the numeric component can set a max length
        Given the user is at the home page
        Given the user selects the "Numeric" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "No format with a null initial value, redact text, allows whitespace, max length"
        When the user enters "12345678" into the Text component
        Then the Text component has the value "1234567"


_https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/numeric/numeric_4.feature_

@component @numeric @numeric_4
Feature: the numeric component input can be constrained by applying an optional number format and the mathematical rounding method can be specified
    Scenario: the numeric component input can have no format
        Given the user is at the home page
        Given the user selects the "Numeric" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "No format with a null initial value, redact text, allows whitespace, max length"
        When the user enters "1.2" into the Numeric component
        Then the Numeric component has the stable value "1.2"

    Scenario: the numeric component input can apply a number format
        Given the user is at the home page
        Given the user selects the "Numeric" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Exponential Format: E4"
        When the user enters "15678" into the Numeric component
        Then the Numeric component has the value "1.5678E+004"

    Scenario: the numeric component input can apply a mathematical rounding method
        Given the user is at the home page
        Given the user selects the "Numeric" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: -#000.0, rounding to even, CssClass applied"
        When the user enters "296.45" into the Numeric component
        Then the Numeric component has the value "296.4"


_https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/numeric/numeric_5.feature_

@component @numeric @numeric_5
Feature: the numeric component can be made read-only and / or disabled. The read-only icon is optional
    Scenario: the numeric component can be made read-only
        Given the user is at the home page
        Given the user selects the "Numeric" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Read-only"
        Then the Numeric component is not editable
        And the Numeric component image matches the base image "numeric-readonly"

    Scenario: the numeric component can be disabled
        Given the user is at the home page
        Given the user selects the "Numeric" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Disabled"
        Then the Numeric component is disabled
        And the Numeric component image matches the base image "numeric-disabled"

    Scenario: the numeric component can be made read-only and disabled
        Given the user is at the home page
        Given the user selects the "Numeric" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Disabled and read-only"
        Then the Numeric component is not editable
        And the Numeric component is disabled
        And the Numeric component image matches the base image "numeric-readonly-disabled"

    Scenario: the numeric component can be made read-only without a read-only icon
        Given the user is at the home page
        Given the user selects the "Numeric" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Read-only long (hide read-only icon)"
        Then the Numeric component is not editable
        And the Numeric component image matches the base image "numeric-readonly-no-icon"

    Scenario: the numeric component can be made read-only and disabled without a read-only icon
        Given the user is at the home page
        Given the user selects the "Numeric" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Disabled and read-only (hide read-only icon)"
        Then the Numeric component is not editable
        And the Numeric component is disabled
        And the Numeric component image matches the base image "numeric-readonly-disabled-no-icon"
</div>

***

**Comments:** \

<div class="comments">

</div>

***

**Timeline events:** \

<div class="timeline">
<div class="timeline">
LABELLED | *User:* phillidgithub *Created on:* 11/07/2024 07:40:52 *Label name:* Validation and Verification *Label description:* Use this label to identify the issue as relating to the validation and verification of a feature

LABELLED | *User:* phillidgithub *Created on:* 11/07/2024 07:40:52 *Label name:* v1.0.0 *Label description:* Version 1.0.0

ASSIGNED | *User:* phillidgithub *Created on:* 11/07/2024 07:43:16 *Assignee:* phillidgithub
</div>
</div>


<br/>

--------------- feature ends ---------------

<br/>

***

<div id="CCTC_Components-36">**Feature:** Radio component</div>

***

----------------------------------------------------------------------------------------------------------------------- -------------------------------------------------------------------------------
**Id:** I_kwDOKq1Kuc6OygC3                                                                                              **Uid:** CCTC_Components-36                                                    
                                                                                                                                                                                                        
**Author:** phillidgithub                                                                                               **Created:** 08/07/2024 13:10:01                                               
                                                                                                                                                                                                        
**Assignees:** phillidgithub                                                                                            **Resource path:** <a href=https://github.com//CCTC-team/CCTC_Components/issues/36 target=_blank>/CCTC-team/CCTC_Components/issues/36</a>
                                                                                                                                                                                                        
**Milestone:** |none|                                                                                                   **Labels:** Validation and Verification || v1.0.0                              
                                                                                                                                                                                                        
----------------------------------------------------------------------------------------------------------------------- -------------------------------------------------------------------------------

***

----------------------------------------------------------------------------------------------------------------------- -------------------------------------------------------------------------------
**State:** OPEN                                                                                                         **State reason**: |not applicable|                                             
                                                                                                                                                                                                        
**Includes created edit:** false                                                                                        
                                                                                                                                                                                                        
**Closed:** false                                                                                                       **Closed on:** |none|                                                          
                                                                                                                                                                                                        
**Editor:** |unknown|                                                                                                   **Updated on:** 11/07/2024 07:43:21                                            
                                                                                                                                                                                                        
**Locked:** false                                                                                                       **Participants:** phillidgithub                                                
                                                                                                                                                                                                        
----------------------------------------------------------------------------------------------------------------------- -------------------------------------------------------------------------------

***

**Project item body:** \
<div class="feature-body">
<p dir="auto">Brief description:<br>
The radio component is used to select an option from a list of options<br>
NOTE: the branch of dp_dev is used but should be main</p>
<p dir="auto">User specs:<br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/user_specs/radio.spec">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/user_specs/radio.spec</a></p>
<p dir="auto">Functional scripts:<br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/radio/radio_1.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/radio/radio_1.feature</a><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/radio/radio_2.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/radio/radio_2.feature</a><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/radio/radio_3.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/radio/radio_3.feature</a><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/radio/radio_4.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/radio/radio_4.feature</a><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/radio/radio_5.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/radio/radio_5.feature</a><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/radio/radio_6.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/radio/radio_6.feature</a></p>
<p dir="auto">Test files:<br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/tests/CCTC_Components.bUnit.test/RadioTests.cs">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/tests/CCTC_Components.bUnit.test/RadioTests.cs</a></p>
<p dir="auto"><strong>Pre Review:</strong></p>
<ul class="contains-task-list">
<li class="task-list-item"><input type="checkbox" id="" disabled="" class="task-list-item-checkbox" aria-label="Incomplete task"> Script(s) target the correct feature sufficiently</li>
<li class="task-list-item"><input type="checkbox" id="" disabled="" class="task-list-item-checkbox" aria-label="Incomplete task"> The user requirements are met</li>
</ul>
<p dir="auto"><strong>Post Review:</strong></p>
<ul class="contains-task-list">
<li class="task-list-item"><input type="checkbox" id="" disabled="" class="task-list-item-checkbox" aria-label="Incomplete task"> Script(s) target the correct feature sufficiently</li>
<li class="task-list-item"><input type="checkbox" id="" disabled="" class="task-list-item-checkbox" aria-label="Incomplete task"> Script(s) appropriately test the feature</li>
</ul>
</div>

***

**User specification:** \

<div class="spec-script">
| 
| _https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/user_specs/radio.spec_
| 
| 1 - the radio component option can be changed using the mouse by clicking on a radio button or radio label
| 2 - the radio component option can be changed using the keyboard
| 3 - the radio component can allow an empty value and an entered value can be optionally cleared
| 4 - the radio component handles options that have a large amount of text and can display tooltips when required
| 5 - the radio component can be made read-only and individual options can be disabled or set as visible. The read-only icon is optional
| 6 - the radio component orientation can be vertical left, vertical right or horizontal
</div>

**Functional scripts:** \

<div class="spec-script">

_https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/radio/radio_1.feature_

@component @radio @radio_1
Feature: the radio component option can be changed using the mouse by clicking on a radio button or radio label
    Scenario: the radio component sample page is available
        Given the user is at the home page
        When the user selects the "Radio" component in the container "Input"
        Then the url ending is "radiosample"

    Scenario: the radio component option can be changed using the mouse by clicking on a radio button
        Given the user is at the home page
        And the user selects the "Radio" component in the container "Input"
        And the current selected radio option has the text "Option 1"
        When the user clicks on the radio option button with the associated label text "Option 2"
        Then the current selected radio option has the text "Option 2"

    Scenario: the radio component option can be changed using the mouse by clicking on a radio label
        Given the user is at the home page
        And the user selects the "Radio" component in the container "Input"
        And the current selected radio option has the text "Option 1"
        When the user clicks on the radio option label with the text "Option 2"
        Then the current selected radio option has the text "Option 2"


_https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/radio/radio_2.feature_

@component @radio @radio_2
Feature: the radio component option can be changed using the keyboard
    Scenario: the radio component option can be changed using the keyboard
        Given the user is at the home page
        And the user selects the "Radio" component in the container "Input"
        And the current selected radio option has the text "Option 1"
        When the user clicks on the radio option button with the associated label text "Option 1"
        And the user presses the down arrow key once
        Then the current selected radio option has the text "Option 2"


_https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/radio/radio_3.feature_

@component @radio @radio_3
Feature: the radio component can allow an empty value and an entered value can be optionally cleared
    Scenario: the current selected radio option can be empty when show clear is set to true
        Given the user is at the home page
        And the user selects the "Radio" component in the container "Input"
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "RadioOrientation: Horizontal, null initial value with show clear"
        Then the Radio component does not have a selected radio option
        And the Radio component image matches the base image "radio-cleared-hide-clear-icon"

    Scenario: the current selected radio option can be cleared when show clear is set to true
        Given the user is at the home page
        And the user selects the "Radio" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "RadioOrientation: VerticalLeft, nullable tuple data with show clear"
        And the current selected radio option has the text "Yes"
        And the Radio component image matches the base image "radio-show-clear-icon"
        When the user clicks on the radio clear icon
        Then the Radio component does not have a selected radio option
        And the radio clear icon is no longer in view

    Scenario: the current selected radio option can be empty when show clear is set to false
        Given the user is at the home page
        And the user selects the "Radio" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "RadioOrientation: Horizontal, null initial value, CssClass applied"
        Then the Radio component does not have a selected radio option


_https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/radio/radio_4.feature_

@component @radio @radio_4
Feature: the radio component handles options that have a large amount of text and can display tooltips when required
    Scenario: long radio options can wrap after three lines
        Given the user is at the home page
        And the user selects the "Radio" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "RadioOrientation: VerticalLeft, RadioLabelOverflow: Wrap, RadioLabelTooltipPlacement: Right, RadioLabelTooltipBehaviour: EnabledOnLabelOverflow, with callback"
        When the user clicks on the radio option button with the associated label text "Option 3"
        And the user presses the down arrow key once
        Then the Radio component image matches the base image "radio-option-wrap"

    Scenario: long radio options can truncate on one line
        Given the user is at the home page
        And the user selects the "Radio" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "RadioOrientation: VerticalLeft, RadioLabelOverflow: NoWrap, RadioLabelTooltipPlacement: Right, RadioLabelTooltipBehaviour: EnabledOnLabelOverflow, with callback"
        When the user clicks on the radio option button with the associated label text "One"
        And the user presses the down arrow key 2 times
        Then the Radio component image matches the base image "radio-option-nowrap"

    Scenario: long radio options can scroll on one line
        Given the user is at the home page
        And the user selects the "Radio" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "RadioOrientation: VerticalLeft, RadioLabelOverflow: Scroll, RadioLabelTooltipPlacement: Right, RadioLabelTooltipBehaviour: Disabled, with callback"
        When the user clicks on the radio option button with the associated label text "One"
        And the user presses the down arrow key 2 times
        Then the radio options can scroll
        And the Radio component image matches the base image "radio-option-scroll"

    Scenario: long radio options can be displayed in full
        Given the user is at the home page
        And the user selects the "Radio" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "RadioOrientation: VerticalLeft, RadioLabelOverflow: None, RadioLabelTooltipPlacement: Right, RadioLabelTooltipBehaviour: Disabled, with callback, input height modified via Style parameter"
        When the user clicks on the radio option button with the associated label text "Option 3"
        And the user presses the down arrow key once
        And the Radio component image matches the base image "radio-option-full"

    Scenario: radio options can have a tooltip
        Given the user is at the home page
        When the user selects the "Radio" component in the container "Input"
        Then the radio options have tooltips enabled containing the full option text

    Scenario: radio options can not have a tooltip
        Given the user is at the home page
        And the user selects the "Radio" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "RadioOrientation: VerticalLeft, RadioLabelOverflow: Scroll, RadioLabelTooltipPlacement: Right, RadioLabelTooltipBehaviour: Disabled, with callback"
        Then the radio options do not have tooltips enabled


_https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/radio/radio_5.feature_

@component @radio @radio_5
Feature: the radio component can be made read-only and individual options can be disabled or set as visible. The read-only icon is optional
    Scenario: the radio component can be made read-only
        Given the user is at the home page
        And the user selects the "Radio" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "RadioOrientation: VerticalLeft, RadioLabelOverflow: Wrap, RadioLabelTooltipPlacement: Top, RadioLabelTooltipBehaviour: EnabledOnLabelOverflow, read-only"
        And the current selected radio option has the text "Option 3"
        When the user clicks on the radio option button with the associated label text "Option 2"
        Then the current selected radio option has the text "Option 3"
        Then the Radio component image matches the base image "radio-readonly"

    Scenario: the radio component individual options can be disabled
        Given the user is at the home page
        And the user selects the "Radio" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "RadioOrientation: VerticalLeft, RadioLabelOverflow: NoWrap, RadioLabelTooltipPlacement: Right, RadioLabelTooltipBehaviour: EnabledOnLabelOverflow, second radio option disabled, fifth radio option not visible"
        Then the radio option button with the associated label text "Option 2" is disabled
        And the Radio component image matches the base image "radio-option-disabled"

    Scenario: the radio component can be made read-only and disabled
        Given the user is at the home page
        And the user selects the "Radio" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "RadioOrientation: VerticalLeft, RadioLabelOverflow: NoWrap, RadioLabelTooltipPlacement: Right, RadioLabelTooltipBehaviour: EnabledOnLabelOverflow, read-only and all radio options disabled, scroll items due to restricted height"
        Then the radio option buttons are all disabled
        And the Radio component image matches the base image "radio-readonly-disabled"

    Scenario: the radio component can be made read-only without a read-only icon
        Given the user is at the home page
        And the user selects the "Radio" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "RadioOrientation: VerticalLeft, RadioLabelOverflow: NoWrap, RadioLabelTooltipPlacement: Right, RadioLabelTooltipBehaviour: EnabledOnLabelOverflow, read-only, hide read-only icon, scroll items due to restricted height"
        And the current selected radio option has the text "Option 2"
        When the user clicks on the radio option button with the associated label text "Option 1"
        Then the current selected radio option has the text "Option 2"
        Then the Radio component image matches the base image "radio-readonly-no-icon"


_https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/radio/radio_6.feature_

@component @radio @radio_6
Feature: the radio component orientation can be vertical left, vertical right or horizontal
    Scenario: the radio component orientation can be vertical left
        Given the user is at the home page
        And the user selects the "Radio" component in the container "Input"
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "RadioOrientation: VerticalLeft, RadioLabelOverflow: Wrap, RadioLabelTooltipPlacement: Right, RadioLabelTooltipBehaviour: EnabledOnLabelOverflow, with callback"
        Then the Radio component image matches the base image "radio-vertical-left"

    Scenario: the radio component orientation can be vertical right
        Given the user is at the home page
        And the user selects the "Radio" component in the container "Input"
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "RadioOrientation: VerticalRight, RadioLabelOverflow: Wrap, RadioLabelTooltipPlacement: Right, RadioLabelTooltipBehaviour: EnabledOnLabelOverflow, with callback"
        Then the Radio component image matches the base image "radio-vertical-right"

    Scenario: the radio component orientation can be horizontal
        Given the user is at the home page
        And the viewport has a width of 1920 and a height of 1200
        And the user selects the "Radio" component in the container "Input"
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "RadioOrientation: Horizontal, RadioLabelOverflow: Wrap, RadioLabelTooltipPlacement: Right, RadioLabelTooltipBehaviour: EnabledOnLabelOverflow, with callback"
        Then the Radio component image matches the base image "radio-horizontal"
</div>

***

**Comments:** \

<div class="comments">

</div>

***

**Timeline events:** \

<div class="timeline">
<div class="timeline">
LABELLED | *User:* phillidgithub *Created on:* 08/07/2024 13:10:01 *Label name:* Validation and Verification *Label description:* Use this label to identify the issue as relating to the validation and verification of a feature

LABELLED | *User:* phillidgithub *Created on:* 08/07/2024 13:11:03 *Label name:* v1.0.0 *Label description:* Version 1.0.0

ASSIGNED | *User:* phillidgithub *Created on:* 11/07/2024 07:43:20 *Assignee:* phillidgithub
</div>
</div>


<br/>

--------------- feature ends ---------------

<br/>

***

<div id="CCTC_Components-26">**Feature:** Date component</div>

***

----------------------------------------------------------------------------------------------------------------------- -------------------------------------------------------------------------------
**Id:** I_kwDOKq1Kuc6NCdbg                                                                                              **Uid:** CCTC_Components-26                                                    
                                                                                                                                                                                                        
**Author:** phillidgithub                                                                                               **Created:** 21/06/2024 10:25:15                                               
                                                                                                                                                                                                        
**Assignees:** phillidgithub                                                                                            **Resource path:** <a href=https://github.com//CCTC-team/CCTC_Components/issues/26 target=_blank>/CCTC-team/CCTC_Components/issues/26</a>
                                                                                                                                                                                                        
**Milestone:** |none|                                                                                                   **Labels:** Validation and Verification || v1.0.0                              
                                                                                                                                                                                                        
----------------------------------------------------------------------------------------------------------------------- -------------------------------------------------------------------------------

***

----------------------------------------------------------------------------------------------------------------------- -------------------------------------------------------------------------------
**State:** OPEN                                                                                                         **State reason**: |not applicable|                                             
                                                                                                                                                                                                        
**Includes created edit:** true                                                                                         
                                                                                                                                                                                                        
**Closed:** false                                                                                                       **Closed on:** |none|                                                          
                                                                                                                                                                                                        
**Editor:** phillidgithub                                                                                               **Updated on:** 11/07/2024 07:44:01                                            
                                                                                                                                                                                                        
**Locked:** false                                                                                                       **Participants:** phillidgithub                                                
                                                                                                                                                                                                        
----------------------------------------------------------------------------------------------------------------------- -------------------------------------------------------------------------------

***

**Project item body:** \
<div class="feature-body">
<p dir="auto"><strong>Brief description:</strong><br>
The date component is used to input a date<br>
NOTE: the branch of dp_dev is used but should be main</p>
<p dir="auto"><strong>User specs:</strong><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/user_specs/temporal/date.spec">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/user_specs/temporal/date.spec</a></p>
<p dir="auto"><strong>Functional scripts:</strong><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/date/date_1.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/date/date_1.feature</a><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/date/date_2.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/date/date_2.feature</a><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/date/date_3.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/date/date_3.feature</a><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/date/date_4.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/date/date_4.feature</a><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/date/date_5.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/date/date_5.feature</a></p>
<p dir="auto"><strong>Test files:</strong><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/tests/CCTC_Components.bUnit.test/DateTests.cs">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/tests/CCTC_Components.bUnit.test/DateTests.cs</a></p>
<p dir="auto"><strong>Pre Review:</strong></p>
<ul class="contains-task-list">
<li class="task-list-item"><input type="checkbox" id="" disabled="" class="task-list-item-checkbox" aria-label="Incomplete task"> Script(s) target the correct feature sufficiently</li>
<li class="task-list-item"><input type="checkbox" id="" disabled="" class="task-list-item-checkbox" aria-label="Incomplete task"> The user requirements are met</li>
</ul>
<p dir="auto"><strong>Post Review:</strong></p>
<ul class="contains-task-list">
<li class="task-list-item"><input type="checkbox" id="" disabled="" class="task-list-item-checkbox" aria-label="Incomplete task"> Script(s) target the correct feature sufficiently</li>
<li class="task-list-item"><input type="checkbox" id="" disabled="" class="task-list-item-checkbox" aria-label="Incomplete task"> Script(s) appropriately test the feature</li>
</ul>
</div>

***

**User specification:** \

<div class="spec-script">
| 
| _https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/user_specs/temporal/date.spec_
| 
| 1 - the date component date can be typed in as text or can be entered via a date picker
| 2 - the date component can allow an empty value and an entered value can be optionally cleared
| 3 - the date component date is validated (date format, min and / or max date) and there is feedback provided to the user via an icon
| 4 - the date component can be made read-only and / or disabled. The read-only icon is optional
| 5 - the date component has a placeholder which matches the date format
</div>

**Functional scripts:** \

<div class="spec-script">

_https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/date/date_1.feature_

@component @temporal @date @date_1
Feature: the date component date can be typed in as text or can be entered via a date picker
    Scenario: the date component sample page is available
        Given the user is at the home page
        When the user selects the "Date" component in the container "Input"
        Then the url ending is "datesample"

    Scenario: the date component date can be typed in as text
        Given the user is at the home page
        And the user selects the "Date" component in the container "Input"
        When the user enters "20210105" into the Date component
        Then the Date component has the value "2021-01-05"

    Scenario: the date component date can be entered via a date picker
        Given the user is at the home page
        And the user selects the "Date" component in the container "Input"
        When the user enters "2021-01-05" into the Date component via the date picker
        Then the Date component has the value "2021-01-05"


_https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/date/date_2.feature_

@component @temporal @date @date_2
Feature: the date component can allow an empty value and an entered value can be optionally cleared
    Scenario: the date component can allow an empty value when allow clear is set to true
        Given the user is at the home page
        And the user selects the "Date" component in the container "Input"
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "Format: default, allow clear, null initial value, FeedbackIcon.Error"
        Then the Date component has the value ""

    Scenario: the date component value can be cleared when allow clear is set to true
        Given the user is at the home page
        And the user selects the "Date" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: default, allow clear, FeedbackIcon.Error"
        When the user enters "" into the Date component
        Then the Date component has the value ""

    Scenario: the date component value can be cleared via the date picker when allow clear is set to true
        Given the user is at the home page
        And the user selects the "Date" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: default, allow clear, FeedbackIcon.Error"
        When the user enters "" into the Date component via the date picker
        Then the Date component has the value ""

    Scenario: the date component can allow an empty value when allow clear is set to false
        Given the user is at the home page
        And the user selects the "Date" component in the container "Input"
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "Format: yyyy-MM-dd, null initial value"
        Then the Date component has the value ""

    Scenario: the date component value can not be cleared when allow clear is set to false
        Given the user is at the home page
        And the user selects the "Date" component in the container "Input"
        When the user enters "" into the Date component
        Then the Date component has the value "2022-05-06"

    Scenario: an alert is shown when the date is cleared via the date picker and allow clear is set to false
        Given the user is at the home page
        When the user selects the "Date" component in the container "Input"
        Then on clearing the Date component value via the date picker an alert is displayed with the text "The date is not permitted to be cleared"


_https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/date/date_3.feature_

@component @temporal @date @date_3
Feature: the date component date is validated (date format, min and / or max date) and there is feedback provided to the user via an icon
    Scenario: an incomplete date is shown as an error
        Given the user is at the home page
        And the user selects the "Date" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: default, FeedbackIcon.Error"
        When the user enters "2021010" into the Date component
        Then the Date component has the value "2021-01-0"
        And the Date component displays a red exclamation mark feedback icon
        And the Date component image matches the base image "date-error"

    Scenario: a complete date is shown as valid
        Given the user is at the home page
        And the user selects the "Date" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: dd MMM yy, FeedbackIcon.Valid"
        When the user enters "06May22" into the Date component
        Then the Date component has the value "06 May 22"
        And the Date component displays a green tick feedback icon
        And the Date component image matches the base image "date-valid"

    Scenario: a date earlier than the minimum date is shown as an error
        Given the user is at the home page
        And the user selects the "Date" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: dd/MM/yyyy, Min (11/12/2023) and Max (16/12/2023), FeedbackIcon.Both"
        When the user enters "10122023" into the Date component
        Then the Date component has the value "10/12/2023"
        And the Date component displays a red exclamation mark feedback icon

    Scenario: a date later than the maximum date is shown as an error
        Given the user is at the home page
        And the user selects the "Date" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: dd/MM/yyyy, Min (11/12/2023) and Max (16/12/2023), FeedbackIcon.Both"
        When the user enters "17122023" into the Date component
        Then the Date component has the value "17/12/2023"
        And the Date component displays a red exclamation mark feedback icon


_https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/date/date_4.feature_

@component @temporal @date @date_4
Feature: the date component can be made read-only and / or disabled. The read-only icon is optional
    Scenario: the date component can be made read-only
        Given the user is at the home page
        And the user selects the "Date" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: yyyy-MM-dd, read-only"
        Then the Date component is not editable
        And the Date component image matches the base image "date-readonly"

    Scenario: the date component can be disabled
        Given the user is at the home page
        And the user selects the "Date" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: yyyy-MM-dd, disabled"
        Then the Date component is disabled
        And the Date component image matches the base image "date-disabled"

    Scenario: the date component can be made read-only and disabled
        Given the user is at the home page
        And the user selects the "Date" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: yyyy-MM-dd, read-only and disabled"
        Then the Date component is not editable
        And the Date component is disabled
        And the Date component image matches the base image "date-readonly-disabled"

    Scenario: the date component can be made read-only without a read-only icon
        Given the user is at the home page
        And the user selects the "Date" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: yyyy-MM-dd, read-only and read-only icon hidden"
        Then the Date component is not editable
        And the Date component image matches the base image "date-readonly-no-icon"

    Scenario: the date component can be made read-only and disabled without a read-only icon
        Given the user is at the home page
        And the user selects the "Date" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: yyyy-MM-dd, read-only, disabled and read-only icon hidden"
        Then the Date component is not editable
        And the Date component is disabled
        And the Date component image matches the base image "date-readonly-disabled-no-icon"


_https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/date/date_5.feature_

@component @temporal @date @date_5
Feature: the date component has a placeholder which matches the date format
    Scenario: the date component has a placeholder which matches the date format
        Given the user is at the home page
        And the user selects the "Date" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: yyyy-MM-dd, null initial value"
        Then the Date component has the placeholder "yyyy-MM-dd"
        And the Date component image matches the base image "date-placeholder"
</div>

***

**Comments:** \

<div class="comments">

</div>

***

**Timeline events:** \

<div class="timeline">
<div class="timeline">
LABELLED | *User:* phillidgithub *Created on:* 21/06/2024 10:25:15 *Label name:* Validation and Verification *Label description:* Use this label to identify the issue as relating to the validation and verification of a feature

LABELLED | *User:* phillidgithub *Created on:* 21/06/2024 10:25:15 *Label name:* v1.0.0 *Label description:* Version 1.0.0

ASSIGNED | *User:* phillidgithub *Created on:* 11/07/2024 07:44:00 *Assignee:* phillidgithub
</div>
</div>


<br/>

--------------- feature ends ---------------

<br/>

***

<div id="CCTC_Components-32">**Feature:** Time component</div>

***

----------------------------------------------------------------------------------------------------------------------- -------------------------------------------------------------------------------
**Id:** I_kwDOKq1Kuc6ODwYr                                                                                              **Uid:** CCTC_Components-32                                                    
                                                                                                                                                                                                        
**Author:** phillidgithub                                                                                               **Created:** 01/07/2024 09:39:35                                               
                                                                                                                                                                                                        
**Assignees:** phillidgithub                                                                                            **Resource path:** <a href=https://github.com//CCTC-team/CCTC_Components/issues/32 target=_blank>/CCTC-team/CCTC_Components/issues/32</a>
                                                                                                                                                                                                        
**Milestone:** |none|                                                                                                   **Labels:** Validation and Verification || v1.0.0                              
                                                                                                                                                                                                        
----------------------------------------------------------------------------------------------------------------------- -------------------------------------------------------------------------------

***

----------------------------------------------------------------------------------------------------------------------- -------------------------------------------------------------------------------
**State:** OPEN                                                                                                         **State reason**: |not applicable|                                             
                                                                                                                                                                                                        
**Includes created edit:** true                                                                                         
                                                                                                                                                                                                        
**Closed:** false                                                                                                       **Closed on:** |none|                                                          
                                                                                                                                                                                                        
**Editor:** phillidgithub                                                                                               **Updated on:** 11/07/2024 07:44:05                                            
                                                                                                                                                                                                        
**Locked:** false                                                                                                       **Participants:** phillidgithub                                                
                                                                                                                                                                                                        
----------------------------------------------------------------------------------------------------------------------- -------------------------------------------------------------------------------

***

**Project item body:** \
<div class="feature-body">
<p dir="auto">Brief description:<br>
The time component is used to input a time<br>
NOTE: the branch of dp_dev is used but should be main</p>
<p dir="auto">User specs:<br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/user_specs/temporal/time.spec">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/user_specs/temporal/time.spec</a></p>
<p dir="auto">Functional scripts:<br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/time/time_1.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/time/time_1.feature</a><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/time/time_2.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/time/time_2.feature</a><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/time/time_3.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/time/time_3.feature</a><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/time/time_4.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/time/time_4.feature</a><br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/time/time_5.feature">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/time/time_5.feature</a></p>
<p dir="auto">Test files:<br>
<a href="https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/tests/CCTC_Components.bUnit.test/TimeTests.cs">https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/tests/CCTC_Components.bUnit.test/TimeTests.cs</a></p>
<p dir="auto"><strong>Pre Review:</strong></p>
<ul class="contains-task-list">
<li class="task-list-item"><input type="checkbox" id="" disabled="" class="task-list-item-checkbox" aria-label="Incomplete task"> Script(s) target the correct feature sufficiently</li>
<li class="task-list-item"><input type="checkbox" id="" disabled="" class="task-list-item-checkbox" aria-label="Incomplete task"> The user requirements are met</li>
</ul>
<p dir="auto"><strong>Post Review:</strong></p>
<ul class="contains-task-list">
<li class="task-list-item"><input type="checkbox" id="" disabled="" class="task-list-item-checkbox" aria-label="Incomplete task"> Script(s) target the correct feature sufficiently</li>
<li class="task-list-item"><input type="checkbox" id="" disabled="" class="task-list-item-checkbox" aria-label="Incomplete task"> Script(s) appropriately test the feature</li>
</ul>
</div>

***

**User specification:** \

<div class="spec-script">
| 
| _https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/user_specs/temporal/time.spec_
| 
| 1 - the time component can receive time input
| 2 - the time component can allow an empty value and an entered value can be optionally cleared
| 3 - the time component time is validated (time format, min and / or max time) and there is feedback provided to the user via an icon
| 4 - the time component can be made read-only and / or disabled. The read-only icon is optional
| 5 - the time component has a placeholder which matches the time format
</div>

**Functional scripts:** \

<div class="spec-script">

_https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/time/time_1.feature_

@component @temporal @time @time_1
Feature: the time component can receive time input

    Scenario: the time component sample page is available
        Given the user is at the home page
        When the user selects the "Time" component in the container "Input"
        Then the url ending is "timesample"

    Scenario: the time component can receive time input
        Given the user is at the home page
        And the user selects the "Time" component in the container "Input"
        When the user enters "141618" into the Time component
        Then the Time component has the value "14:16:18"


_https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/time/time_2.feature_

@component @temporal @time @time_2
Feature: the time component can allow an empty value and an entered value can be optionally cleared
    Scenario: the time component can allow an empty value when allow clear is set to true
        Given the user is at the home page
        And the user selects the "Time" component in the container "Input"
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "Format: default, allow clear, null initial value, FeedbackIcon.Error"
        Then the Time component has the value ""

    Scenario: the time component value can be cleared when allow clear is set to true
        Given the user is at the home page
        And the user selects the "Time" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: default, allow clear, FeedbackIcon.Error"
        When the user enters "" into the Time component
        Then the Time component has the value ""

    Scenario: the time component can allow an empty value when allow clear is set to false
        Given the user is at the home page
        And the user selects the "Time" component in the container "Input"
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "Format: h:mm tt, null initial value, FeedbackIcon.Valid"
        Then the Time component has the value ""

    Scenario: the time component value can not be cleared when allow clear is set to false
        Given the user is at the home page
        And the user selects the "Time" component in the container "Input"
        When the user enters "" into the Time component
        Then the Time component has the value "13:46:22"


_https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/time/time_3.feature_

@component @temporal @time @time_3
Feature: the time component time is validated (time format, min and / or max time) and there is feedback provided to the user via an icon
    Scenario: an incomplete time is shown as an error
        Given the user is at the home page
        And the user selects the "Time" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: HH:mm:ss, FeedbackIcon.Error"
        When the user enters "14151" into the Time component
        Then the Time component has the value "14:15:1"
        And the Time component displays a red exclamation mark feedback icon
        And the Time component image matches the base image "time-error"

    Scenario: a complete time is shown as valid
        Given the user is at the home page
        And the user selects the "Time" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: h:mm tt, null initial value, FeedbackIcon.Valid"
        When the user enters "1213pm" into the Time component
        Then the Time component has the value "12:13 pm"
        And the Time component displays a green tick feedback icon
        And the Time component image matches the base image "time-valid"

    Scenario: a time earlier than the minimum time is shown as an error
        Given the user is at the home page
        And the user selects the "Time" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: h:mm tt, Min (11:05 am) and max (1:10 pm), FeedbackIcon.Both, CssClass applied"
        When the user enters "1104am" into the Time component
        Then the Time component has the value "11:04 am"
        And the Time component displays a red exclamation mark feedback icon

    Scenario: a time later than the maximum time is shown as an error
        Given the user is at the home page
        And the user selects the "Time" component in the container "Input"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: h:mm tt, Min (11:05 am) and max (1:10 pm), FeedbackIcon.Both, CssClass applied"
        When the user enters "1:11pm" into the Time component
        Then the Time component has the value "1:11 pm"
        And the Time component displays a red exclamation mark feedback icon


_https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/time/time_4.feature_

@component @temporal @time @time_4
Feature: the time component can be made read-only and / or disabled. The read-only icon is optional
    Scenario: the time component can be made read-only
        Given the user is at the home page
        And the user selects the "Time" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: h:mm tt, FeedbackIcon.Valid, read-only"
        Then the Time component is not editable
        And the Time component image matches the base image "time-readonly"

    Scenario: the time component can be disabled
        Given the user is at the home page
        And the user selects the "Time" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: h:mm tt, FeedbackIcon.Valid, disabled"
        Then the Time component is disabled
        And the Time component image matches the base image "time-disabled"

    Scenario: the time component can be made read-only and disabled
        Given the user is at the home page
        And the user selects the "Time" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: h:mm tt, FeedbackIcon.Valid, read-only and disabled"
        Then the Time component is not editable
        And the Time component is disabled
        And the Time component image matches the base image "time-readonly-disabled"

    Scenario: the time component can be made read-only without a read-only icon
        Given the user is at the home page
        And the user selects the "Time" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: h:mm tt, FeedbackIcon.Valid, read-only and read-only icon hidden"
        Then the Time component is not editable
        And the Time component image matches the base image "time-readonly-no-icon"

    Scenario: the time component can be made read-only and disabled without a read-only icon
        Given the user is at the home page
        And the user selects the "Time" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: h:mm tt, FeedbackIcon.Valid, read-only, disabled and read-only icon hidden"
        Then the Time component is not editable
        And the Time component is disabled
        And the Time component image matches the base image "time-readonly-disabled-no-icon"


_https://github.com/CCTC-team/CCTC_Components/blob/dp_dev/functional_scripts/features/temporal/time/time_5.feature_

@component @temporal @time @time_5
Feature: the time component has a placeholder which matches the time format
    Scenario: the time component has a placeholder which matches the time format
        Given the user is at the home page
        And the user selects the "Time" component in the container "Input"
        When the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "Format: h:mm tt, null initial value, FeedbackIcon.Valid"
        Then the Time component has the placeholder "h:mm am/pm"
        And the Time component image matches the base image "time-placeholder"
</div>

***

**Comments:** \

<div class="comments">

</div>

***

**Timeline events:** \

<div class="timeline">
<div class="timeline">
LABELLED | *User:* phillidgithub *Created on:* 01/07/2024 09:39:36 *Label name:* Validation and Verification *Label description:* Use this label to identify the issue as relating to the validation and verification of a feature

LABELLED | *User:* phillidgithub *Created on:* 01/07/2024 09:39:36 *Label name:* v1.0.0 *Label description:* Version 1.0.0

ASSIGNED | *User:* phillidgithub *Created on:* 11/07/2024 07:44:05 *Assignee:* phillidgithub
</div>
</div>


<br/>

--------------- feature ends ---------------

<br/>



</body>
</html>